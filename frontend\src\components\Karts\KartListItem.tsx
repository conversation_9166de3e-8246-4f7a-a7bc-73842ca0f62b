// src/components/Karts/KartListItem.tsx
import React from 'react';
import { IonItem, IonLabel } from '@ionic/react';
// Removed unused imports: IonIcon, ribbonOutline, timeOutline
import { Kart } from '../../types';
import { speedLevels, getSpeedBgClass, getLocationDisplay } from './kartUtils';

// Extended Kart interface for apex data
interface ApexKart extends Kart {
    lastLapTimeFormatted?: string;
    bestLapTimeFormatted?: string;
    totalLaps?: number;
}

interface KartListItemProps {
    kart: Kart;
    isProcessing: boolean;
    onClick: (kart: Kart) => void;
}

// Note: The 'react/prop-types' ESLint rule is redundant for TypeScript components
// using interfaces like KartListItemProps. It's recommended to disable this rule
// in your ESLint config for .ts/.tsx files.

export const KartListItem: React.FC<KartListItemProps> = React.memo(({ kart, isProcessing, onClick }) => {
    const locationInfo = getLocationDisplay(kart);
    const speedInfo = speedLevels[kart.speed ?? 4];
    const speedBgClass = getSpeedBgClass(kart.speed);

    // Extract lap times from apex data (if available)
    const lastLapTime = (kart as ApexKart).lastLapTimeFormatted || 'N/A';
    const bestLapTime = (kart as ApexKart).bestLapTimeFormatted || 'N/A';

    return (
        <IonItem
            key={kart._id}
            lines="inset"
            button={true}
            onClick={() => onClick(kart)}
            detail={false}
            disabled={isProcessing}
            className="list-item-no-padding"
        >
            <div className="list-row-content ion-align-items-center ion-padding-vertical">
                {/* Kart Number */}
                <IonLabel style={{ flex: '0 0 60px' }} className="ion-text-center ion-padding-start list-item-highlight">
                    {kart.number}
                </IonLabel>
                {/* Location / Status */}
                <IonLabel style={{ flex: '1 1 auto' }} className="location-col">
                    {locationInfo.isRowColor && locationInfo.rowColorClass ? (
                        <div className="list-item-data-group">
                            <div className={`row-color-indicator ${locationInfo.rowColorClass}`} />
                            <span className="location-text-beside-rect truncate-text list-item-primary-text">{locationInfo.text}</span>
                        </div>
                    ) : (
                        <div className="location-text-list">
                            <span className={`truncate-text list-item-primary-text ${locationInfo.color ? `ion-color ion-color-${locationInfo.color}` : ''}`}>
                                {locationInfo.text}
                            </span>
                        </div>
                    )}
                </IonLabel>
                {/* Speed Indicator */}
                <IonLabel style={{ flex: '0 0 120px' }} className="ion-text-center speed-col">
                    <div className={`speed-indicator ${speedBgClass}`}>
                        <span className="speed-label">{speedInfo.label}</span>
                    </div>
                </IonLabel>
                {/* Lap Times */}
                <IonLabel style={{ flex: '0 0 90px' }} className="ion-text-center hide-sm list-item-secondary-text">
                    <span className={lastLapTime !== 'N/A' ? 'lap-time-value' : ''}
                          style={{
                            fontWeight: lastLapTime !== 'N/A' ? 'bold' : 'normal',
                            color: lastLapTime !== 'N/A' ? 'var(--ion-color-primary)' : 'inherit'
                          }}>
                        {lastLapTime}
                    </span>
                </IonLabel>
                <IonLabel style={{ flex: '0 0 90px' }} className="ion-text-center hide-sm list-item-secondary-text">
                    <span className={bestLapTime !== 'N/A' ? 'lap-time-value best-lap' : ''}
                          style={{
                            fontWeight: bestLapTime !== 'N/A' ? 'bold' : 'normal',
                            color: bestLapTime !== 'N/A' ? 'var(--ion-color-success)' : 'inherit'
                          }}>
                        {bestLapTime}
                    </span>
                </IonLabel>
            </div>
        </IonItem>
    );
});
KartListItem.displayName = 'KartListItem';