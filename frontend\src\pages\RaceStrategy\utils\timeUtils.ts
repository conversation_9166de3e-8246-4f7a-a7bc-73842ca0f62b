/**
 * Consolidated time utilities for race strategy components
 */

// Constants
export const SECONDS_IN_MINUTE = 60;
export const SECONDS_IN_HOUR = 3600;

/**
 * Time format types used throughout the application
 */
export type TimeFormat = 'mm:ss' | 'hh:mm' | 'hh:mm:ss' | 'mm:ss.fff';

/**
 * Formats seconds into a time string based on the specified format
 * @param seconds Total seconds to format
 * @param format The desired output format
 * @returns Formatted time string
 */
export const formatSecondsToTimeString = (seconds: number, format: TimeFormat): string => {
  if (seconds < 0) seconds = 0;
  
  const hours = Math.floor(seconds / SECONDS_IN_HOUR);
  const minutes = Math.floor((seconds % SECONDS_IN_HOUR) / SECONDS_IN_MINUTE);
  const secs = Math.floor(seconds % SECONDS_IN_MINUTE);
  const ms = Math.floor((seconds % 1) * 1000);
  
  switch (format) {
    case 'mm:ss':
      return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
      
    case 'hh:mm':
      return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
      
    case 'hh:mm:ss':
      return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
      
    case 'mm:ss.fff':
      return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}.${ms.toString().padStart(3, '0')}`;
      
    default:
      return seconds.toString();
  }
};

/**
 * Parses a time string into seconds based on the specified format
 * @param timeString Time string to parse (e.g., "01:30" or "02:45:30")
 * @param format The format of the input time string
 * @returns Total seconds
 */
export const parseTimeStringToSeconds = (timeString: string, format: TimeFormat): number => {
  if (!timeString) return 0;
  
  const parts = timeString.split(':');
  
  switch (format) {
    case 'mm:ss':
      { // Wrap declarations in braces
        if (parts.length !== 2) return 0;
        const minutes = parseInt(parts[0], 10) || 0;
        const seconds = parseInt(parts[1], 10) || 0;
        return minutes * SECONDS_IN_MINUTE + seconds;
      }
      
    case 'hh:mm':
      { // Wrap declarations in braces
        if (parts.length !== 2) return 0;
        const hours = parseInt(parts[0], 10) || 0;
        const mins = parseInt(parts[1], 10) || 0;
        return hours * SECONDS_IN_HOUR + mins * SECONDS_IN_MINUTE;
      }
      
    case 'hh:mm:ss':
      { // Wrap declarations in braces
        if (parts.length !== 3) return 0;
        const hrs = parseInt(parts[0], 10) || 0;
        const minutes2 = parseInt(parts[1], 10) || 0;
        const secs = parseInt(parts[2], 10) || 0;
        return hrs * SECONDS_IN_HOUR + minutes2 * SECONDS_IN_MINUTE + secs;
      }
      
    case 'mm:ss.fff':
      { // Wrap declarations in braces
        if (parts.length !== 2) return 0;
        const mins2 = parseInt(parts[0], 10) || 0;
      const secParts = parts[1].split('.');
      const secs2 = parseInt(secParts[0], 10) || 0;
      const milliseconds = secParts.length > 1 ? parseInt(secParts[1].padEnd(3, '0').substring(0, 3), 10) / 1000 : 0;
      return mins2 * SECONDS_IN_MINUTE + secs2 + milliseconds;
      }
    default:
      return parseInt(timeString, 10) || 0;
  }
};

/**
 * Validates if a time string matches the expected format
 * @param timeString Time string to validate
 * @param format Expected format
 * @returns True if valid, false otherwise
 */
export const isValidTimeFormat = (timeString: string, format: TimeFormat): boolean => {
  if (!timeString) return false;
  
  switch (format) {
    case 'mm:ss':
      return /^\d{1,2}:\d{2}$/.test(timeString);
      
    case 'hh:mm':
      return /^\d{1,2}:\d{2}$/.test(timeString);
      
    case 'hh:mm:ss':
      return /^\d{1,2}:\d{2}:\d{2}$/.test(timeString);
      
    case 'mm:ss.fff':
      return /^\d{1,2}:\d{2}\.\d{1,3}$/.test(timeString);
      
    default:
      return false;
  }
};

/**
 * Displays seconds as HH:MM format
 */
export const displaySecondsAsHHMM = (seconds: number): string => {
  return formatSecondsToTimeString(seconds, 'hh:mm');
};

/**
 * Displays seconds as MM:SS format
 */
export const displaySecondsAsMMSS = (seconds: number): string => {
  return formatSecondsToTimeString(seconds, 'mm:ss');
};

/**
 * Displays seconds as HH:MM:SS format
 */
export const displaySecondsAsHHMMSS = (seconds: number): string => {
  return formatSecondsToTimeString(seconds, 'hh:mm:ss');
};

/**
 * Formats a lap time in seconds as a time string (MM:SS.sss)
 */
export const formatLapTime = (seconds: number): string => {
  if (seconds <= 0) return '--:--';
  return formatSecondsToTimeString(seconds, 'mm:ss.fff');
};

/**
 * Formats a race time (seconds elapsed) as a time string with optional sign
 * @param seconds Seconds elapsed in race
 * @param showPositiveSign Whether to show + for positive values
 * @returns Formatted time string (e.g., "+01:30" or "-00:45")
 */
export const formatRaceTime = (seconds: number, showPositiveSign = true): string => {
  const isNegative = seconds < 0;
  const absSeconds = Math.abs(seconds);
  const timeString = formatSecondsToTimeString(absSeconds, 'mm:ss');
  
  if (isNegative) {
    return `-${timeString}`;
  } else if (showPositiveSign && seconds > 0) {
    return `+${timeString}`;
  } else {
    return timeString;
  }
};

/**
 * Formats seconds into a human-readable duration string
 * @param seconds Total seconds
 * @returns Formatted duration string (e.g., "2h 30m" or "45m 20s")
 */
export const formatDuration = (seconds: number): string => {
  if (seconds < 0) seconds = 0;
  
  const hours = Math.floor(seconds / SECONDS_IN_HOUR);
  const minutes = Math.floor((seconds % SECONDS_IN_HOUR) / SECONDS_IN_MINUTE);
  const remainingSeconds = Math.floor(seconds % SECONDS_IN_MINUTE);
  
  let result = '';
  
  if (hours > 0) {
    result += `${hours}h `;
    if (minutes > 0) {
      result += `${minutes}m`;
    }
  } else if (minutes > 0) {
    result += `${minutes}m `;
    if (remainingSeconds > 0) {
      result += `${remainingSeconds}s`;
    }
  } else {
    result = `${remainingSeconds}s`;
  }
  
  return result.trim();
};

/**
 * Converts time from one unit to another
 * @param value Time value to convert
 * @param fromUnit Source unit ('seconds', 'minutes', 'hours')
 * @param toUnit Target unit ('seconds', 'minutes', 'hours')
 * @returns Converted time value
 */
export const convertTime = (
  value: number, 
  fromUnit: 'seconds' | 'minutes' | 'hours', 
  toUnit: 'seconds' | 'minutes' | 'hours'
): number => {
  // Convert to seconds first
  let seconds: number;
  switch (fromUnit) {
    case 'seconds':
      seconds = value;
      break;
    case 'minutes':
      seconds = value * SECONDS_IN_MINUTE;
      break;
    case 'hours':
      seconds = value * SECONDS_IN_HOUR;
      break;
    default:
      seconds = value;
  }
  
  // Convert from seconds to target unit
  switch (toUnit) {
    case 'seconds':
      return seconds;
    case 'minutes':
      return seconds / SECONDS_IN_MINUTE;
    case 'hours':
      return seconds / SECONDS_IN_HOUR;
    default:
      return seconds;
  }
};
