// backend/src/models/Row.ts
import mongoose, { Document, Schema, Types } from 'mongoose';

export interface IRow extends Document {
  _id: Types.ObjectId;
  rowNumber: number; // NEW - e.g., 1, 2, 3, 4
  color: string;
  currentKartId: Types.ObjectId | null;
  pastKarts: Types.ObjectId[];
}

const rowSchema = new Schema<IRow>({
  rowNumber: { type: Number, required: true, unique: true }, // NEW
  color: { type: String, required: true },
  currentKartId: { type: Schema.Types.ObjectId, default: null }, // Remove ref to allow both Kart and ApexKart
  pastKarts: [{ type: Schema.Types.ObjectId }], // Remove ref to allow both Kart and ApexKart
}, { timestamps: true });

export const Row = mongoose.model<IRow>('Row', rowSchema);
