// src/components/Karts/BatchCreateKartsModal.tsx
import React, { useState } from 'react';
import {
    IonModal,
    IonHeader,
    IonToolbar,
    IonTitle,
    IonButtons,
    IonButton,
    IonIcon,
    IonContent,
    IonList,
    IonItem,
    IonLabel,
    IonInput,
    IonNote,
} from '@ionic/react';
import { close } from 'ionicons/icons';

interface BatchCreateKartsModalProps {
    isOpen: boolean;
    onDidDismiss: () => void;
    onBatchCreate: (start: number, end: number) => Promise<boolean>; // Returns true on success
    isMutating: boolean; // Use a specific prop like isBatchCreating if needed
}

export const BatchCreateKartsModal: React.FC<BatchCreateKartsModalProps> = ({ isOpen, onDidDismiss, onBatchCreate, isMutating }) => {
    const [batchStartNumber, setBatchStartNumber] = useState<string>('');
    const [batchEndNumber, setBatchEndNumber] = useState<string>('');

    const handleInternalBatchCreate = async () => {
        const startNum = parseInt(batchStartNumber, 10);
        const endNum = parseInt(batchEndNumber, 10);
        // Rely on parent for validation toast
        if (isNaN(startNum) || isNaN(endNum) || startNum <= 0 || endNum <= 0 || startNum > endNum || (endNum - startNum + 1 > 100)) {
            return;
        }
        const success = await onBatchCreate(startNum, endNum);
        if (success) {
            setBatchStartNumber('');
            setBatchEndNumber('');
            onDidDismiss();
        }
    };

    const handleDismiss = () => {
        setBatchStartNumber('');
        setBatchEndNumber('');
        onDidDismiss();
    };

    return (
        <IonModal isOpen={isOpen} onDidDismiss={handleDismiss}>
            <IonHeader>
                <IonToolbar>
                    <IonTitle>Batch Create Karts</IonTitle>
                    <IonButtons slot="end">
                        <IonButton onClick={handleDismiss} disabled={isMutating} fill="clear">
                            <IonIcon slot="icon-only" icon={close} />
                        </IonButton>
                    </IonButtons>
                </IonToolbar>
            </IonHeader>
            <IonContent className="ion-padding">
                <IonList lines="none">
                    <IonItem>
                        <IonLabel position="stacked">Start Number *</IonLabel>
                        <IonInput type="number" value={batchStartNumber} onIonChange={e => setBatchStartNumber(e.detail.value!)} placeholder="e.g., 1" required min="1" disabled={isMutating} clearInput />
                    </IonItem>
                    <IonItem>
                        <IonLabel position="stacked">End Number *</IonLabel>
                        <IonInput type="number" value={batchEndNumber} onIonChange={e => setBatchEndNumber(e.detail.value!)} placeholder="e.g., 60" required min="1" disabled={isMutating} clearInput />
                    </IonItem>
                </IonList>
                <IonNote color="medium" style={{ display: 'block', marginTop: '0.5rem', marginBottom: '1rem' }}>
                    Creates karts sequentially from Start to End number. Existing numbers will be skipped. Max 100.
                </IonNote>
                <IonButton expand="block" onClick={handleInternalBatchCreate} disabled={isMutating || !batchStartNumber || !batchEndNumber || parseInt(batchStartNumber, 10) <= 0 || parseInt(batchEndNumber, 10) <= 0 || parseInt(batchStartNumber, 10) > parseInt(batchEndNumber, 10)}>
                    {isMutating ? 'Creating Karts...' : 'Create Karts in Range'}
                </IonButton>
            </IonContent>
        </IonModal>
    );
};