// d:\Desktop\ManulilacRaceplanner\frontend\src\components\Karts\KartStatusList.tsx
/* eslint-disable react/prop-types */
/* eslint-disable no-case-declarations */
import React, { useState, useEffect, useCallback, useMemo } from 'react';
import {
    IonHeader,
    IonToolbar,
    IonTitle,
    IonContent,
    IonLabel,
    IonLoading,
    IonText,
    IonRefresher,
    IonRefresherContent,
    RefresherEventDetail,
    IonButtons,
    IonButton,
    IonIcon, // Keep
    IonToast,
    IonItem,
    IonList, // Keep IonList
    IonAlert,
    // IonNote, // Removed unused
    IonSearchbar,
    IonMenuButton,
} from '@ionic/react';
// import { useNavigate as useHistory } from 'react-router-dom'; // Removed if not used
import { // Removed close, trashOutline
    add,
    searchCircleOutline,
    listCircleOutline,
    refreshCircleOutline,
    duplicateOutline,
    closeCircleOutline, // <-- Add this import
} from 'ionicons/icons';
import apiService from '../../services/apiService';
import { Kart, Team } from '../../types'; // Import Team type
import useWebSocket from '../../hooks/webSocketHandler'; // <-- Import the WebSocket hook
import { useDataContext } from '../../context/DataContext';
import { getLocationDisplay } from './kartUtils'; // Import utils, removed getSpeedBgClass and speedLevels
import { KartListItem } from './KartListItem'; // Ensure paths are correct
import { CreateKartModal } from './CreateKartModal';
import { BatchCreateKartsModal } from './BatchCreateKartsModal';
import { KartInfoModal } from './KartInfoModal';
// Import global styles
import '../../themes/speed-styles.css';
import '../../themes/list-styles.css';
import '../../themes/row-styles.css'; // <-- Import row styles if needed for location display

// WebSocket Event Names
const KART_UPDATE_EVENT = "kartsUpdated";
const TEAM_UPDATE_EVENT = "teamsUpdated"; // Listen for team changes (affects location)
const ROW_UPDATE_EVENT = "rowsUpdated";   // Listen for row changes (affects location)

const KartStatusList: React.FC = () => {
    // --- Data Context ---
    const {
        useApexDatabase,
        selectedSession,
        selectedTeam,
        availableTeams,
        isLoadingTeams,
        refreshTeams,
        error: contextError
    } = useDataContext();

    // --- State variables ---
    const [karts, setKarts] = useState<Kart[]>([]);
    const [isLoading, setIsLoading] = useState<boolean>(true);
    const [isMutating, setIsMutating] = useState<boolean>(false); // General mutation flag
    const [isBatchCreating, setIsBatchCreating] = useState<boolean>(false); // Specific flag for batch create
    const [error, setError] = useState<string | null>(null);
    const [toastMessage, setToastMessage] = useState<string | null>(null);
    const [toastColor, setToastColor] = useState<"success" | "danger" | "warning" | "medium">("medium");
    const [showCreateModal, setShowCreateModal] = useState<boolean>(false);
    const [showResetSpeedConfirm, setShowResetSpeedConfirm] = useState(false);
    const [showKartInfoModal, setShowKartInfoModal] = useState(false);
    const [selectedKartForAction, setSelectedKartForAction] = useState<Kart | null>(null);
    const [showDeleteKartConfirm, setShowDeleteKartConfirm] = useState(false);
    const [showBatchCreateModal, setShowBatchCreateModal] = useState(false);
    const [searchText, setSearchText] = useState<string>('');
    const [sortOrder, setSortOrder] = useState<'number' | 'status'>('number'); // Default sort

    // --- Helper: Show Toast ---
    const showToast = useCallback((message: string, color: "success" | "danger" | "warning" | "medium") => {
        setToastMessage(message);
        setToastColor(color);
    }, []);

    // --- Fetching Logic ---
    const fetchData = useCallback(async (showLoading = true) => {
        if (showLoading) setIsLoading(true);
        setError(null);

        console.log('[KartStatusList] ---> Fetching karts...', useApexDatabase ? 'APEX MODE' : 'REGULAR MODE');
        try {
            // Smart API automatically redirects to apex when needed
            const kartsData = await apiService.karts.getAll('currentTeamId,currentRowId', useApexDatabase, selectedSession?._id);
            setKarts(kartsData || []);
            console.log('[KartStatusList] Karts fetch successful:', kartsData?.length || 0, 'karts');
        } catch (err) {
            console.error("[KartStatusList] Error fetching karts:", err);
            const message = err instanceof Error ? err.message : "An unknown error occurred";
            setError(`Failed to load karts: ${message}`);
            showToast(`Error: ${message}`, 'danger');
        }

        if (showLoading) setIsLoading(false);
    }, [showToast, useApexDatabase, selectedSession]); // Include dependencies

    // --- WebSocket Handler ---
    const handleWebSocketMessage = useCallback((message: { event: string; payload?: unknown }) => {
        console.log('[KartStatusList] <<< WebSocket message received:', message); // Log message received
        const { event } = message;
        // Refetch if any relevant data changes
        if (event === KART_UPDATE_EVENT || event === TEAM_UPDATE_EVENT || event === ROW_UPDATE_EVENT) {
            console.log(`[KartStatusList] ---> Triggering refetch due to ${event} event...`); // Log refetch trigger
            fetchData(false); // Fetch without showing main loading spinner
        }
    }, [fetchData]); // Include fetchData in dependencies

    useWebSocket(handleWebSocketMessage); // Use the hook

    // --- Initial Data Load ---
    useEffect(() => {
        fetchData();
    }, [fetchData]); // Fetch data on component mount

    // --- Pull-to-Refresh Handler ---
    const handleRefresh = async (event: CustomEvent<RefresherEventDetail>) => {
        await fetchData(false); // Fetch data without showing main loading spinner
        event.detail.complete();
    };

    // --- API Call Handlers ---
    // Modified to be passed to CreateKartModal
    const handleCreateKart = async (kartNum: number): Promise<boolean> => {
        if (isNaN(kartNum) || kartNum <= 0) {
            showToast("Please enter a valid Kart Number.", "warning");
            return false; // Return false on validation failure
        }
        setIsMutating(true);
        let success = false;
        try {
            const response = await apiService.karts.create({ number: kartNum, status: 'available', speed: 4 });
            showToast(`Kart #${response.number} created successfully!`, "success");
            success = true;
            // Modal closing handled by component on success
        } catch (err) {
            console.error("Error creating kart:", err);
            const message = err instanceof Error ? err.message : "Failed to create kart.";
            showToast(`Error: ${message}`, "danger");
        } finally { setIsMutating(false); }
        return success; // Return success status
    };

    // Modified to be passed to BatchCreateKartsModal
    const handleBatchCreateKarts = async (startNum: number, endNum: number): Promise<boolean> => {
        if (isNaN(startNum) || isNaN(endNum) || startNum <= 0 || endNum <= 0 || startNum > endNum || (endNum - startNum + 1 > 100)) {
            showToast("Invalid range or too many karts requested (max 100).", "warning");
            return false; // Return false on validation failure
        }
        setIsBatchCreating(true);
        let success = false; // Initialize success
        try {
            const response = await apiService.admin.batchCreateKarts({ startNumber: startNum, endNumber: endNum }); // response is the data directly
            showToast(response.message || `Batch creation complete.`, "success"); // Access message directly
            success = true;
            // Modal closing handled by component on success
        } catch (err) {
            console.error("Error batch creating karts:", err);
            const message = err instanceof Error ? err.message : "Failed to batch create karts.";
            showToast(`Error: ${message}`, "danger");
        } finally { setIsBatchCreating(false); }
        return success; // Return success status
    };

    // Modified to be passed to KartInfoModal (for speed)
    const handleUpdateKartSpeed = async (kartId: string, speed: number): Promise<boolean> => {
        setIsMutating(true); // Use global mutation flag or specific one if needed
        let success = false;
        try {
            const response = await apiService.karts.update(kartId, { speed }, useApexDatabase, selectedSession?._id);
            showToast(`Kart #${response.number} speed updated.`, "success");
            success = true;
            closeInfoModal(); // Close modal on success
        } catch (err) {
            console.error("Error updating kart speed:", err);
            const message = err instanceof Error ? err.message : "Failed to update kart speed.";
            showToast(`Error: ${message}`, "danger");
        } finally { setIsMutating(false); } // Don't close modal on error here, let user retry or close manually
        return success; // Return success status
    };

    const handleResetAllSpeeds = async () => {
        setIsMutating(true);
        try {
            const response = await apiService.admin.resetKartSpeeds(); // Corrected to use adminApi and full method name
            showToast(response.message || 'All kart speeds reset to Unknown.', "success"); // Access message directly
            // REMOVED: await fetchData(false); // WebSocket will trigger refetch
        } catch (err) {
            console.error("Error resetting speeds:", err);
            const message = err instanceof Error ? err.message : "Failed to reset speeds.";
            showToast(`Error: ${message}`, "danger");
        } finally { setIsMutating(false); setShowResetSpeedConfirm(false); }
    };

    const handleDeleteKart = async () => {
        if (!selectedKartForAction) return;
        const kartToDelete = selectedKartForAction; // Capture kart before closing modal/alert
        setShowDeleteKartConfirm(false); // Close confirm alert
        closeInfoModal(); // Close info modal if open
        setIsMutating(true);
        try {
            await apiService.karts.delete(kartToDelete._id);
            showToast(`Kart #${kartToDelete.number} deleted successfully!`, "success");
        } catch (err) {
            console.error("Error deleting kart:", err);
            const message = err instanceof Error ? err.message : "Failed to delete kart.";
            showToast(`Error: ${message}`, "danger");
        } finally {
            setIsMutating(false);
            setSelectedKartForAction(null); // Clear selection
        }
    };

    // --- Modal Triggers ---
    const openKartInfoModal = (kart: Kart) => {
        setSelectedKartForAction(kart);
        setShowKartInfoModal(true);
    };

    const closeInfoModal = () => {
        setShowKartInfoModal(false);
        // Delay clearing selection slightly to allow modal animation
        setSelectedKartForAction(null);
    };

    // Trigger for delete confirmation from the info modal
    const triggerDeleteKartConfirm = (kart: Kart) => {
        setSelectedKartForAction(kart); // Set kart to delete
        setShowDeleteKartConfirm(true);
    };

    // --- Filtering Logic ---
    // Ensure filtering uses populated data correctly
    const filteredKarts = useMemo(() => {
        if (!karts) return [];
        const sorted = [...karts]; // Create a copy to sort

        // Sort
        if (sortOrder === 'number') {
            sorted.sort((a, b) => a.number - b.number);
        } else if (sortOrder === 'status') {
            // Example status sort order (adjust as needed)
            const statusOrder = ['on_track', 'in_pit_row', 'available', 'maintenance', 'retired', 'unknown'];
            sorted.sort((a, b) => {
                const statusA = a.status ?? 'unknown';
                const statusB = b.status ?? 'unknown';
                return statusOrder.indexOf(statusA) - statusOrder.indexOf(statusB);
            });
        }

        // Filter by search text
        const lowerSearchText = searchText.toLowerCase().trim();
        if (!lowerSearchText) return sorted;

        return sorted.filter(kart => {
            const team = kart.currentTeamId as Team | null;
            const teamName = team?.name?.toLowerCase();
            const teamNumber = team?.number?.toString();
            const locationText = getLocationDisplay(kart).text.toLowerCase();

            return (
                kart.number.toString().includes(lowerSearchText) ||
                locationText.includes(lowerSearchText) ||
                (teamName && teamName.includes(lowerSearchText)) ||
                (teamNumber && teamNumber.includes(lowerSearchText))
            );
        });
    }, [karts, searchText, sortOrder]);

    // --- Combined Loading State ---
    const isProcessing = isLoading || isMutating || isBatchCreating;
    const hasKarts = karts && karts.length > 0;

    // --- Render ---
    return (
        <>
            {/* --- Header --- */}
            <IonHeader>
                <IonToolbar>
                    <IonButtons slot="start">
                        <IonMenuButton />
                    </IonButtons>
                    <IonTitle>Kart Status</IonTitle>
                    <IonButtons slot="end">
                        <IonButton onClick={() => setShowCreateModal(true)} disabled={isProcessing} title="Add Kart">
                            <IonIcon slot="icon-only" icon={add} />
                        </IonButton>
                        <IonButton onClick={() => setShowBatchCreateModal(true)} disabled={isProcessing} title="Batch Create Karts">
                            <IonIcon slot="icon-only" icon={duplicateOutline} />
                        </IonButton>
                        <IonButton onClick={() => setShowResetSpeedConfirm(true)} disabled={isProcessing || !hasKarts} title="Reset All Speeds">
                            <IonIcon slot="icon-only" icon={refreshCircleOutline} color="warning" />
                        </IonButton>
                        {/* Add Sort/Filter buttons if needed */}
                        {/* <IonButton disabled={isProcessing} title="Sort/Filter">
                            <IonIcon slot="icon-only" icon={filterOutline} />
                        </IonButton> */}
                    </IonButtons>
                </IonToolbar>
                <IonToolbar>
                    <IonSearchbar
                        value={searchText}
                        onIonInput={(e) => setSearchText(e.detail.value ?? "")}
                        placeholder="Search #, Location, Team"
                        animated={true}
                        debounce={300}
                        disabled={isLoading}
                    />
                </IonToolbar>
            </IonHeader>

            <IonContent>
                {/* --- Loading, Refresher, Toast, Error --- */}
                <IonLoading isOpen={isLoading && !hasKarts} message={'Loading Karts...'} />
                <IonLoading isOpen={isMutating} message={'Updating...'} />
                <IonLoading isOpen={isBatchCreating} message={'Batch Creating Karts...'} />
                <IonRefresher slot="fixed" onIonRefresh={handleRefresh} disabled={isProcessing}>
                    <IonRefresherContent />
                </IonRefresher>
                <IonToast
                    isOpen={!!toastMessage}
                    message={toastMessage || ''}
                    duration={2000} // Increased duration slightly
                    onDidDismiss={() => setToastMessage(null)}
                    color={toastColor}
                    position="bottom"
                    buttons={[{ icon: 'close', role: 'cancel' }]}
                />
                {error && !isLoading && !isProcessing && (
                    <div className="ion-padding ion-text-center no-data-message">
                        <IonIcon icon={closeCircleOutline} size="large" color="danger" />
                        <IonText color="danger"><h5>Error Loading Karts</h5><p>{error}</p></IonText>
                        <IonButton onClick={() => fetchData()} fill="outline" disabled={isProcessing}>Retry</IonButton>
                    </div>
                )}

                {/* --- Kart List --- */}
                {!isLoading && !error && (
                    <>
                        {filteredKarts.length > 0 ? (
                            <IonList lines="none" className="data-list">
                                {/* Header Row */}
                                <IonItem lines="full" color="light" className="list-header-sticky list-item-no-padding">
                                    <div className="list-row-content ion-align-items-center">
                                        <IonLabel style={{ flex: '0 0 60px' }} className="ion-text-center ion-padding-start list-header-label">#</IonLabel>
                                        <IonLabel style={{ flex: '1 1 auto' }} className="list-header-label">Location</IonLabel>
                                        <IonLabel style={{ flex: '0 0 120px' }} className="ion-text-center list-header-label">Speed</IonLabel>
                                        <IonLabel style={{ flex: '0 0 90px' }} className="ion-text-center hide-sm list-header-label">Last Lap</IonLabel>
                                        <IonLabel style={{ flex: '0 0 90px' }} className="ion-text-center hide-sm list-header-label">Best Lap</IonLabel>
                                    </div>
                                </IonItem>

                                {/* Kart Rows */}
                                {filteredKarts.map((kart) => {
                                    return (
                                        <KartListItem
                                            key={kart._id}
                                            kart={kart}
                                            isProcessing={isProcessing}
                                            onClick={openKartInfoModal}
                                        />
                                    );
                                })}
                            </IonList>
                        ) : (
                            /* Empty State */
                            <div className="ion-padding ion-text-center no-data-message">
                                <IonIcon icon={searchText ? searchCircleOutline : listCircleOutline} size="large" color="medium" />
                                <IonText color="medium">
                                    <h5>{searchText ? "No karts match search" : "No Karts Found"}</h5>
                                    <p>{searchText ? "Clear search to see all karts." : "Use '+' to add a kart."}</p>
                                </IonText>
                                {!searchText && <IonButton onClick={() => setShowCreateModal(true)} fill="clear" disabled={isProcessing}>Add Kart</IonButton>}
                            </div>
                        )}
                    </>
                )}

                {/* --- Modals & Alerts --- */}
                {/* Create Kart Modal */}
                <CreateKartModal
                    isOpen={showCreateModal}
                    onDidDismiss={() => setShowCreateModal(false)}
                    onCreateKart={handleCreateKart}
                    isMutating={isMutating}
                />

                {/* Batch Create Karts Modal */}
                <BatchCreateKartsModal
                    isOpen={showBatchCreateModal}
                    onDidDismiss={() => setShowBatchCreateModal(false)}
                    onBatchCreate={handleBatchCreateKarts}
                    isMutating={isBatchCreating} // Pass specific flag
                />

                {/* Kart Info Modal */}
                <KartInfoModal
                    isOpen={showKartInfoModal}
                    onDidDismiss={closeInfoModal}
                    kart={selectedKartForAction}
                    isProcessing={isProcessing} // Pass combined processing state
                    onSpeedSelect={handleUpdateKartSpeed}
                    onDelete={triggerDeleteKartConfirm}
                />

                {/* Reset All Speeds Confirmation Alert */}
                <IonAlert
                    isOpen={showResetSpeedConfirm}
                    onDidDismiss={() => setShowResetSpeedConfirm(false)}
                    header={'Confirm Reset'}
                    message={'Are you sure you want to reset ALL kart speeds to Unknown?'}
                    buttons={[
                        { text: 'Cancel', role: 'cancel' },
                        { text: 'Reset All', role: 'destructive', handler: handleResetAllSpeeds }
                    ]}
                />

                {/* Delete Kart Confirmation Alert */}
                <IonAlert
                    isOpen={showDeleteKartConfirm}
                    onDidDismiss={() => setShowDeleteKartConfirm(false)}
                    header={'Confirm Delete'}
                    message={`Are you sure you want to delete Kart #${selectedKartForAction?.number}? This cannot be undone.`}
                    buttons={[
                        { text: 'Cancel', role: 'cancel' },
                        { text: 'Delete', role: 'destructive', handler: handleDeleteKart }
                    ]}
                />

            </IonContent>
        </>
    );
};

export default KartStatusList;
