// src/components/Karts/KartInfoModal.tsx
import React, { useState, useEffect } from 'react'; // Import useState and useEffect
import {
    IonModal,
    IonHeader,
    IonToolbar,
    IonTitle,
    IonButtons,
    IonButton,
    IonIcon,
    IonContent,
    IonCard,
    IonCardHeader,
    IonCardTitle,
    IonCardContent,
    IonList,
    IonItem,
    IonLabel,
    IonNote,
    IonSpinner, // Keep spinner if needed for local actions
    IonBadge,
} from '@ionic/react';
import { close, trashOutline, timeOutline } from 'ionicons/icons';
import { Kart } from '../../types';
import { speedLevels, getSpeedBgClass, getLocationDisplay } from './kartUtils';
import apiService from '../../services/apiService';
import { useDataContext } from '../../context/DataContext';

interface LapTime {
    _id: string;
    lapNumber: number;
    lapTime: number;
    lapTimeFormatted: string;
    timestamp: string;
}

// Extended Kart interface for apex data
interface ApexKart extends Kart {
    lastLapTimeFormatted?: string;
    bestLapTimeFormatted?: string;
    totalLaps?: number;
}

interface KartInfoModalProps {
    isOpen: boolean;
    onDidDismiss: () => void;
    kart: Kart | null;
    isProcessing: boolean; // Global processing state
    onSpeedSelect: (kartId: string, speed: number) => Promise<boolean>; // Returns true on success
    onDelete: (kart: Kart) => void; // Trigger delete confirmation
}

export const KartInfoModal: React.FC<KartInfoModalProps> = ({
    isOpen,
    onDidDismiss,
    kart,
    isProcessing,
    onSpeedSelect,
    onDelete,
}) => {
    const [isMutatingSpeed, setIsMutatingSpeed] = useState(false);
    const [lapTimes, setLapTimes] = useState<LapTime[]>([]);
    const [loadingLapTimes, setLoadingLapTimes] = useState(false);
    const { useApexDatabase, selectedSessionId } = useDataContext();

    // Fetch lap times when modal opens and kart changes
    useEffect(() => {
        if (isOpen && kart && useApexDatabase && selectedSessionId) {
            fetchLapTimes();
        } else {
            setLapTimes([]);
        }
    }, [isOpen, kart, useApexDatabase, selectedSessionId]);

    const fetchLapTimes = async () => {
        if (!kart || !selectedSessionId) return;

        setLoadingLapTimes(true);
        try {
            // Fetch lap times for this kart from apex database
            const response = await apiService.get(`/apex/sessions/${selectedSessionId}/karts/${kart.number}/laps`);
            setLapTimes(response.data || []);
        } catch (error) {
            console.error('Error fetching lap times:', error);
            setLapTimes([]);
        } finally {
            setLoadingLapTimes(false);
        }
    };

    const handleInternalSpeedSelect = async (speedValue: number) => {
        if (!kart || speedValue === (kart.speed ?? 4)) return;
        setIsMutatingSpeed(true);
        await onSpeedSelect(kart._id, speedValue);
        setIsMutatingSpeed(false);
        // Modal closing is handled by parent via onSpeedSelect success/failure
    };

    const handleTriggerDelete = () => {
        if (kart) onDelete(kart);
    };

    const isBusy = isProcessing || isMutatingSpeed;

    return (
        <IonModal isOpen={isOpen} onDidDismiss={onDidDismiss}>
            <IonHeader>
                <IonToolbar>
                    <IonTitle>Kart #{kart?.number} Info</IonTitle>
                    <IonButtons slot="end">
                        <IonButton onClick={handleTriggerDelete} disabled={isBusy} color="danger" fill="clear" title={`Delete Kart #${kart?.number}`}>
                            <IonIcon slot="icon-only" icon={trashOutline} />
                        </IonButton>
                        <IonButton onClick={onDidDismiss} disabled={isBusy} fill="clear" title="Close">
                            <IonIcon slot="icon-only" icon={close} />
                        </IonButton>
                    </IonButtons>
                </IonToolbar>
            </IonHeader>
            <IonContent className="ion-padding">
                {!kart ? (
                    <div className="ion-text-center ion-padding"><IonSpinner name="crescent" /><p>Loading Kart Data...</p></div>
                ) : (
                    <>
                        <IonCard>
                            <IonCardHeader><IonCardTitle>Details</IonCardTitle></IonCardHeader>
                            <IonCardContent>
                                <IonList lines="none">
                                    <IonItem><IonLabel>Number</IonLabel><IonNote slot="end">{kart.number}</IonNote></IonItem>
                                    <IonItem><IonLabel>Location</IonLabel><IonNote slot="end">{getLocationDisplay(kart).text}</IonNote></IonItem>
                                    {kart.tablet && (<IonItem><IonLabel>Tablet</IonLabel><IonNote slot="end">{kart.tablet}</IonNote></IonItem>)}
                                    {/* Show apex lap time data if available */}
                                    {useApexDatabase && (kart as ApexKart).lastLapTimeFormatted && (
                                        <IonItem><IonLabel>Last Lap</IonLabel><IonNote slot="end">{(kart as ApexKart).lastLapTimeFormatted}</IonNote></IonItem>
                                    )}
                                    {useApexDatabase && (kart as ApexKart).bestLapTimeFormatted && (
                                        <IonItem><IonLabel>Best Lap</IonLabel><IonNote slot="end">{(kart as ApexKart).bestLapTimeFormatted}</IonNote></IonItem>
                                    )}
                                    {useApexDatabase && (kart as ApexKart).totalLaps !== undefined && (
                                        <IonItem><IonLabel>Total Laps</IonLabel><IonNote slot="end">{(kart as ApexKart).totalLaps}</IonNote></IonItem>
                                    )}
                                </IonList>
                            </IonCardContent>
                        </IonCard>
                        <IonCard>
                            <IonCardHeader><IonCardTitle>Set Speed</IonCardTitle></IonCardHeader>
                            <IonCardContent>
                                <div className="speed-button-container">
                                    {Object.values(speedLevels).map(level => {
                                        const speedBgClass = getSpeedBgClass(level.value);
                                        const isCurrentSpeed = (kart?.speed ?? 4) === level.value;
                                        return (
                                            <IonButton key={level.value} expand="block" className={`speed-button ${speedBgClass} ${isCurrentSpeed ? 'current-speed' : ''}`} onClick={() => handleInternalSpeedSelect(level.value)} disabled={isBusy || isCurrentSpeed}>
                                                {level.label}
                                            </IonButton>
                                        );
                                    })}
                                </div>
                            </IonCardContent>
                        </IonCard>

                        {/* Past Lap Times Card - Only show in apex mode */}
                        {useApexDatabase && (
                            <IonCard>
                                <IonCardHeader>
                                    <IonCardTitle>
                                        <IonIcon icon={timeOutline} style={{ marginRight: '8px' }} />
                                        Past Lap Times
                                        {lapTimes.length > 0 && (
                                            <IonBadge color="primary" style={{ marginLeft: '8px' }}>
                                                {lapTimes.length}
                                            </IonBadge>
                                        )}
                                    </IonCardTitle>
                                </IonCardHeader>
                                <IonCardContent>
                                    {loadingLapTimes ? (
                                        <div className="ion-text-center ion-padding">
                                            <IonSpinner name="crescent" />
                                            <p>Loading lap times...</p>
                                        </div>
                                    ) : lapTimes.length > 0 ? (
                                        <IonList lines="inset">
                                            {lapTimes.slice(0, 10).map((lap) => (
                                                <IonItem key={lap._id}>
                                                    <IonLabel>
                                                        <h3>Lap {lap.lapNumber}</h3>
                                                        <p>{new Date(lap.timestamp).toLocaleTimeString()}</p>
                                                    </IonLabel>
                                                    <IonNote slot="end" color="primary">
                                                        {lap.lapTimeFormatted}
                                                    </IonNote>
                                                </IonItem>
                                            ))}
                                            {lapTimes.length > 10 && (
                                                <IonItem>
                                                    <IonLabel color="medium">
                                                        <p>... and {lapTimes.length - 10} more laps</p>
                                                    </IonLabel>
                                                </IonItem>
                                            )}
                                        </IonList>
                                    ) : (
                                        <div className="ion-text-center ion-padding">
                                            <p color="medium">No lap times recorded yet</p>
                                        </div>
                                    )}
                                </IonCardContent>
                            </IonCard>
                        )}
                    </>
                )}
            </IonContent>
        </IonModal>
    );
};