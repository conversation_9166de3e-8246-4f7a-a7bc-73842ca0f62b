# Apex Parser Documentation

This directory contains the Apex race timing parser implementation that can process websocket messages and log files from the Apex timing system.

## Overview

The Apex parser is designed to handle race timing data from the Apex timing system, creating a comprehensive database of race sessions, teams, karts, drivers, lap times, and pit stops.

## Files in this Directory

- `messages.txt` - Sample log file with race timing data
- `messages_soleluna_9_11_24.txt` - Another sample log file
- `webSocketParser.ts` - Original React-based websocket parser (reference)
- `parseGrid.ts` - Original grid parsing logic (reference)
- `apexParserService.js` - Original AngularJS service (reference)
- `raceService.js` - Original race service with data models (reference)
- `raceCtrl.js` - Original race controller (reference)

## How to Use the Parser

### 1. Via API Endpoints

The parser can be used through the following API endpoints:

#### Parse Log File
```bash
POST /api/apex/parse-log-file
{
  "filePath": "messages.txt",
  "raceId": "optional-race-id",
  "sessionId": "optional-session-id"
}
```

#### Parse Custom Log Content
```bash
POST /api/apex/parse-log
{
  "logContent": "log content here...",
  "raceId": "optional-race-id",
  "sessionId": "optional-session-id"
}
```

#### Get Sessions
```bash
GET /api/apex/sessions
```

#### Get Session Statistics
```bash
GET /api/apex/sessions/{sessionId}/stats
```

### 2. Via Frontend Interface

Navigate to `/apex-parser` in the frontend application to access the user interface for:

- Selecting and parsing log files
- Entering custom log content
- Viewing parsed sessions
- Monitoring session statistics
- Managing race data

### 3. Via Test Script

Run the test script to parse the sample log file:

```bash
cd backend
npm run ts-node src/scripts/testApexParser.ts
```

## Data Structure

The parser creates the following database collections:

### apex_sessions
- Session metadata (race name, track, start time)
- Grid data and session configuration
- Active/inactive status

### apex_teams
- Team information extracted from grid data
- Driver lists and nationality
- Kart number assignments

### apex_karts
- Kart numbers and team assignments
- Current driver information
- Active status

### apex_competitors
- Maps websocket competitor IDs to team IDs
- Links race data to database entities

### apex_laps
- Individual lap times and sector data
- Best lap and personal best tracking
- Timestamp information

### apex_pitstops
- Pit entry and exit times
- Pit duration calculations
- Lap number references

### apex_livedata
- Real-time race positions
- Gap and interval information
- Current sector times and status

## Log File Format

The parser expects log files in the following format:

```
[timestamp] field|type|value
field2|type2|value2
field3|type3|value3
```

### Key Message Types

- `init` - Creates a new race session
- `grid` - Contains HTML grid data with participant information
- `r{id}c{column}` - Driver/competitor data updates
- Session fields: `title1`, `title2`, `track`, `dyn1`, etc.

### Example Log Entry

```
[2024-11-09T13:53:15.141Z] init|r|
title1||EKO 4H of Serres
title2||Final
track||Karting Track (1200m)
grid||<tbody><tr data-id="r0"...
r17749c9|tn|1:10.062
r17749c13|in|149
```

## Configuration

The parser can be configured with:

- `sessionId` - Custom session identifier
- `raceId` - Race identifier for grouping
- `enableLogging` - Enable/disable console logging

## Database Prefixes

All Apex parser collections use the `apex_` prefix to separate them from user input data:

- `apex_sessions`
- `apex_teams`
- `apex_karts`
- `apex_drivers`
- `apex_competitors`
- `apex_laps`
- `apex_pitstops`
- `apex_livedata`

This allows the application to support both websocket parsing mode and user input mode simultaneously.

## Error Handling

The parser includes comprehensive error handling for:

- Invalid log file formats
- Missing grid data
- Malformed websocket messages
- Database connection issues
- Type validation errors

## Performance Considerations

- Large log files are processed incrementally
- Database operations are batched where possible
- Indexes are created for optimal query performance
- Memory usage is monitored during parsing

## Troubleshooting

### Common Issues

1. **"Cannot find module" errors**: Ensure all dependencies are installed
2. **Database connection errors**: Check MongoDB connection string
3. **Parsing errors**: Verify log file format matches expected structure
4. **Memory issues**: Process large files in smaller chunks

### Debug Mode

Enable debug logging by setting `enableLogging: true` in the parser configuration.

## Integration with Race Strategy

The Apex parser is designed to work alongside the existing race strategy system:

- Websocket mode uses `apex_` prefixed collections
- User input mode uses standard collections
- Both modes can coexist in the same database
- Session selection determines which data source to use

This allows teams to switch between live timing data and manual strategy planning as needed.
