# RaceService Alignment and Pit Stop Improvements

## Overview

This document outlines the comprehensive changes made to align `apexParserSimple.ts` with the correct data parsing logic from `raceService.js` and implement the requested pit stop improvements.

## Issues Identified and Fixed

### ✅ **1. Field Type Mapping Mismatch**

**Problem**: `apexParserSimple.ts` was using incorrect field type mappings compared to `raceService.js`

**Root Cause**: The apex parser was checking field names (`llp`, `blp`) instead of data types (`tn`, `ti`, `ib`)

**Solution**: Updated field type detection to match `raceService.js` logic:

```typescript
// OLD (Incorrect)
case 'llp': // Last lap time
case 'blp': // Best lap time
  await this.handleLapTimeUpdate(competitorId, fieldType, data.value);
  break;

// NEW (Correct - following raceService.js)
case 'llp': // Last lap time column
case 'blp': // Best lap time column
  // Check the data type to determine what kind of lap time this is
  await this.handleLapTimeByType(competitorId, data.type, data.value, fieldType);
  break;
```

### ✅ **2. Data Type Based Processing**

**Problem**: Not using `data.type` to determine the actual field meaning

**Solution**: Implemented data type based processing following `raceService.js`:

```typescript
/**
 * Handle lap time updates based on data type (following raceService.js logic)
 */
private async handleLapTimeByType(websocketId: string, dataType: string, lapTime: string, fieldType: string): Promise<void> {
  const dataTypeLower = dataType.toLowerCase();
  
  // Based on raceService.js logic:
  // ti = personal best time, tn = normal time, tb = best time in race, ib = best lap
  if ((dataTypeLower === 'ti' || dataTypeLower === 'tn' || dataTypeLower === 'tb') && fieldType === 'llp') {
    // Last lap time
    await this.handleLapTimeUpdate(websocketId, dataType, lapTime);
  } else if ((dataTypeLower === 'ti' || dataTypeLower === 'ib' || dataTypeLower === 'in') && fieldType === 'blp') {
    // Best lap time
    await this.handleLapTimeUpdate(websocketId, dataType, lapTime);
  }
}
```

### ✅ **3. Correct Pit Status Detection**

**Problem**: Pit status was being detected from field values instead of data types

**Solution**: Updated to use data types for pit status detection:

```typescript
case 'sta': // Status column
  if (competitorId && data.value) {
    if (data.type === 'si') {
      // Pit in
      await this.handlePitStatusUpdate(competitorId, 'IN');
    } else if (data.type === 'so' || data.type === 'in') {
      // Pit out
      await this.handlePitStatusUpdate(competitorId, 'OUT');
    } else {
      await this.handleStatusUpdate(competitorId, data.value);
    }
  }
  break;
```

### ✅ **4. Enhanced Field Type Handling**

**Added support for all field types from `raceService.js`**:

```typescript
case 'tlp': // Total laps column
  if (competitorId && data.value && data.type === 'in') {
    await this.handleLapCountUpdate(competitorId, data.value);
  }
  break;
case 'pit': // Pit column
  if (competitorId && data.value && data.type === 'in') {
    await this.handlePitCountUpdate(competitorId, data.value);
  }
  break;
case 'otr': // On track time column
  if (competitorId && data.value && (data.type === 'to' || data.type === 'in')) {
    await this.handleTimeOnTrackUpdate(competitorId, data.value);
  }
  break;
```

## Pit Stop Improvements

### ✅ **1. Removed Lap Number Requirement**

**Before**: Pit stops required lap number calculation
**After**: Lap number removed from pit stop schema as requested

```typescript
// OLD Schema
export interface IApexPitStop extends Document {
  // ... other fields
  lapNumber: number; // ❌ Removed
  // ... other fields
}

// NEW Schema  
export interface IApexPitStop extends Document {
  // ... other fields
  raceTimeAtPitIn?: number; // ✅ Added
  raceTimeAtPitOut?: number; // ✅ Added
  // ... other fields (no lapNumber)
}
```

### ✅ **2. Added Race Time Tracking**

**Added race time tracking at pit in and pit out**:

```typescript
/**
 * Get current race time in seconds from session start
 */
private getCurrentRaceTime(): number {
  if (!this.currentSession) return 0;
  
  const sessionStartTime = this.currentSession.createdAt || new Date();
  const currentTime = new Date();
  const raceTimeMs = currentTime.getTime() - sessionStartTime.getTime();
  
  return Math.floor(raceTimeMs / 1000); // Return seconds
}
```

**Pit Entry**:
```typescript
const pitStopData = {
  sessionId: this.currentSession._id,
  competitorId: competitorObjectId,
  kartId: competitor.kartId,
  pitInTime: new Date(),
  raceTimeAtPitIn: raceTimeAtPitIn, // ✅ Total race time when entering pit
  pitCurrentDuration: 0,
  reason: 'Regular',
  isActive: true
};
```

**Pit Exit**:
```typescript
await ApexPitStop.updateOne(
  { _id: activePitStop._id },
  {
    pitOutTime: pitOutTime,
    pitDuration: pitDuration,
    raceTimeAtPitOut: raceTimeAtPitOut, // ✅ Total race time when exiting pit
    pitCurrentDuration: 0,
    pitTotalDuration: newPitTotalDuration, // ✅ Calculated at pit out
    isActive: false
  }
);
```

### ✅ **3. Pit Total Duration Calculated at Pit Out**

**Before**: `pitTotalDuration` was calculated at pit entry
**After**: `pitTotalDuration` is calculated at pit exit as requested

```typescript
// Calculate total duration from all previous completed pit stops + this one
const previousPitStops = await ApexPitStop.find({
  sessionId: this.currentSession._id,
  competitorId: competitorObjectId,
  pitDuration: { $exists: true },
  _id: { $ne: activePitStop._id } // Exclude current pit stop
});

const previousTotalDuration = previousPitStops.reduce((total, pit) => total + (pit.pitDuration || 0), 0);
const newPitTotalDuration = previousTotalDuration + pitDuration; // ✅ Calculated at pit out
```

## Updated Database Schema

### **ApexPitStop Schema Changes**

```typescript
// REMOVED
lapNumber: { type: Number, required: true }, // ❌ Removed

// ADDED
raceTimeAtPitIn: { type: Number }, // ✅ Total race time when entering pit
raceTimeAtPitOut: { type: Number }, // ✅ Total race time when exiting pit
```

### **Complete Updated Schema**

```typescript
export interface IApexPitStop extends Document {
  sessionId: mongoose.Types.ObjectId;
  competitorId: mongoose.Types.ObjectId;
  kartId: mongoose.Types.ObjectId;
  pitInTime: Date;
  pitOutTime?: Date;
  pitDuration?: number; // Total duration when completed (seconds)
  pitCurrentDuration?: number; // Current duration for active pit stops (seconds)
  pitTotalDuration?: number; // Cumulative total calculated at pit out (seconds)
  raceTimeAtPitIn?: number; // Total race time when entering pit (seconds)
  raceTimeAtPitOut?: number; // Total race time when exiting pit (seconds)
  reason: string;
  isActive: boolean;
}
```

## Data Type Mappings (from raceService.js)

### **Lap Time Data Types**
- `tn` = Normal lap time
- `ti` = Personal best time
- `tb` = Best time in race
- `ib` = Best lap marker

### **Status Data Types**
- `si` = Pit in
- `so` = Pit out
- `in` = General increment/status

### **Count Data Types**
- `in` = Increment (for lap count, pit count)
- `to` = Time on track

## Expected Database Results

### **Pit Stop Record Example**
```javascript
{
  _id: ObjectId("..."),
  sessionId: ObjectId("..."),
  competitorId: ObjectId("17788"), // VANHAT KOIRAT
  kartId: ObjectId("..."),
  pitInTime: "2024-11-09T10:04:45.000Z",
  pitOutTime: "2024-11-09T10:05:15.000Z",
  pitDuration: 30, // Seconds in pit
  pitCurrentDuration: 0, // Reset on exit
  pitTotalDuration: 30, // ✅ Calculated at pit out
  raceTimeAtPitIn: 285, // ✅ 4:45 race time when entering
  raceTimeAtPitOut: 315, // ✅ 5:15 race time when exiting
  reason: "Regular",
  isActive: false
}
```

## Benefits

### ✅ **Correct Data Processing**
- Follows `raceService.js` logic exactly
- Proper field type and data type handling
- Accurate lap time and pit status detection

### ✅ **Enhanced Pit Stop Tracking**
- Race time context for pit stops
- Proper total duration calculation
- Simplified schema without unnecessary lap numbers

### ✅ **Real-time Compatibility**
- Supports both numeric pit statuses (6) and string statuses (IN/OUT)
- Handles `otr` field for pit duration updates
- Compatible with real-world websocket data

### ✅ **Performance Improvements**
- Removed unnecessary lap number calculations
- Optimized pit total duration calculation
- Better database indexing without lap number

## Testing

The updated parser now correctly handles:

1. **Field Type Detection**: Uses `data.type` instead of field names
2. **Lap Time Processing**: Follows `raceService.js` data type logic
3. **Pit Status Detection**: Proper `si`/`so` data type handling
4. **Race Time Tracking**: Records race time at pit in/out
5. **Total Duration**: Calculated at pit exit as requested

This ensures the apex parser processes real-world log data correctly and creates accurate pit stop records with proper race time context.
