#!/usr/bin/env node

/**
 * Test script for the optimized Apex schema
 * Verifies that the new schema works correctly with proper ObjectId references
 */

const mongoose = require('mongoose');

// Import optimized models (simplified for testing)
const ApexSessionSchema = new mongoose.Schema({
  title1: { type: String, default: '' },
  title2: { type: String, default: '' },
  track: { type: String, default: '' },
  isActive: { type: Boolean, default: true },
  gridData: { type: mongoose.Schema.Types.Mixed },
  sessionData: { type: mongoose.Schema.Types.Mixed, default: {} }
}, {
  timestamps: true,
  collection: 'apex_sessions_test'
});

const ApexTeamSchema = new mongoose.Schema({
  sessionId: { type: mongoose.Schema.Types.ObjectId, ref: 'ApexSession', required: true },
  name: { type: String, required: true },
  currentKartId: { type: mongoose.Schema.Types.ObjectId, ref: 'ApexKart' },
  pastKarts: [{ type: mongoose.Schema.Types.ObjectId, ref: 'ApexKart' }],
  drivers: [{ type: String }],
  nationality: { type: String, default: 'Unknown' },
  totalLaps: { type: Number, default: 0 },
  status: { type: String, default: 'active' },
  isActive: { type: Boolean, default: true }
}, {
  timestamps: true,
  collection: 'apex_teams_test'
});

const ApexKartSchema = new mongoose.Schema({
  sessionId: { type: mongoose.Schema.Types.ObjectId, ref: 'ApexSession', required: true },
  kartNumber: { type: Number, required: true },
  speed: { type: Number, default: 3 },
  currentTeamId: { type: mongoose.Schema.Types.ObjectId, ref: 'ApexTeam' },
  currentRowId: { type: mongoose.Schema.Types.ObjectId, ref: 'Row' },
  status: {
    type: String,
    enum: ['on_track', 'in_pit_row', 'maintenance', 'available'],
    default: 'available'
  },
  isActive: { type: Boolean, default: true }
}, {
  timestamps: true,
  collection: 'apex_karts_test'
});

const ApexCompetitorSchema = new mongoose.Schema({
  sessionId: { type: mongoose.Schema.Types.ObjectId, ref: 'ApexSession', required: true },
  competitorId: { type: String, required: true },
  teamId: { type: mongoose.Schema.Types.ObjectId, ref: 'ApexTeam', required: true },
  kartId: { type: mongoose.Schema.Types.ObjectId, ref: 'ApexKart', required: true },
  name: { type: String, required: true },
  nationality: { type: String, default: 'Unknown' },
  drivers: [{ type: String }],
  isActive: { type: Boolean, default: true }
}, {
  timestamps: true,
  collection: 'apex_competitors_test'
});

const ApexLapSchema = new mongoose.Schema({
  sessionId: { type: mongoose.Schema.Types.ObjectId, ref: 'ApexSession', required: true },
  competitorId: { type: String, required: true },
  kartId: { type: mongoose.Schema.Types.ObjectId, ref: 'ApexKart', required: true },
  lapNumber: { type: Number, default: 0 },
  lapTime: { type: Number, required: true }, // milliseconds
  lapTimeFormatted: { type: String, required: true },
  isBestLap: { type: Boolean, default: false },
  isPersonalBest: { type: Boolean, default: false },
  timestamp: { type: Date, required: true }
}, {
  timestamps: true,
  collection: 'apex_laps_test'
});

// Create models
const ApexSession = mongoose.model('ApexSessionTest', ApexSessionSchema);
const ApexTeam = mongoose.model('ApexTeamTest', ApexTeamSchema);
const ApexKart = mongoose.model('ApexKartTest', ApexKartSchema);
const ApexCompetitor = mongoose.model('ApexCompetitorTest', ApexCompetitorSchema);
const ApexLap = mongoose.model('ApexLapTest', ApexLapSchema);

// Test optimized schema
async function testOptimizedSchema() {
  console.log('🧪 Testing Optimized Apex Schema...');
  
  try {
    // Connect to database
    const mongoUri = process.env.MONGODB_URI || 'mongodb://localhost:27017/race-planner-test';
    await mongoose.connect(mongoUri);
    console.log('✅ Connected to database');
    
    // Clean up any existing test data
    await Promise.all([
      ApexSession.deleteMany({}),
      ApexTeam.deleteMany({}),
      ApexKart.deleteMany({}),
      ApexCompetitor.deleteMany({}),
      ApexLap.deleteMany({})
    ]);
    console.log('🧹 Cleaned up test data');
    
    // Test 1: Create session
    console.log('\n📝 Test 1: Creating session...');
    const session = await ApexSession.create({
      title1: 'Test Race',
      title2: 'Optimized Schema Test',
      track: 'Test Track',
      isActive: true
    });
    console.log(`✅ Session created: ${session._id}`);
    
    // Test 2: Create team
    console.log('\n📝 Test 2: Creating team...');
    const team = await ApexTeam.create({
      sessionId: session._id,
      name: 'Test Team',
      drivers: ['Test Driver'],
      nationality: 'Test Country'
    });
    console.log(`✅ Team created: ${team._id}`);
    
    // Test 3: Create kart
    console.log('\n📝 Test 3: Creating kart...');
    const kart = await ApexKart.create({
      sessionId: session._id,
      kartNumber: 1,
      speed: 3,
      status: 'available'
    });
    console.log(`✅ Kart created: ${kart._id}`);
    
    // Test 4: Create competitor with ObjectId references
    console.log('\n📝 Test 4: Creating competitor with ObjectId references...');
    const competitor = await ApexCompetitor.create({
      sessionId: session._id,
      competitorId: '12345',
      teamId: team._id, // ObjectId reference
      kartId: kart._id, // ObjectId reference
      name: 'Test Competitor',
      nationality: 'Test Country'
    });
    console.log(`✅ Competitor created: ${competitor._id}`);
    console.log(`   teamId: ${competitor.teamId} (ObjectId)`);
    console.log(`   kartId: ${competitor.kartId} (ObjectId)`);
    
    // Test 5: Update relationships
    console.log('\n📝 Test 5: Updating relationships...');
    await ApexTeam.findByIdAndUpdate(team._id, { currentKartId: kart._id });
    await ApexKart.findByIdAndUpdate(kart._id, { currentTeamId: team._id });
    console.log('✅ Relationships updated');
    
    // Test 6: Create lap with ObjectId reference
    console.log('\n📝 Test 6: Creating lap with ObjectId reference...');
    const lap = await ApexLap.create({
      sessionId: session._id,
      competitorId: '12345',
      kartId: kart._id, // ObjectId reference
      lapNumber: 1,
      lapTime: 71328, // milliseconds
      lapTimeFormatted: '1:11.328',
      timestamp: new Date()
    });
    console.log(`✅ Lap created: ${lap._id}`);
    console.log(`   kartId: ${lap.kartId} (ObjectId)`);
    console.log(`   lapTime: ${lap.lapTime}ms`);
    
    // Test 7: Query with population
    console.log('\n📝 Test 7: Testing population queries...');
    const populatedCompetitor = await ApexCompetitor.findById(competitor._id)
      .populate('teamId')
      .populate('kartId');
    
    console.log('✅ Population query successful:');
    console.log(`   Competitor: ${populatedCompetitor.name}`);
    console.log(`   Team: ${populatedCompetitor.teamId.name}`);
    console.log(`   Kart: ${populatedCompetitor.kartId.kartNumber}`);
    
    // Test 8: Complex query with joins
    console.log('\n📝 Test 8: Testing complex queries...');
    const lapsWithDetails = await ApexLap.find({ sessionId: session._id })
      .populate({
        path: 'kartId',
        populate: {
          path: 'currentTeamId',
          model: 'ApexTeamTest'
        }
      });
    
    if (lapsWithDetails.length > 0) {
      const lapWithDetails = lapsWithDetails[0];
      console.log('✅ Complex query successful:');
      console.log(`   Lap time: ${lapWithDetails.lapTimeFormatted}`);
      console.log(`   Kart: ${lapWithDetails.kartId.kartNumber}`);
      console.log(`   Team: ${lapWithDetails.kartId.currentTeamId?.name || 'No team'}`);
    }
    
    // Test 9: Validate required fields
    console.log('\n📝 Test 9: Testing validation...');
    try {
      await ApexCompetitor.create({
        sessionId: session._id,
        competitorId: '67890',
        name: 'Invalid Competitor'
        // Missing required teamId and kartId
      });
      console.log('❌ Validation should have failed!');
    } catch (validationError) {
      console.log('✅ Validation working correctly:');
      console.log(`   ${validationError.message}`);
    }
    
    // Test 10: Performance test
    console.log('\n📝 Test 10: Performance test...');
    const startTime = Date.now();
    
    // Create multiple entities
    const teams = [];
    const karts = [];
    for (let i = 0; i < 10; i++) {
      const testTeam = await ApexTeam.create({
        sessionId: session._id,
        name: `Team ${i}`,
        drivers: [`Driver ${i}`]
      });
      teams.push(testTeam);
      
      const testKart = await ApexKart.create({
        sessionId: session._id,
        kartNumber: i + 10,
        currentTeamId: testTeam._id
      });
      karts.push(testKart);
    }
    
    const endTime = Date.now();
    console.log(`✅ Created 10 teams and 10 karts in ${endTime - startTime}ms`);
    
    // Clean up test data
    await Promise.all([
      ApexSession.deleteMany({}),
      ApexTeam.deleteMany({}),
      ApexKart.deleteMany({}),
      ApexCompetitor.deleteMany({}),
      ApexLap.deleteMany({})
    ]);
    console.log('\n🧹 Cleaned up test data');
    
    console.log('\n🎉 All optimized schema tests passed!');
    console.log('\n📝 Summary:');
    console.log('   ✅ ObjectId references work correctly');
    console.log('   ✅ Population queries work');
    console.log('   ✅ Complex joins work');
    console.log('   ✅ Validation enforces required fields');
    console.log('   ✅ Performance is good');
    console.log('   ✅ No redundant fields');
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  } finally {
    await mongoose.disconnect();
    console.log('👋 Disconnected from database');
  }
}

// Run the test
if (require.main === module) {
  testOptimizedSchema().catch(console.error);
}

module.exports = { testOptimizedSchema };
