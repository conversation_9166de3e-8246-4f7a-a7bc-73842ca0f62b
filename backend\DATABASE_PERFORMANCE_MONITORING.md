# Database Performance Monitoring

## Overview

Added comprehensive performance monitoring to identify slow database operations that could be causing queue buildup. The system now tracks timing for all critical database operations and provides detailed performance statistics.

## Performance Monitoring Features

### ✅ **1. Automatic Operation Timing**
```typescript
private async measurePerformance<T>(operationName: string, operation: () => Promise<T>): Promise<T> {
  const startTime = Date.now();
  try {
    const result = await operation();
    const duration = Date.now() - startTime;
    
    // Update performance stats
    const stats = this.performanceStats.get(operationName) || { count: 0, totalTime: 0, maxTime: 0 };
    stats.count++;
    stats.totalTime += duration;
    stats.maxTime = Math.max(stats.maxTime, duration);
    this.performanceStats.set(operationName, stats);
    
    // Log slow operations
    if (duration > this.slowOperationThreshold) {
      console.warn(`🐌 SLOW DB OPERATION: ${operationName} took ${duration}ms`);
    }
    
    return result;
  } catch (error) {
    const duration = Date.now() - startTime;
    console.error(`❌ DB OPERATION FAILED: ${operationName} failed after ${duration}ms:`, error);
    throw error;
  }
}
```

### ✅ **2. Monitored Database Operations**

**Grid Creation Operations:**
- `ApexTeam.insertMany(count)` - Bulk team creation
- `ApexKart.insertMany(count)` - Bulk kart creation  
- `ApexCompetitor.insertMany(count)` - Bulk competitor creation

**Session Operations:**
- `ApexSession.findByIdAndUpdate(field)` - Session field updates

**Pit Stop Operations:**
- `ApexPitStop.create` - New pit stop creation
- `ApexPitStop.findOne(activePit)` - Finding active pit stops
- `ApexPitStop.updateOne(pitExit)` - Pit exit updates
- `ApexPitStop.updateOne(pitDuration)` - Pit duration updates

### ✅ **3. Performance Thresholds**

**Slow Operation Detection:**
- **100ms threshold**: Operations taking longer than 100ms are flagged as slow
- **50ms logging**: Operations taking longer than 50ms are logged for analysis

**Real-time Alerts:**
```
🐌 SLOW DB OPERATION: ApexPitStop.findOne(activePit) took 150ms
⏱️ ApexCompetitor.insertMany(65): 75ms
```

### ✅ **4. Performance Statistics**

**Automatic Tracking:**
- **Count**: Number of times each operation was called
- **Average Time**: Average duration per operation
- **Max Time**: Longest single operation duration
- **Total Time**: Cumulative time spent on each operation

**Performance Summary:**
```typescript
getPerformanceStats(): Record<string, { count: number; avgTime: number; maxTime: number; totalTime: number }>
```

### ✅ **5. Performance Summary Report**

**Automatic Summary on Cleanup:**
```
📊 DATABASE PERFORMANCE SUMMARY:
🔴 ApexCompetitor.insertMany(65): 1 calls, avg: 120ms, max: 120ms, total: 120ms
🟡 ApexPitStop.findOne(activePit): 15 calls, avg: 75ms, max: 150ms, total: 1125ms
🟢 ApexSession.findByIdAndUpdate(dyn1): 25 calls, avg: 25ms, max: 45ms, total: 625ms
🟢 ApexPitStop.create: 5 calls, avg: 30ms, max: 50ms, total: 150ms

📈 TOTAL: 46 operations, 2020ms total time
```

**Color Coding:**
- 🔴 **Red**: Average > 100ms (Critical - needs optimization)
- 🟡 **Yellow**: Average > 50ms (Warning - monitor closely)  
- 🟢 **Green**: Average ≤ 50ms (Good performance)

## Expected Performance Insights

### **Queue Buildup Causes**

**1. Slow Bulk Operations:**
```
🐌 SLOW DB OPERATION: ApexCompetitor.insertMany(65) took 250ms
```
- Large competitor creation during grid processing
- Could cause queue buildup during initial session setup

**2. Frequent Pit Queries:**
```
🐌 SLOW DB OPERATION: ApexPitStop.findOne(activePit) took 180ms
```
- Multiple pit status checks per message
- Could accumulate delay with many pit updates

**3. Session Updates:**
```
🐌 SLOW DB OPERATION: ApexSession.findByIdAndUpdate(dyn1) took 120ms
```
- Frequent dyn1 race time updates
- Could slow down message processing

### **Performance Optimization Targets**

**High Impact Operations** (sorted by total time):
1. **ApexCompetitor.insertMany** - One-time but expensive
2. **ApexPitStop.findOne** - Frequent and potentially slow
3. **ApexSession.findByIdAndUpdate** - Very frequent
4. **ApexPitStop.updateOne** - Frequent during pit activity

## Usage

### **Automatic Monitoring**
Performance monitoring is automatically enabled and tracks all database operations without any additional code changes.

### **Manual Performance Check**
```typescript
// Get current performance stats
const stats = parser.getPerformanceStats();
console.log('Current performance:', stats);

// Log performance summary
parser.logPerformanceSummary();
```

### **Cleanup with Summary**
```typescript
// Automatically logs performance summary
parser.cleanup();
```

## Performance Analysis

### **Expected Output During Log Replay**
```
⏱️ ApexTeam.insertMany(25): 45ms
⏱️ ApexKart.insertMany(25): 38ms
⏱️ ApexCompetitor.insertMany(65): 120ms
🐌 SLOW DB OPERATION: ApexCompetitor.insertMany(65) took 120ms
⏱️ ApexPitStop.findOne(activePit): 85ms
⏱️ ApexPitStop.create: 32ms
⏱️ ApexSession.findByIdAndUpdate(dyn1): 28ms
```

### **Queue Impact Analysis**
```
📊 DATABASE PERFORMANCE SUMMARY:
🔴 ApexCompetitor.insertMany(65): 1 calls, avg: 120ms, max: 120ms, total: 120ms
🟡 ApexPitStop.findOne(activePit): 45 calls, avg: 85ms, max: 150ms, total: 3825ms
🟢 ApexSession.findByIdAndUpdate(dyn1): 200 calls, avg: 28ms, max: 45ms, total: 5600ms

📈 TOTAL: 246 operations, 9545ms total time
```

**Analysis:**
- **ApexPitStop.findOne**: 45 calls × 85ms = 3.8 seconds of pit queries
- **Session updates**: 200 calls × 28ms = 5.6 seconds of session updates
- **Total database time**: 9.5 seconds for 246 operations

### **Queue Correlation**
If queue builds up to 100+ messages, check if:
1. **Bulk operations** are taking >200ms (grid creation bottleneck)
2. **Frequent operations** are taking >100ms (pit/session update bottleneck)
3. **Total database time** exceeds message processing rate

## Benefits

### ✅ **Identify Bottlenecks**
- Pinpoint exact operations causing delays
- Quantify performance impact with precise timing
- Prioritize optimization efforts based on total time impact

### ✅ **Monitor Performance Trends**
- Track performance changes over time
- Detect performance regressions
- Validate optimization improvements

### ✅ **Queue Correlation**
- Correlate slow database operations with queue buildup
- Understand root cause of message processing delays
- Optimize critical path operations

### ✅ **Production Monitoring**
- Real-time alerts for slow operations
- Performance summary for troubleshooting
- Data-driven optimization decisions

**Run your log replay again and the performance monitoring will show exactly which database operations are slow and potentially causing queue buildup!**
