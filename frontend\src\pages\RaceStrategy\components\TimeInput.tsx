import React, { useState, useEffect } from 'react';
import { IonInput, IonNote, IonIcon, IonButton } from '@ionic/react';
import { 
  formatSecondsToTimeString, 
  parseTimeStringToSeconds, 
  isValidTimeFormat,
  TimeFormat
} from '../utils/timeUtils';
import { timeOutline, refreshOutline } from 'ionicons/icons';

interface TimeInputProps {
  label: string;
  value: number;
  onChange: (seconds: number) => void;
  format: TimeFormat;
  error?: string;
  placeholder?: string;
  disabled?: boolean;
  className?: string;
}

const TimeInput: React.FC<TimeInputProps> = ({ 
  label, 
  value, 
  onChange, 
  format, 
  error, 
  placeholder,
  disabled = false,
  className = ''
}) => {
  const [displayValue, setDisplayValue] = useState<string>('');
  const [isValid, setIsValid] = useState<boolean>(true);
  
  // Update display value when value prop changes
  useEffect(() => {
    setDisplayValue(formatSecondsToTimeString(value, format));
    setIsValid(true);
  }, [value, format]);
  
  // Handle input change
  const handleChange = (e: CustomEvent) => {
    const inputValue = e.detail.value as string;
    setDisplayValue(inputValue);
    
    // Validate the format
    const valid = isValidTimeFormat(inputValue, format);
    setIsValid(valid);
    
    // Only update parent if the format is valid
    if (valid) {
      const seconds = parseTimeStringToSeconds(inputValue, format);
      onChange(seconds);
    }
  };
  
  // Reset to the original value
  const handleReset = () => {
    setDisplayValue(formatSecondsToTimeString(value, format));
    setIsValid(true);
  };
  
  return (
    <div className={`time-input-container ${className}`}>
      {label && (
        <div className="time-input-label">
          {label}
        </div>
      )}
      
      <div className="time-input-field">
        <IonIcon icon={timeOutline} className="time-input-icon" />
        <IonInput
          type="text"
          value={displayValue}
          onIonChange={handleChange}
          placeholder={placeholder || getPlaceholderForFormat(format)}
          disabled={disabled}
          className={`time-input ${!isValid ? 'time-input-invalid' : ''}`}
        />
        {!disabled && (
          <IonButton 
            fill="clear" 
            size="small" 
            onClick={handleReset}
            className="time-input-reset"
          >
            <IonIcon icon={refreshOutline} />
          </IonButton>
        )}
      </div>
      
      {(error || !isValid) && (
        <IonNote color="danger" className="time-input-error">
          {error || `Invalid format. Expected: ${getFormatDescription(format)}`}
        </IonNote>
      )}
    </div>
  );
};

// Helper function to get appropriate placeholder based on format
const getPlaceholderForFormat = (format: TimeFormat): string => {
  switch (format) {
    case 'mm:ss':
      return '05:30';
    case 'hh:mm':
      return '01:30';
    case 'hh:mm:ss':
      return '01:30:45';
    case 'mm:ss.fff':
      return '01:35.500';
    default:
      return '';
  }
};

// Helper function to get format description
const getFormatDescription = (format: TimeFormat): string => {
  switch (format) {
    case 'mm:ss':
      return 'Minutes:Seconds (05:30)';
    case 'hh:mm':
      return 'Hours:Minutes (01:30)';
    case 'hh:mm:ss':
      return 'Hours:Minutes:Seconds (01:30:45)';
    case 'mm:ss.fff':
      return 'Minutes:Seconds.Milliseconds (01:35.500)';
    default:
      return '';
  }
};

export default TimeInput;


