import React, { useState, useEffect } from 'react';
import {
  IonHeader,
  IonToolbar,
  IonTitle,
  IonContent,
  IonButtons,
  IonButton,
  IonIcon,
  IonModal,
  IonCard,
  IonCardHeader,
  IonCardTitle,
  IonCardContent,
  IonItem,
  IonLabel,
  IonAccordionGroup,
  IonAccordion,
  IonList,
  IonPopover,
  IonSelect,
  IonSelectOption
} from '@ionic/react';
import { 
  closeOutline, 
  createOutline, 
  trashOutline, 
  addOutline, 
  calculatorOutline,
  informationCircleOutline,
  timerOutline,
  carSportOutline,
  hourglassOutline,
  flagOutline,
  timeOutline,
  settingsOutline
} from 'ionicons/icons';
import { StrategyData } from '../../../types';
import { displaySecondsAsHHMMSS, displaySecondsAsMMSS } from '../utils/timeFormatters';
import apiService from '../../../services/apiService';
import ApexDataControls from '../../../components/ApexDataControls/ApexDataControls';
import './RaceManagementModal.css';

interface RaceManagementModalProps {
  isOpen: boolean;
  onClose: () => void;
  strategyData: StrategyData;
  onEditStrategy: () => void;
  onDeleteStrategy: () => void;
  onCreateStrategy: () => void;
  onRecalculateStints: () => void;
  onSwitchStrategy: (strategyId: string) => void;
}

const RaceManagementModal: React.FC<RaceManagementModalProps> = ({
  isOpen,
  onClose,
  strategyData,
  onEditStrategy,
  onDeleteStrategy,
  onCreateStrategy,
  onRecalculateStints,
  onSwitchStrategy
}) => {
  const [showActionsPopover, setShowActionsPopover] = useState(false);
  const [popoverEvent, setPopoverEvent] = useState<Event | null>(null);
  const [strategies, setStrategies] = useState<{ _id: string; raceName: string }[]>([]);
  const [currentStrategyId, setCurrentStrategyId] = useState<string | null>(null);
  
  // Fetch all strategies when modal opens
  useEffect(() => {
    if (isOpen) {
      fetchStrategies();
    }
  }, [isOpen]);
  
  // Update current strategy ID when strategyData changes
  useEffect(() => {
    if (strategyData && strategyData._id) {
      setCurrentStrategyId(strategyData._id);
    }
  }, [strategyData]);
  
  const fetchStrategies = async () => {
    try {
      const data = await apiService.strategy.getAll();
      setStrategies(data.map((s: StrategyData) => ({ _id: s._id || '', raceName: s.raceName })));
    } catch (error) {
      console.error('Error fetching strategies:', error);
    }
  };
  
  const formatTime = (seconds: number) => {
    return displaySecondsAsHHMMSS(seconds);
  };
  
  const formatShortTime = (seconds: number) => {
    return displaySecondsAsMMSS(seconds);
  };
  
  const handleActionClick = (e: React.MouseEvent) => {
    setPopoverEvent(e.nativeEvent);
    setShowActionsPopover(true);
  };
  
  const handleStrategyChange = (e: CustomEvent) => {
    const selectedId = e.detail.value;
    if (selectedId && onSwitchStrategy) {
      onSwitchStrategy(selectedId);
    }
  };
  
  return (
    <IonModal isOpen={isOpen} onDidDismiss={onClose}>
      <IonHeader>
        <IonToolbar>
          <IonTitle>Race Strategy</IonTitle>
          <IonButtons slot="end">
            <IonButton onClick={onRecalculateStints} title="Recalculate Stints">
              <IonIcon icon={calculatorOutline} />
            </IonButton>
            <IonButton onClick={handleActionClick} title="Strategy Actions">
              <IonIcon icon={settingsOutline} />
            </IonButton>
            <IonButton onClick={onClose}>
              <IonIcon icon={closeOutline} />
            </IonButton>
          </IonButtons>
        </IonToolbar>
        
        {/* Strategy Selector Toolbar */}
        {strategies.length > 0 && (
          <IonToolbar className="strategy-selector-toolbar">
            <IonSelect
              interface="popover"
              value={currentStrategyId}
              onIonChange={handleStrategyChange}
              className="strategy-selector"
              placeholder="Select Strategy"
            >
              {strategies.map(strategy => (
                <IonSelectOption key={strategy._id} value={strategy._id}>
                  {strategy.raceName}
                </IonSelectOption>
              ))}
            </IonSelect>
          </IonToolbar>
        )}
      </IonHeader>
      
      <IonContent>
        {/* Apex Data Controls */}
        <ApexDataControls
          showTeamSelection={true}
          title="Race Data Source"
          description="Choose between manual strategy data or live Apex database data"
        />

        {/* Strategy Header Card */}
        <IonCard className="strategy-header-card">
          <IonCardHeader>
            <IonCardTitle>{strategyData.raceName}</IonCardTitle>
          </IonCardHeader>
          <IonCardContent>
            <div className="race-type-indicator">
              <IonIcon icon={strategyData.raceDurationType === 'time' ? timeOutline : flagOutline} />
              <span>
                {strategyData.raceDurationType === 'time'
                  ? formatTime(strategyData.raceDurationValue)
                  : `${strategyData.raceDurationValue} Laps`}
              </span>
            </div>
          </IonCardContent>
        </IonCard>
        
        {/* Quick Rules Overview */}
        <div className="quick-rules-container">
          <div className="quick-rule">
            <div className="quick-rule-icon">
              <IonIcon icon={carSportOutline} />
            </div>
            <div className="quick-rule-content">
              <div className="quick-rule-label">Pit Stops</div>
              <div className="quick-rule-value">{strategyData.mandatoryPitStops}</div>
            </div>
          </div>
          
          <div className="quick-rule">
            <div className="quick-rule-icon">
              <IonIcon icon={timerOutline} />
            </div>
            <div className="quick-rule-content">
              <div className="quick-rule-label">Min Stint</div>
              <div className="quick-rule-value">{formatShortTime(strategyData.minStintTimeSeconds)}</div>
            </div>
          </div>
          
          <div className="quick-rule">
            <div className="quick-rule-icon">
              <IonIcon icon={timerOutline} />
            </div>
            <div className="quick-rule-content">
              <div className="quick-rule-label">Max Stint</div>
              <div className="quick-rule-value">{formatShortTime(strategyData.maxStintTimeSeconds)}</div>
            </div>
          </div>
          
          <div className="quick-rule">
            <div className="quick-rule-icon">
              <IonIcon icon={hourglassOutline} />
            </div>
            <div className="quick-rule-content">
              <div className="quick-rule-label">Lap Time</div>
              <div className="quick-rule-value">{formatShortTime(strategyData.avgLapTimeSeconds)}</div>
            </div>
          </div>
        </div>
        
        {/* Detailed Rules Accordion */}
        <IonAccordionGroup>
          <IonAccordion value="pitWindow">
            <IonItem slot="header">
              <IonIcon icon={informationCircleOutline} slot="start" />
              <IonLabel>Pit Window Details</IonLabel>
            </IonItem>
            
            <div className="ion-padding" slot="content">
              <div className="detailed-rules-grid">
                <div className="detailed-rule">
                  <div className="detailed-rule-label">Window Opens</div>
                  <div className="detailed-rule-value">
                    {strategyData.pitWindowOpenType === 'time'
                      ? formatTime(strategyData.pitWindowOpenValue)
                      : `Lap ${strategyData.pitWindowOpenValue}`}
                  </div>
                </div>
                
                <div className="detailed-rule">
                  <div className="detailed-rule-label">Window Closes</div>
                  <div className="detailed-rule-value">
                    {strategyData.pitWindowCloseType === 'time'
                      ? formatTime(strategyData.pitWindowCloseValue)
                      : `Lap ${strategyData.pitWindowCloseValue}`}
                  </div>
                </div>
                
                <div className="detailed-rule">
                  <div className="detailed-rule-label">Min Pit Duration</div>
                  <div className="detailed-rule-value">{formatShortTime(strategyData.minPitDurationSeconds)}</div>
                </div>
              </div>
            </div>
          </IonAccordion>
        </IonAccordionGroup>
        
        {/* Actions Popover */}
        <IonPopover
          isOpen={showActionsPopover}
          event={popoverEvent}
          onDidDismiss={() => setShowActionsPopover(false)}
          className="strategy-actions-popover"
        >
          <IonList>
            <IonItem button onClick={() => { setShowActionsPopover(false); onEditStrategy(); }}>
              <IonIcon slot="start" icon={createOutline} />
              <IonLabel>Edit Strategy</IonLabel>
            </IonItem>
            
            <IonItem button onClick={() => { setShowActionsPopover(false); onCreateStrategy(); }}>
              <IonIcon slot="start" icon={addOutline} />
              <IonLabel>Create New Strategy</IonLabel>
            </IonItem>
            
            <IonItem 
              button 
              onClick={() => { setShowActionsPopover(false); onDeleteStrategy(); }}
              disabled={strategyData.startTime !== undefined}
              color={strategyData.startTime !== undefined ? undefined : "danger"}
            >
              <IonIcon slot="start" icon={trashOutline} />
              <IonLabel>Delete Strategy</IonLabel>
            </IonItem>
          </IonList>
        </IonPopover>
      </IonContent>
    </IonModal>
  );
};

export default RaceManagementModal;
