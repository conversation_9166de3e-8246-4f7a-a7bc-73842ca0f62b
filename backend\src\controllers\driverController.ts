// controllers/driverController.ts
import { Request, Response } from 'express';
import mongoose from 'mongoose'; // Import mongoose
import { Driver } from '../models/Driver';
import { Lap } from '../models/Lap'; // *** Import Lap model ***
import { isValidObjectId, sendErrorResponse } from '../utils/controllerUtils'; // *** Import helpers ***
import { sendWebSocketMessage } from '../websocket/websocket'; // *** Import WS helper ***

const DRIVER_UPDATE_EVENT = "driversUpdated"; // Example WS event name

// Create a new driver
export const createDriver = async (req: Request, res: Response) => {
  console.log('[Driver Controller] POST /api/drivers - Attempting to create a new driver.');
  try {
    const { name, teamId } = req.body;

    const newDriver = new Driver({
      name,
      teamId,
    });

    const savedDriver = await newDriver.save();
    console.log(`[Driver Controller] Driver created with ID: ${savedDriver._id}`);
    res.status(201).json(savedDriver);
  } catch (error) {
    console.error('Error creating driver:', error);
    res.status(500).json({ message: 'Error creating driver', error });
  }
};

// Get all drivers
export const getAllDrivers = async (req: Request, res: Response) => {
  console.log('[Driver Controller] GET /api/drivers - Attempting to fetch all drivers.');
  try {
    const drivers = await Driver.find().populate('teamId'); // Populate the team reference
    res.status(200).json(drivers);
  } catch (error) {
    console.error('Error retrieving drivers:', error);
    res.status(500).json({ message: 'Error retrieving drivers', error });
  }
};

// Get a specific driver by ID
export const getDriverById = async (req: Request, res: Response): Promise<void> => {
  console.log(`[Driver Controller] GET /api/drivers/${req.params.id} - Attempting to fetch driver by ID.`);
  try {
    const driver = await Driver.findById(req.params.id).populate('teamId');

    if (!driver) {
      sendErrorResponse(res, 'Driver not found', 404);
      console.warn(`[Driver Controller] Driver with ID ${req.params.id} not found.`);
      return; // *** Add explicit return ***
    }

    res.status(200).json(driver);
  } catch (error) {
    console.error('Error retrieving driver:', error);
    res.status(500).json({ message: 'Error retrieving driver', error });
  }
};

// Update a driver by ID
export const updateDriver = async (req: Request, res: Response): Promise<void> => {
  console.log(`[Driver Controller] PUT /api/drivers/${req.params.id} - Attempting to update driver by ID.`);
  try {
    const { name, teamId } = req.body;

    const updatedDriver = await Driver.findByIdAndUpdate(
      req.params.id,
      { name, teamId },
      { new: true }
    );

    if (!updatedDriver) {
      console.warn(`[Driver Controller] Driver with ID ${req.params.id} not found for update.`);
      sendErrorResponse(res, 'Driver not found', 404);
      return; // *** Add explicit return ***
    }

    res.status(200).json(updatedDriver);
  } catch (error) {
    console.error('Error updating driver:', error);
    res.status(500).json({ message: 'Error updating driver', error });
  }
};

// Delete a driver by ID
export const deleteDriver = async (req: Request, res: Response): Promise<void> => {
  console.log(`[Driver Controller] DELETE /api/drivers/${req.params.id} - Attempting to delete driver by ID.`);
  try {
    const deletedDriver = await Driver.findByIdAndDelete(req.params.id);

    if (!deletedDriver) {
      sendErrorResponse(res, 'Driver not found', 404);
      console.warn(`[Driver Controller] Driver with ID ${req.params.id} not found for deletion.`);
      return; // *** Add explicit return ***
    }

    res.status(200).json({ message: 'Driver deleted successfully' });
  } catch (error) {
    console.error('Error deleting driver:', error);
    res.status(500).json({ message: 'Error deleting driver', error });
  }
};
