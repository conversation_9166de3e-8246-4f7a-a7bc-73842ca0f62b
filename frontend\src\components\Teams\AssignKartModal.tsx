// src/components/Teams/AssignKartModal.tsx
import React, { useState } from 'react';
import {
    IonModal,
    IonHeader,
    IonToolbar,
    IonTitle,
    IonButtons,
    IonButton,
    IonIcon,
    IonContent,
    IonListHeader,
    IonList,
    IonItem,  // <-- Add IonItem import
    IonLabel, // <-- Add IonLabel import
    IonText,
    IonAlert,
} from '@ionic/react';
import { close } from 'ionicons/icons';
import { Team, Kart } from '../../types'; // Adjust path if needed
import { KartListItem } from '../Karts/KartListItem'; // Import KartListItem

import '../../themes/list-styles.css';

import '../../themes/speed-styles.css';

import '../../themes/row-styles.css'; // If needed for location indicator
interface AssignKartModalProps {
    isOpen: boolean;
    onDidDismiss: () => void;
    teamToAssignTo: Team | null;
    availableKarts: Kart[];
    onAssignKart: (teamId: string, kartId: string) => Promise<boolean>; // Returns true on success
    isMutating: boolean;
}

export const AssignKartModal: React.FC<AssignKartModalProps> = ({
    isOpen,
    onDidDismiss,
    teamToAssignTo,
    availableKarts,
    onAssignKart,
    isMutating,
}) => {
    const [kartForAssignConfirm, setKartForAssignConfirm] = useState<Kart | null>(null);
    const [showAssignConfirmAlert, setShowAssignConfirmAlert] = useState<boolean>(false);

    const triggerAssignConfirm = (kart: Kart) => {
        setKartForAssignConfirm(kart);
        setShowAssignConfirmAlert(true);
    };

    const handleInternalAssign = async () => {
        setShowAssignConfirmAlert(false);
        if (!teamToAssignTo || !kartForAssignConfirm) return;

        const success = await onAssignKart(teamToAssignTo._id, kartForAssignConfirm._id);
        if (success) {
            setKartForAssignConfirm(null); // Reset selection
            onDidDismiss(); // Close modal via dismiss
        }
        // Error handling done by parent via toast
    };

    const handleDismiss = () => {
        setKartForAssignConfirm(null); // Reset selection on dismiss
        setShowAssignConfirmAlert(false); // Ensure alert is closed
        onDidDismiss();
    };

    return (
        <>
            <IonModal isOpen={isOpen} onDidDismiss={handleDismiss}>
                <IonHeader>
                    <IonToolbar>
                        <IonTitle>Assign Kart to Team {teamToAssignTo?.number}</IonTitle>
                        <IonButtons slot="end">
                            <IonButton onClick={handleDismiss} disabled={isMutating}>
                                <IonIcon slot="icon-only" icon={close} />
                            </IonButton>
                        </IonButtons>
                    </IonToolbar>
                </IonHeader>
                <IonContent> {/* Removed ion-padding */}
                    <IonListHeader>Select an Available Kart</IonListHeader>
                    {availableKarts.length > 0 ? (
                        <>
                        <IonList lines="none" className="data-list"> {/* Use same classes as KartStatusList */}
                            {availableKarts.map(kart => (
                                <KartListItem
                                    key={kart._id}
                                    kart={kart}
                                    isProcessing={isMutating} // Pass the mutation state
                                    onClick={() => triggerAssignConfirm(kart)}
                                />
                            ))}
                        </IonList>
                        </>
                    ) : (
                        <IonText color="medium" className="ion-padding ion-text-center" style={{ display: 'block' }}>
                            <p>No karts are currently available for assignment.</p>
                        </IonText>
                    )}
                </IonContent>
            </IonModal>

            {/* Assign Kart Confirmation Alert */}
            <IonAlert
                isOpen={showAssignConfirmAlert}
                onDidDismiss={() => {
                    setShowAssignConfirmAlert(false);
                    setKartForAssignConfirm(null);
                }}
                header={'Confirm Assignment'}
                message={`Assign Kart #${kartForAssignConfirm?.number} to Team ${teamToAssignTo?.number}?`}
                buttons={[
                    { text: 'Cancel', role: 'cancel' },
                    {
                        text: 'Assign',
                        handler: handleInternalAssign // Call internal handler
                    }
                ]}
            />
        </>
    );
};
