export interface ParsedMessage {
  timestamp: Date;
  type: 'init' | 'update' | 'session' | 'driver';
  data: Record<string, { type: string; value: string }>;
}

/**
 * Parse a log message line from Apex timing system
 * Handles the format: [timestamp] field|type|value
 */
export const parseLogMessage = (line: string): ParsedMessage | null => {
  if (!line || !line.trim()) {
    return null;
  }

  try {
    // Extract timestamp if present
    const timestampMatch = line.match(/^\[([^\]]+)\]/);
    let timestamp = new Date();
    let messageContent = line;

    if (timestampMatch && timestampMatch[1]) {
      timestamp = new Date(timestampMatch[1]);
      messageContent = line.substring(timestampMatch[0].length).trim();
    }

    // Parse the message content
    const data: Record<string, { type: string; value: string }> = {};
    let messageType: 'init' | 'update' | 'session' | 'driver' = 'update';

    // Split by lines and process each field
    const fields = messageContent.split('\n').filter(field => field.trim());

    for (const field of fields) {
      const parts = field.split('|');
      if (parts.length >= 2) {
        const fieldName = parts[0]?.trim() || '';
        const fieldType = parts[1]?.trim() || '';
        const fieldValue = parts.slice(2).join('|').trim(); // Handle values with | in them

        data[fieldName] = {
          type: fieldType,
          value: fieldValue
        };

        // Determine message type
        if (fieldName === 'init') {
          messageType = 'init';
        } else if (fieldName.startsWith('r') && fieldName.includes('c')) {
          messageType = 'driver';
        } else if (['title1', 'title2', 'dyn1', 'track', 'com', 'grid'].includes(fieldName)) {
          messageType = 'session';
        }
      }
    }

    return {
      timestamp,
      type: messageType,
      data
    };

  } catch (error) {
    console.error('Error parsing log message:', line, error);
    return null;
  }
};

/**
 * Parse a multi-line websocket message
 * Handles messages that span multiple lines
 */
export const parseMultiLineMessage = (messageLines: string[]): ParsedMessage | null => {
  if (!messageLines || messageLines.length === 0) {
    return null;
  }

  // Combine all lines and parse as a single message
  const combinedMessage = messageLines.join('\n');
  return parseLogMessage(combinedMessage);
};

/**
 * Parse comma-separated data format
 * Handles the format where actual data comes after comma brackets
 */
export const parseCommaSeparatedData = (data: string): Record<string, { type: string; value: string }> => {
  const result: Record<string, { type: string; value: string }> = {};

  if (!data || !data.trim()) {
    return result;
  }

  try {
    // Split by commas and process each field
    const fields = data.split(',').map(field => field.trim()).filter(field => field);

    for (const field of fields) {
      // Look for pattern: field|type|value
      const parts = field.split('|');
      if (parts.length >= 2) {
        const fieldName = parts[0]?.trim() || '';
        const fieldType = parts[1]?.trim() || '';
        const fieldValue = parts.slice(2).join('|').trim();

        result[fieldName] = {
          type: fieldType,
          value: fieldValue
        };
      }
    }

  } catch (error) {
    console.error('Error parsing comma-separated data:', data, error);
  }

  return result;
};

/**
 * Extract driver/competitor ID from field name
 * Handles formats like r12345c4 -> competitorId: 12345, columnId: 4
 */
export const extractDriverInfo = (fieldName: string): { competitorId: string; columnId: string } | null => {
  const match = fieldName.match(/^r(\d+)c(\d+)$/);
  if (match && match[1] && match[2]) {
    return {
      competitorId: match[1],
      columnId: match[2]
    };
  }
  return null;
};

/**
 * Parse time string to seconds
 * Handles various time formats from Apex system
 */
export const parseTimeToSeconds = (timeStr: string): number => {
  if (!timeStr || timeStr.trim() === '') {
    return 0;
  }

  try {
    // Remove any non-time characters
    const cleanTime = timeStr.replace(/[^\d:.,]/g, '');

    // Handle format like "1:23.456" or "1:23,456"
    if (cleanTime.includes(':')) {
      const parts = cleanTime.split(':');
      if (parts.length === 2 && parts[0] && parts[1]) {
        const minutes = parseInt(parts[0]) || 0;
        const seconds = parseFloat(parts[1].replace(',', '.')) || 0;
        return minutes * 60 + seconds;
      }
    }

    // Handle direct seconds format
    const directSeconds = parseFloat(cleanTime.replace(',', '.'));
    if (!isNaN(directSeconds)) {
      return directSeconds;
    }

  } catch (error) {
    console.error('Error parsing time string:', timeStr, error);
  }

  return 0;
};

/**
 * Format seconds to time string
 */
export const formatSecondsToTime = (seconds: number): string => {
  if (seconds <= 0) {
    return '0:00.000';
  }

  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  
  return `${minutes}:${remainingSeconds.toFixed(3).padStart(6, '0')}`;
};

/**
 * Validate message structure
 */
export const validateMessage = (message: ParsedMessage): boolean => {
  if (!message || typeof message !== 'object') {
    return false;
  }

  if (!message.timestamp || !(message.timestamp instanceof Date)) {
    return false;
  }

  if (!message.type || !['init', 'update', 'session', 'driver'].includes(message.type)) {
    return false;
  }

  if (!message.data || typeof message.data !== 'object') {
    return false;
  }

  return true;
};

/**
 * Extract session information from message
 */
export const extractSessionInfo = (message: ParsedMessage): {
  title1?: string;
  title2?: string;
  track?: string;
  startTime?: Date;
} => {
  const sessionInfo: any = {};

  if (message.data.title1) {
    sessionInfo.title1 = message.data.title1.value;
  }

  if (message.data.title2) {
    sessionInfo.title2 = message.data.title2.value;
  }

  if (message.data.track) {
    sessionInfo.track = message.data.track.value;
  }

  if (message.timestamp) {
    sessionInfo.startTime = message.timestamp;
  }

  return sessionInfo;
};

/**
 * Check if message contains grid data
 */
export const hasGridData = (message: ParsedMessage): boolean => {
  return !!(message.data.grid && message.data.grid.value);
};

/**
 * Check if message is an init message
 */
export const isInitMessage = (message: ParsedMessage): boolean => {
  return message.type === 'init' || !!(message.data.init);
};

/**
 * Extract all driver field updates from message
 */
export const extractDriverUpdates = (message: ParsedMessage): Array<{
  competitorId: string;
  columnId: string;
  fieldName: string;
  type: string;
  value: string;
}> => {
  const updates: Array<{
    competitorId: string;
    columnId: string;
    fieldName: string;
    type: string;
    value: string;
  }> = [];

  for (const [fieldName, fieldData] of Object.entries(message.data)) {
    const driverInfo = extractDriverInfo(fieldName);
    if (driverInfo) {
      updates.push({
        competitorId: driverInfo.competitorId,
        columnId: driverInfo.columnId,
        fieldName,
        type: fieldData.type,
        value: fieldData.value
      });
    }
  }

  return updates;
};

/**
 * Parse session timer from dyn1 field
 * Converts milliseconds to hh:mm:ss format
 */
export const parseSessionTimer = (dyn1Value: string): string => {
  try {
    const milliseconds = parseInt(dyn1Value) || 0;
    const totalSeconds = Math.floor(milliseconds / 1000);
    
    const hours = Math.floor(totalSeconds / 3600);
    const minutes = Math.floor((totalSeconds % 3600) / 60);
    const seconds = totalSeconds % 60;

    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  } catch (error) {
    console.error('Error parsing session timer:', dyn1Value, error);
    return '00:00:00';
  }
};
