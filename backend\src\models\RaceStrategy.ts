import mongoose, { Schema, Document, Types } from 'mongoose';

/**
 * Interface representing the Race Strategy document in MongoDB.
 */
export interface IRaceStrategy extends Document {
  raceName: string;
  raceDurationValue: number; // Value for duration (either laps or minutes)
  raceDurationType: 'laps' | 'time'; // Unit for duration
  mandatoryPitStops: number; // Number of mandatory stops
  minStintTimeSeconds: number; // Minimum stint duration in seconds
  maxStintTimeSeconds: number; // Maximum stint duration in seconds
  pitWindowOpenValue: number; // Value when pit window opens (lap or minutes)
  pitWindowOpenType: 'laps' | 'time'; // Unit for pit window open
  pitWindowCloseValue: number; // Value when pit window closes (lap or minutes)
  pitWindowCloseType: 'laps' | 'time'; // Unit for pit window close
  avgLapTimeSeconds: number; // Average lap time in seconds
  minPitDurationSeconds: number; // Minimum duration for a pit stop in seconds
  startTime?: Date; // Optional: Actual start time of the race

  // Calculated/User Input Fields (Consider if these belong here or in a related 'RaceLog' model)
  calculatedStintTimeSeconds?: number; // Optional: A calculated ideal stint time
  actualStintTimesSeconds?: number[]; // Optional: Array of actual stint durations input by user
  actualPitTimesSeconds?: number[]; // Optional: Array of actual pit stop durations input by user

  // Flags
  isActive: boolean; // Flag to mark the currently active strategy
  stints: Types.ObjectId[]; // Array of references to Stint documents
  isTemplate: boolean; // Flag to mark if this is a template strategy

  // Timestamps
  createdAt: Date;
  updatedAt: Date;
}

const RaceStrategySchema: Schema = new Schema(
  {
    raceName: {
      type: String,
      required: [true, 'Race name is required.'],
      trim: true,
    },
    raceDurationValue: {
      type: Number,
      required: [true, 'Race duration value is required.'],
      min: [0, 'Race duration value cannot be negative.'],
    },
    raceDurationType: {
      type: String,
      required: [true, 'Race duration type is required.'],
      enum: ['laps', 'time'], // 'time' typically means seconds here
    },
    mandatoryPitStops: {
      type: Number,
      required: [true, 'Number of mandatory pit stops is required.'],
      min: [0, 'Mandatory pit stops cannot be negative.'],
      default: 0,
    },
    minStintTimeSeconds: {
      type: Number,
      required: [true, 'Minimum stint time (in seconds) is required.'],
      min: [0, 'Minimum stint time cannot be negative.'],
    },
    maxStintTimeSeconds: {
      type: Number,
      required: [true, 'Maximum stint time (in seconds) is required.'],
      min: [0, 'Maximum stint time cannot be negative.'],
    },
    pitWindowOpenValue: {
      type: Number,
      required: [true, 'Pit window open value is required.'],
      min: [0, 'Pit window open value cannot be negative.'],
    },
    pitWindowOpenType: {
      type: String,
      required: [true, 'Pit window open type is required.'],
      enum: ['laps', 'time'],
    },
    pitWindowCloseValue: {
      type: Number,
      required: [true, 'Pit window close value is required.'],
      min: [0, 'Pit window close value cannot be negative.'],
    },
    pitWindowCloseType: {
      type: String,
      required: [true, 'Pit window close type is required.'],
      enum: ['laps', 'time'],
    },
    avgLapTimeSeconds: { type: Number, required: true, min: 0 },
    minPitDurationSeconds: { type: Number, required: true, min: 0 },
    startTime: { type: Date, required: false },
    calculatedStintTimeSeconds: { type: Number, required: false, min: 0 },
    actualStintTimesSeconds: { type: [Number], required: false, default: [] },
    actualPitTimesSeconds: { type: [Number], required: false, default: [] },
    isActive: {
      type: Boolean,
      default: false,
    },
    stints: [{ type: Schema.Types.ObjectId, ref: 'Stint' }],
    isTemplate: {
      type: Boolean,
      default: false,
    },
  },
  {
    timestamps: true, // Automatically adds createdAt and updatedAt fields
  }
);

// Add validation to ensure maxStintTimeSeconds >= minStintTimeSeconds if needed
RaceStrategySchema.pre<IRaceStrategy>('save', function (next) {
  if (this.maxStintTimeSeconds < this.minStintTimeSeconds) {
    console.error(`[RaceStrategy Model] Validation Error: maxStintTimeSeconds (${this.maxStintTimeSeconds}) < minStintTimeSeconds (${this.minStintTimeSeconds})`);
    next(new Error('Maximum stint time cannot be less than minimum stint time.'));
  } else {
    next();
  }
});

// Add validation for pit window logic if needed (e.g., close > open)

// Add a pre-save hook to ensure only one strategy is active at a time
RaceStrategySchema.pre<IRaceStrategy>('save', async function(next) {
  // If this strategy is being set as active, deactivate all others
  console.log(`[RaceStrategy Model] Pre-save hook for strategy ID: ${this._id}, isActive: ${this.isActive}`);
  if (this.isActive) {
    console.log(`[RaceStrategy Model] Deactivating other active strategies...`);
    try {
      await mongoose.model('RaceStrategy').updateMany(
        { _id: { $ne: this._id } },
        { isActive: false }
      );
    } catch (error) {
      console.error(`[RaceStrategy Model] Error deactivating other strategies:`, error);
      return next(error as Error);
    }
  }
  next(); // Always call next()
});

export default mongoose.model<IRaceStrategy>('RaceStrategy', RaceStrategySchema);
