// d:\Desktop\ManulilacRaceplanner\frontend\src\pages\PitRows\components\KartRowSettings.tsx
import React from 'react';
import {
    IonModal,
    IonHeader,
    IonToolbar,
    IonTitle,
    IonButtons,
    IonButton,
    IonIcon,
    IonContent,
    IonList,
    IonItem,
    IonChip,          // Import IonChip
    IonLabel,
    IonSpinner,
    IonText,
    IonSelect,
    IonSelectOption,
} from '@ionic/react';
import { close, addCircleOutline, trashOutline } from 'ionicons/icons';
import { Row } from '../../../types'; // Adjust path as needed
import './KartRowSettings.css'; // Ensure this CSS file exists and is styled

// Define available colors here or import from a constants file
export const AVAILABLE_ROW_COLORS = [
    'white', 'black', 'red', 'green', 'blue', 'yellow' // Updated colors
];

interface KartRowSettingsProps {
    isOpen: boolean;
    onClose: () => void;
    rows: Row[];
    onCreateRow: (newRowData: { color: string }) => Promise<void>; // Expects color for new row
    onDeleteRow: (rowNumber: number) => Promise<void>;
    onUpdateRowColor: (rowNumber: number, newColor: string) => Promise<void>;
    isMutating: boolean;
}

const KartRowSettings: React.FC<KartRowSettingsProps> = ({
    isOpen,
    onClose,
    rows,
    onCreateRow,
    onDeleteRow,
    onUpdateRowColor,
    isMutating,
}) => {

    const handleAddNewRow = () => {
        // Add a new row with a default color (e.g., the first available color or 'grey')
        const defaultColor = AVAILABLE_ROW_COLORS[0] || 'grey';
        onCreateRow({ color: defaultColor });
    };

    return (
        <IonModal isOpen={isOpen} onDidDismiss={onClose}>
            <IonHeader>
                <IonToolbar>
                    <IonTitle>Manage Pit Rows</IonTitle>
                    <IonButtons slot="end">
                        <IonButton onClick={onClose} disabled={isMutating}>
                            <IonIcon slot="icon-only" icon={close} />
                        </IonButton>
                    </IonButtons>
                </IonToolbar>
            </IonHeader>
            <IonContent className="ion-padding">
                <IonList lines="full">
                    {rows.length === 0 && !isMutating && (
                        <IonItem lines="none" className="ion-text-center">
                            <IonText color="medium">No pit rows configured yet.</IonText>
                        </IonItem>
                    )}
                    {rows.map((row) => (
                        <IonItem key={row._id} className="row-setting-item">
                            <IonLabel className="row-number-label">Row {row.rowNumber}</IonLabel>
                            <IonSelect
                                value={row.color}
                                onIonChange={e => !isMutating && e.detail.value && e.detail.value !== row.color && onUpdateRowColor(row.rowNumber, e.detail.value)}
                                disabled={isMutating}
                                interface="popover" // Or "action-sheet"
                                placeholder="Select Color"
                                className={`color-select row-bg-${row.color}`} // Add dynamic class for selected color indicator
                            // Use justify="end" to push icon/indicator right if needed
                            >
                                {AVAILABLE_ROW_COLORS.map((color) => (
                                    <IonSelectOption
                                        key={color}
                                        value={color}
                                        className={`select-option-color-block row-bg-${color}`} // Class for styling the option block
                                    />
                                ))}
                            </IonSelect>
                            <IonButton
                                slot="end"
                                fill="clear"
                                color="danger"
                                size="small"
                                onClick={() => !isMutating && onDeleteRow(row.rowNumber)}
                                disabled={isMutating}
                                title={`Delete Row ${row.rowNumber}`}
                            >
                                {isMutating ? <IonSpinner name="dots" /> : <IonIcon icon={trashOutline} />}
                            </IonButton>
                        </IonItem>
                    ))}
                </IonList>

                <IonButton
                    expand="block"
                    fill="outline"
                    onClick={handleAddNewRow}
                    disabled={isMutating}
                    style={{ marginTop: '1rem' }}
                >
                    <IonIcon slot="start" icon={addCircleOutline} />
                    Add New Row
                    {isMutating && <IonSpinner name="dots" slot="end" />}
                </IonButton>
            </IonContent>
        </IonModal>
    );
};

export default KartRowSettings;
