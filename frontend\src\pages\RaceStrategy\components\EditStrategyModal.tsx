import React, { useState, useEffect } from 'react';
import axios from 'axios';
import {
  IonModal,
  IonHeader,
  IonToolbar,
  IonTitle,
  IonContent,
  IonButtons,
  IonButton,
  IonItem,
  IonLabel,
  IonInput,
  IonSelect,
  IonSelectOption,
  IonNote
  // Remove unused imports
} from '@ionic/react';
import { StrategyData } from '../../../types';
import TimeInput from './TimeInput';
import { 
  SECONDS_IN_HOUR,
  SECONDS_IN_MINUTE
  // Remove unused imports
} from '../utils/timeUtils';
// Remove unused import for RaceDurationInput

interface EditStrategyModalProps {
  isOpen: boolean;
  onClose: () => void;
  initialStrategy: StrategyData | null;
  onSave: (strategy: StrategyData) => void;
  isNewStrategy: boolean;
  apiBaseUrl: string;
}

const EditStrategyModal: React.FC<EditStrategyModalProps> = ({
  isOpen,
  onClose,
  initialStrategy,
  onSave,
  isNewStrategy,
  apiBaseUrl
}) => {
  // Add formErrors state
  const [formErrors, setFormErrors] = useState<Record<string, string>>({});
  
  // Initialize with default values for new strategy
  const [strategyData, setStrategyData] = useState<StrategyData>(() => {
    const defaultStrategy: StrategyData = {
        _id: '',
        raceName: 'New Race Strategy',
        raceDurationValue: 6 * 60 * 60, // 6 hours in seconds
        raceDurationType: 'time',
        mandatoryPitStops: 8,
        minStintTimeSeconds: 20 * 60, // 20 minutes
        maxStintTimeSeconds: 65 * 60, // 65 minutes
        avgLapTimeSeconds: 90, // 1:30 per lap
        minPitDurationSeconds: 120, // 2 minutes
        pitWindowOpenValue: 20 * 60, // 20 minutes
        pitWindowOpenType: 'time',
        pitWindowCloseValue: 5.5 * 60 * 60, // 5.5 hours
        pitWindowCloseType: 'time',
        startTime: undefined, // Align with StrategyData type
        isActive: false
      };
    return initialStrategy 
      ? { ...initialStrategy, startTime: initialStrategy.startTime === null ? undefined : initialStrategy.startTime } 
      : defaultStrategy;
  });
  
  // Update form when initialStrategy changes
  useEffect(() => {
    if (initialStrategy) {
      setStrategyData({ ...initialStrategy, startTime: initialStrategy.startTime === null ? undefined : initialStrategy.startTime });
    }
  }, [initialStrategy]);
  
  // Handle form submission
  const handleSave = async () => {
    console.log('[EditStrategyModal] Attempting to save strategy.');
    if (validateForm()) {
      try {
        let response;
        
        if (isNewStrategy) {
          // Create new strategy
          response = await axios.post<StrategyData>(
            `${apiBaseUrl}/strategy`,
            // Ensure only necessary fields are sent, exclude _id for new creation
            strategyData
          );
        } else {
          // Update existing strategy
          response = await axios.put<StrategyData>(
            `${apiBaseUrl}/strategy`,
            strategyData
            // _id should be included in strategyData for update
          );
        }
        
        onSave(response.data);
        console.log('[EditStrategyModal] Strategy saved successfully.');
      } catch (error) {
        console.error('Error saving strategy:', error);
        // Handle error (show toast, etc.)
      }
    }
  };
  
  // Handle input changes
  const handleInputChange = (field: keyof StrategyData, value: unknown) => {
    setStrategyData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // Validate form before saving
  const validateForm = (): boolean => {
    console.log('[EditStrategyModal] Validating form...');
    const errors: Record<string, string> = {};
    
    // Required fields
    if (!strategyData.raceName) {
      errors.raceName = 'Race name is required';
    }
    
    if (!strategyData.raceDurationValue || strategyData.raceDurationValue <= 0) {
      errors.raceDurationValue = 'Race duration must be greater than 0';
    }
    
    if (!strategyData.avgLapTimeSeconds || strategyData.avgLapTimeSeconds <= 0) {
      errors.avgLapTimeSeconds = 'Average lap time must be greater than 0';
    }
    
    if (strategyData.mandatoryPitStops < 0) {
      errors.mandatoryPitStops = 'Mandatory pit stops cannot be negative';
    }
    
    if (!strategyData.minPitDurationSeconds || strategyData.minPitDurationSeconds <= 0) {
      errors.minPitDurationSeconds = 'Minimum pit duration must be greater than 0';
    }
    
    // Set errors and return validation result
    if (Object.keys(errors).length > 0) {
      console.warn('[EditStrategyModal] Form validation failed:', errors);
    } else {
      console.log('[EditStrategyModal] Form validation successful.');
    }
    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  return (
    <IonModal isOpen={isOpen} onDidDismiss={onClose}>
      <IonHeader>
        <IonToolbar>
          <IonTitle>Edit Race Strategy</IonTitle>
          <IonButtons slot="end">
            <IonButton onClick={onClose}>Cancel</IonButton>
            <IonButton strong onClick={handleSave}>Save</IonButton>
          </IonButtons>
        </IonToolbar>
      </IonHeader>
      <IonContent className="ion-padding">
        <IonItem>
          <IonLabel position="stacked">Race Name</IonLabel>
          <IonInput
            value={strategyData.raceName}
            onIonChange={e => handleInputChange('raceName', e.detail.value)}
            placeholder="Enter race name"
          />
          {formErrors.raceName && <IonNote color="danger">{formErrors.raceName}</IonNote>}
        </IonItem>
        
        <IonItem>
          <IonLabel position="stacked">Race Duration</IonLabel>
          <div className="ion-margin-top ion-margin-bottom">
            <IonSelect
              value={strategyData.raceDurationType}
              onIonChange={e => handleInputChange('raceDurationType', e.detail.value)}
            >
              <IonSelectOption value="time">Time</IonSelectOption>
              <IonSelectOption value="laps">Laps</IonSelectOption>
            </IonSelect>
          </div>
          
          {strategyData.raceDurationType === 'time' ? (
            <TimeInput
              label="Race Duration (hh:mm:ss)"
              value={strategyData.raceDurationValue}
              onChange={(value) => handleInputChange('raceDurationValue', value)}
              format="hh:mm:ss"
              error={formErrors.raceDurationValue}
              placeholder="06:00:00"
            />
          ) : (
            <IonInput
              type="number"
              value={strategyData.raceDurationValue}
              onIonChange={e => handleInputChange('raceDurationValue', parseInt(e.detail.value || '0', 10))}
              placeholder="Enter number of laps"
            />
          )}
          {formErrors.raceDurationValue && <IonNote color="danger">{formErrors.raceDurationValue}</IonNote>}
        </IonItem>
        
        <IonItem>
          <IonLabel position="stacked">Mandatory Pit Stops</IonLabel>
          <IonInput
            type="number"
            value={strategyData.mandatoryPitStops}
            onIonChange={e => handleInputChange('mandatoryPitStops', parseInt(e.detail.value || '0', 10))}
            placeholder="Enter number of mandatory pit stops"
          />
          {formErrors.mandatoryPitStops && <IonNote color="danger">{formErrors.mandatoryPitStops}</IonNote>}
        </IonItem>
        
        <IonItem>
          <TimeInput
            label="Average Lap Time (mm:ss.fff)"
            value={strategyData.avgLapTimeSeconds}
            onChange={(value) => handleInputChange('avgLapTimeSeconds', value)}
            format="mm:ss.fff"
            error={formErrors.avgLapTimeSeconds}
            placeholder="01:30.000"
          />
        </IonItem>
        
        <IonItem>
          <TimeInput
            label="Minimum Pit Duration (mm:ss)"
            value={strategyData.minPitDurationSeconds}
            onChange={(value) => handleInputChange('minPitDurationSeconds', value)}
            format="mm:ss"
            error={formErrors.minPitDurationSeconds}
            placeholder="00:45"
          />
        </IonItem>
        
        <IonItem>
          <TimeInput
            label="Minimum Stint Time (mm:ss)"
            value={strategyData.minStintTimeSeconds}
            onChange={(value) => handleInputChange('minStintTimeSeconds', value)}
            format="mm:ss"
            error={formErrors.minStintTimeSeconds}
            placeholder="20:00"
          />
        </IonItem>
        
        <IonItem>
          <TimeInput
            label="Maximum Stint Time (mm:ss)"
            value={strategyData.maxStintTimeSeconds}
            onChange={(value) => handleInputChange('maxStintTimeSeconds', value)}
            format="mm:ss"
            error={formErrors.maxStintTimeSeconds}
            placeholder="45:00"
          />
        </IonItem>
        
        <IonItem>
          <IonLabel position="stacked">Pit Window Opens</IonLabel>
          <div className="ion-margin-top ion-margin-bottom">
            <IonSelect
              value={strategyData.pitWindowOpenType}
              onIonChange={e => handleInputChange('pitWindowOpenType', e.detail.value)}
            >
              <IonSelectOption value="time">Time</IonSelectOption>
              <IonSelectOption value="lap">Lap</IonSelectOption>
            </IonSelect>
          </div>
          
          {strategyData.pitWindowOpenType === 'time' && (
            <TimeInput
              label="Pit Window Opens (mm:ss)"
              value={strategyData.pitWindowOpenValue}
              onChange={(value) => handleInputChange('pitWindowOpenValue', value)}
              format="mm:ss"
              error={formErrors.pitWindowOpenValue}
              placeholder="20:00"
            />
          )}
        </IonItem>
        
        <IonItem>
          <IonLabel position="stacked">Pit Window Closes</IonLabel>
          <div className="ion-margin-top ion-margin-bottom">
            <IonSelect
              value={strategyData.pitWindowCloseType}
              onIonChange={e => handleInputChange('pitWindowCloseType', e.detail.value)}
            >
              <IonSelectOption value="time">Time Before End</IonSelectOption>
              <IonSelectOption value="lap">Laps Before End</IonSelectOption>
            </IonSelect>
          </div>
          
          {strategyData.pitWindowCloseType === 'time' && (
            <TimeInput
              label="Pit Window Closes (time before race end)"
              value={strategyData.pitWindowCloseValue}
              onChange={(value) => handleInputChange('pitWindowCloseValue', value)}
              format="mm:ss"
              error={formErrors.pitWindowCloseValue}
              placeholder="30:00"
            />
          )}
        </IonItem>
      </IonContent>
    </IonModal>
  );
};

export default EditStrategyModal;
