export interface Driver {
  gridId: number;
  grp: { type: string; value: string };
  sta: { type: string; value: string };
  rk: { type: string; value: string };
  no: { type: string; value: string };
  dr: { type: string; value: string };
  s1: { type: string; value: string };
  s2: { type: string; value: string };
  s3: { type: string; value: string };
  sp1: { type: string; value: string };
  llp: { type: string; value: string };
  blp: { type: string; value: string };
  gap: { type: string; value: string };
  tlp: { type: string; value: string };
  nat: { type: string; value: string };
  otr: { type: string; value: string };
  int?: { type: string; value: string };
  pit?: { type: string; value: string };
  [key: string]: any;
  Laps: any[];
  Pits: any[];
  pastKartIds: any[];
  currentKartId: number;
}

export interface GridParseResult {
  drivers: { [key: string]: Driver };
  header_labels: { [key: string]: string };
  header_types: { [key: string]: string };
  id: number;
}

/**
 * Parse HTML grid data from Apex timing system
 * Based on the original parseGrid.ts from apex parser files
 */
export const parseGridData = (grid: string): GridParseResult => {
  const id = Date.now();
  const drivers: { [key: string]: Driver } = {};
  const header: {
    types: { [key: string]: string };
    labels: { [key: string]: string };
  } = { types: {}, labels: {} };

  if (!grid) {
    console.warn('No grid data provided');
    return { drivers, header_labels: header.labels, header_types: header.types, id };
  }

  // Regex to capture each row with a `data-id` attribute
  const rowsRegex = /<tr[^>]*data-id="(r\d+)"[^>]*>(.*?)<\/tr>/gs;
  let rowMatch;

  while ((rowMatch = rowsRegex.exec(grid)) !== null) {
    const rowId = rowMatch[1]; // Extract the row ID (e.g., "r0", "r3")
    let driver: Driver | null = null;

    // Regex to match all <td> or nested <div>/<p> with data-id attribute inside the row
    const cellRegex =
      /<td[^>]*data-id="(r?\d*c\d+|c\d+)"[^>]*>(.*?)<\/td>|<(?:div|p)[^>]*data-id="(r?\d*c\d+|c\d+)"[^>]*>(.*?)<\/(?:div|p)>/gs;
    let cellMatch;

    // Process each cell in the row
    const rowContent = rowMatch[2];
    if (!rowContent) continue;

    while ((cellMatch = cellRegex.exec(rowContent)) !== null) {
      const cellId = cellMatch[1] || cellMatch[3]; // Either <td> or nested <div>/<p> ID
      if (!cellId) continue;

      const cleanCellId = cellId.replace(/^r?\d*/, ""); // Remove row number part of the ID
      const cellContent = (cellMatch[2] || cellMatch[4] || "")
        .replace(/<[^>]+>/g, "")
        .trim(); // Clean cell content by stripping HTML tags

      if (rowId === "r0") {
        // For the header row (r0), capture the labels and types
        const headerTypeMatch = /data-type="([^"]*)"/.exec(cellMatch[0]);
        let headerType = headerTypeMatch ? headerTypeMatch[1] : "";

        // If the data-type is empty, assign a custom headerType based on cell content
        if (!headerType) {
          headerType = cellContent.toLowerCase().replace(/\s+/g, "");
        }

        if (headerType) {
          header.types[cleanCellId] = headerType;
        }
        header.labels[cleanCellId] = cellContent;
      } else {
        // Initialize the driver if it hasn't been initialized already
        if (!driver) {
          driver = createEmptyDriver(id);
        }

        // Map cell data to the driver object based on the header types
        const headerType = header.types[cleanCellId];

        if (headerType && driver.hasOwnProperty(headerType)) {
          driver[headerType] = { type: headerType, value: cellContent };
        } else if (headerType) {
          // Create dynamic property for unknown header types
          driver[headerType] = { type: headerType, value: cellContent };
        } else {
          // If headerType is missing, create a new type name based on cell content
          const dynamicType = cellContent.toLowerCase().replace(/\s+/g, "");
          if (dynamicType) {
            driver[dynamicType] = { type: dynamicType, value: cellContent };
          }
        }
      }
    }

    // Only add the driver if it's not the header row and driver is initialized
    if (rowId !== "r0" && driver) {
      if (rowId) {
        drivers[rowId] = driver;
      }
    }
  }

  const result = {
    drivers,
    header_labels: header.labels,
    header_types: header.types,
    id
  };

  console.log('Grid parsing completed. Found', Object.keys(drivers).length, 'drivers');
  return result;
};

/**
 * Create an empty driver object with default structure
 */
function createEmptyDriver(gridId: number): Driver {
  return {
    gridId,
    grp: { type: "", value: "" },
    sta: { type: "", value: "" },
    rk: { type: "", value: "" },
    no: { type: "", value: "" },
    dr: { type: "", value: "" },
    s1: { type: "", value: "" },
    s2: { type: "", value: "" },
    s3: { type: "", value: "" },
    sp1: { type: "", value: "" },
    llp: { type: "", value: "" },
    blp: { type: "", value: "" },
    gap: { type: "", value: "" },
    tlp: { type: "", value: "" },
    nat: { type: "", value: "" },
    otr: { type: "", value: "" },
    int: { type: "", value: "" },
    pit: { type: "", value: "" },
    Laps: [],
    Pits: [],
    pastKartIds: [],
    currentKartId: 0
  };
}

/**
 * Extract kart numbers from grid data
 */
export const extractKartNumbers = (gridData: GridParseResult): { [competitorId: string]: number } => {
  const kartNumbers: { [competitorId: string]: number } = {};

  for (const [rowId, driver] of Object.entries(gridData.drivers)) {
    const competitorId = rowId.replace('r', '');
    const kartNumber = parseInt(driver.no?.value || '0') || 0;
    kartNumbers[competitorId] = kartNumber;
  }

  return kartNumbers;
};

/**
 * Extract team information from grid data
 */
export const extractTeamInfo = (gridData: GridParseResult): Array<{
  competitorId: string;
  teamName: string;
  kartNumber: number;
  nationality: string;
}> => {
  const teams: Array<{
    competitorId: string;
    teamName: string;
    kartNumber: number;
    nationality: string;
  }> = [];

  for (const [rowId, driver] of Object.entries(gridData.drivers)) {
    const competitorId = rowId.replace('r', '');
    const teamName = driver.dr?.value || `Team ${competitorId}`;
    const kartNumber = parseInt(driver.no?.value || '0') || 0;
    const nationality = driver.nat?.value || 'Unknown';

    teams.push({
      competitorId,
      teamName,
      kartNumber,
      nationality
    });
  }

  return teams;
};

/**
 * Get header mapping for column types
 */
export const getHeaderMapping = (gridData: GridParseResult): { [columnId: string]: string } => {
  return gridData.header_types;
};

/**
 * Validate grid data structure
 */
export const validateGridData = (gridData: GridParseResult): boolean => {
  if (!gridData || typeof gridData !== 'object') {
    return false;
  }

  if (!gridData.drivers || typeof gridData.drivers !== 'object') {
    return false;
  }

  if (!gridData.header_types || typeof gridData.header_types !== 'object') {
    return false;
  }

  if (!gridData.header_labels || typeof gridData.header_labels !== 'object') {
    return false;
  }

  // Check if we have essential columns
  const requiredColumns = ['no', 'dr']; // Kart number and driver/team name
  const hasRequiredColumns = requiredColumns.some(col => 
    Object.values(gridData.header_types).includes(col)
  );

  if (!hasRequiredColumns) {
    console.warn('Grid data missing required columns:', requiredColumns);
    return false;
  }

  return true;
};

/**
 * Get driver by competitor ID
 */
export const getDriverByCompetitorId = (gridData: GridParseResult, competitorId: string): Driver | null => {
  const rowId = `r${competitorId}`;
  return gridData.drivers[rowId] || null;
};

/**
 * Get all competitor IDs from grid data
 */
export const getAllCompetitorIds = (gridData: GridParseResult): string[] => {
  return Object.keys(gridData.drivers).map(rowId => rowId.replace('r', ''));
};
