import React from 'react';
import { IonIcon, IonText, IonButton } from '@ionic/react';
import { hourglassOutline, addOutline } from 'ionicons/icons';

interface NoDataMessageProps {
  onCreateStrategy: () => void;
}

const NoDataMessage: React.FC<NoDataMessageProps> = ({ onCreateStrategy }) => {
  return (
    <div className="ion-text-center no-data-message">
      <IonIcon icon={hourglassOutline} size="large" color="medium" />
      <IonText color="medium">
        <p>No strategy data has been configured yet.</p>
      </IonText>
      <IonButton onClick={onCreateStrategy}>
        <IonIcon slot="start" icon={addOutline} />
        Create Strategy
      </IonButton>
    </div>
  );
};

export default NoDataMessage;
