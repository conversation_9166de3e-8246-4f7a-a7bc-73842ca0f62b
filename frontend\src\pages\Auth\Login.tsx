import React, { useState, useEffect } from 'react';
import { 
  IonPage, 
  IonHeader, 
  IonToolbar, 
  IonTitle, 
  IonContent, 
  IonItem, 
  IonLabel, 
  IonInput, 
  IonButton, 
  IonGrid, 
  IonRow, 
  IonCol, 
  IonCard, 
  IonCardHeader, 
  IonCardTitle, 
  IonCardContent,
  IonLoading,
  IonAlert,
  IonText,
  IonIcon,
  IonBackButton,
  IonButtons
} from '@ionic/react';
import { personCircleOutline, lockClosedOutline } from 'ionicons/icons';
import { useAuth } from '../../context/AuthContext';
import { useLocation } from 'react-router';


const Login: React.FC = () => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [showAlert, setShowAlert] = useState(false);
  const [localError, setLocalError] = useState<string | null>(null);
  const location = useLocation();

  // Get auth context
  const { login, isLoading, error, clearError } = useAuth();

  // Check for success message from registration
  const message = location.state && (location.state as any).message;

  // Show alert when error changes
  useEffect(() => {
    if (error) {
      setLocalError(error);
      setShowAlert(true);
    }
  }, [error]);

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Validate inputs
    if (!email || !password) {
      setLocalError('Please enter both email and password');
      setShowAlert(true);
      return;
    }
    
    try {
      await login(email, password);
    } catch (err) {
      console.error('Login error caught in component:', err);
      // Error is already set in the context
    }
  };

  return (
    <IonPage>
      <IonContent fullscreen className="ion-padding">
        <IonLoading 
          isOpen={isLoading} 
          message={'Logging in...'} 
          duration={15000} // 15 seconds max
        />

        <IonAlert
          isOpen={showAlert}
          onDidDismiss={() => {
            setShowAlert(false);
            clearError();
            setLocalError(null);
          }}
          header="Login Error"
          message={localError || error || 'An error occurred during login. Please try again.'}
          buttons={['OK']}
        />

        <IonGrid fixed>
          <IonRow className="ion-justify-content-center">
            <IonCol size="12" sizeMd="8" sizeLg="6" sizeXl="4">
              <div className="logo-container ion-text-center">
                <h1>Manulilac RacePlanner</h1>
                {message && (
                  <IonText color="success">
                    <p className="ion-text-center">{message}</p>
                  </IonText>
                )}
              </div>
              
              <IonCard className="auth-card">
                <IonCardHeader>
                  <IonCardTitle className="ion-text-center">
                    <IonIcon icon={personCircleOutline} color="primary" className="auth-header-icon" />
                    <h2>Login</h2>
                  </IonCardTitle>
                </IonCardHeader>
                
                <IonCardContent>
                  <form onSubmit={handleLogin}>
                    <IonItem>
                      <IonIcon icon={personCircleOutline} slot="start" color="medium" />
                      <IonLabel position="floating">Email</IonLabel>
                      <IonInput 
                        type="email" 
                        value={email} 
                        onIonChange={e => setEmail(e.detail.value || '')} 
                        required
                      />
                    </IonItem>
                    
                    <IonItem className="ion-margin-bottom">
                      <IonIcon icon={lockClosedOutline} slot="start" color="medium" />
                      <IonLabel position="floating">Password</IonLabel>
                      <IonInput 
                        type="password" 
                        value={password} 
                        onIonChange={e => setPassword(e.detail.value || '')} 
                        required
                      />
                    </IonItem>
                    
                    <IonButton 
                      expand="block" 
                      type="submit" 
                      className="ion-margin-top"
                      disabled={isLoading}
                    >
                      Login
                    </IonButton>
                    
                    <div className="ion-text-center ion-margin-top">
                      <IonText>
                        <p>Don't have an account? <a href="/register">Register</a></p>
                      </IonText>
                    </div>
                  </form>
                </IonCardContent>
              </IonCard>
            </IonCol>
          </IonRow>
        </IonGrid>
      </IonContent>
    </IonPage>
  );
};

export default Login;

