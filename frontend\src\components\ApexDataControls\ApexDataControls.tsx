import React from 'react';
import {
  IonCard,
  IonCardHeader,
  IonCardTitle,
  IonCardContent,
  IonItem,
  IonLabel,
  IonToggle,
  IonSelect,
  IonSelectOption,
  IonSpinner,
  IonIcon,
  IonButton,
  IonNote,
  IonAlert
} from '@ionic/react';
import { refreshOutline, warningOutline } from 'ionicons/icons';
import { useDataContext } from '../../context/DataContext';
import './ApexDataControls.css';

interface ApexDataControlsProps {
  showTeamSelection?: boolean;
  title?: string;
  description?: string;
}

const ApexDataControls: React.FC<ApexDataControlsProps> = ({
  showTeamSelection = false,
  title = "Apex Database Data",
  description = "Use live data from Apex racing sessions"
}) => {
  const {
    useApexDatabase,
    setUseApexDatabase,
    selectedSessionId,
    setSelectedSessionId,
    availableSessions,
    selectedSession,
    selectedTeamId,
    setSelectedTeamId,
    availableTeams,
    selectedTeam,
    isLoadingSessions,
    isLoadingTeams,
    refreshSessions,
    refreshTeams,
    error,
    clearError
  } = useDataContext();

  const handleToggleApexDatabase = (enabled: boolean) => {
    setUseApexDatabase(enabled);
    if (enabled) {
      refreshSessions();
    }
  };

  const handleSessionChange = (sessionId: string) => {
    setSelectedSessionId(sessionId);
  };

  const handleTeamChange = (teamId: string) => {
    setSelectedTeamId(teamId);
  };

  const handleRefreshSessions = () => {
    refreshSessions();
  };

  const handleRefreshTeams = () => {
    refreshTeams();
  };

  return (
    <>
      <IonCard>
        <IonCardHeader>
          <IonCardTitle>{title}</IonCardTitle>
        </IonCardHeader>
        <IonCardContent>
          {/* Apex Database Toggle */}
          <IonItem>
            <IonLabel>
              <h3>Use Apex Database</h3>
              <p>{description}</p>
            </IonLabel>
            <IonToggle
              checked={useApexDatabase}
              onIonChange={(e) => handleToggleApexDatabase(e.detail.checked)}
            />
          </IonItem>

          {/* Session Selection */}
          {useApexDatabase && (
            <>
              <IonItem>
                <IonLabel position="stacked">
                  Session Selection
                  {selectedSession && (
                    <IonNote color="medium">
                      {selectedSession.track} • {new Date(selectedSession.createdAt).toLocaleDateString()}
                    </IonNote>
                  )}
                </IonLabel>
                <div className="apex-select-container">
                  <IonSelect
                    value={selectedSessionId}
                    onIonChange={(e) => handleSessionChange(e.detail.value)}
                    placeholder="Select a session"
                    interface="popover"
                    disabled={isLoadingSessions}
                  >
                    {availableSessions.map((session) => (
                      <IonSelectOption key={session._id} value={session._id}>
                        {session.title1} - {session.title2}
                      </IonSelectOption>
                    ))}
                  </IonSelect>
                  <IonButton
                    fill="clear"
                    size="small"
                    onClick={handleRefreshSessions}
                    disabled={isLoadingSessions}
                  >
                    {isLoadingSessions ? <IonSpinner name="crescent" /> : <IonIcon icon={refreshOutline} />}
                  </IonButton>
                </div>
              </IonItem>

              {/* Session Info */}
              {selectedSession && (
                <IonItem>
                  <IonLabel>
                    <h3>Selected Session</h3>
                    <p>
                      <strong>{selectedSession.title1} - {selectedSession.title2}</strong><br />
                      Track: {selectedSession.track}<br />
                      Created: {new Date(selectedSession.createdAt).toLocaleString()}<br />
                      Teams Available: {availableTeams.length}
                    </p>
                  </IonLabel>
                </IonItem>
              )}

              {/* Team Selection (if enabled) */}
              {showTeamSelection && selectedSessionId && (
                <IonItem>
                  <IonLabel position="stacked">
                    Team Selection (for Race Strategy)
                    {selectedTeam && (
                      <IonNote color="medium">
                        Kart #{selectedTeam.kartNumber} • {selectedTeam.nationality}
                      </IonNote>
                    )}
                  </IonLabel>
                  <div className="apex-select-container">
                    <IonSelect
                      value={selectedTeamId}
                      onIonChange={(e) => handleTeamChange(e.detail.value)}
                      placeholder="Select a team to follow"
                      interface="popover"
                      disabled={isLoadingTeams || availableTeams.length === 0}
                    >
                      {availableTeams.map((team) => (
                        <IonSelectOption key={team._id} value={team._id}>
                          {team.name} (Kart #{team.kartNumber})
                        </IonSelectOption>
                      ))}
                    </IonSelect>
                    <IonButton
                      fill="clear"
                      size="small"
                      onClick={handleRefreshTeams}
                      disabled={isLoadingTeams}
                    >
                      {isLoadingTeams ? <IonSpinner name="crescent" /> : <IonIcon icon={refreshOutline} />}
                    </IonButton>
                  </div>
                </IonItem>
              )}

              {/* Team Info */}
              {showTeamSelection && selectedTeam && (
                <IonItem>
                  <IonLabel>
                    <h3>Selected Team</h3>
                    <p>
                      <strong>{selectedTeam.name}</strong><br />
                      Kart Number: #{selectedTeam.kartNumber}<br />
                      Nationality: {selectedTeam.nationality}<br />
                      Status: {selectedTeam.status}
                    </p>
                  </IonLabel>
                </IonItem>
              )}

              {/* No teams message */}
              {showTeamSelection && selectedSessionId && availableTeams.length === 0 && !isLoadingTeams && (
                <IonItem>
                  <IonIcon icon={warningOutline} slot="start" color="warning" />
                  <IonLabel>
                    <h3>No Teams Found</h3>
                    <p>No teams available in the selected session. Make sure the session has been parsed and teams have been created.</p>
                  </IonLabel>
                </IonItem>
              )}

              {/* No sessions message */}
              {availableSessions.length === 0 && !isLoadingSessions && (
                <IonItem>
                  <IonIcon icon={warningOutline} slot="start" color="warning" />
                  <IonLabel>
                    <h3>No Sessions Found</h3>
                    <p>No Apex sessions available. Parse some race data first to create sessions.</p>
                  </IonLabel>
                </IonItem>
              )}
            </>
          )}

          {/* Disabled state message */}
          {!useApexDatabase && (
            <IonItem>
              <IonLabel>
                <h3>Using Manual Data Entry</h3>
                <p>Enable Apex Database to use live race data from parsed sessions.</p>
              </IonLabel>
            </IonItem>
          )}
        </IonCardContent>
      </IonCard>

      {/* Error Alert */}
      <IonAlert
        isOpen={!!error}
        onDidDismiss={clearError}
        header="Error"
        message={error || ''}
        buttons={['OK']}
      />
    </>
  );
};

export default ApexDataControls;
