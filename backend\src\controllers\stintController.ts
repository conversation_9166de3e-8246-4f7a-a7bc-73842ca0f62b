// d:/Desktop/ManulilacRaceplanner/backend/src/controllers/stintController.ts
import { Request, Response } from 'express';
import mongoose, { isValidObjectId, Types } from 'mongoose';
    import StintModel, { IStint, StintCalculationInfo } from '../models/Stint';
import RaceStrategy from '../models/RaceStrategy';
import { sendErrorResponse } from '../utils/controllerUtils';
import { sendWebSocketMessage } from '../websocket/websocket';
import { calculateStints } from '../utils/stintCalculator';

/**
 * @description Update a single stint and recalculate subsequent stints
 * @route PUT /api/stint/:id
 * @access Private
 */
export const updateStint = async (req: Request, res: Response) => {
  const { id } = req.params;
  const { actualPitDuration, actualPitEntryTime, isUserModified } = req.body; // Expect actualPitEntryTime as number

  try {
    // Find the stint and associated strategy
    const stintToUpdate = await StintModel.findById(id);
    if (!stintToUpdate) return sendErrorResponse(res, `Stint not found with ID: ${id}`, 404);
    
    const strategy = await RaceStrategy.findById(stintToUpdate.raceStrategyId);
    if (!strategy) return sendErrorResponse(res, `Strategy not found for stint ID: ${id}`, 404);

    // Prepare update data
    const updateData: Partial<IStint> = { isUserModified: isUserModified || false };

    // Handle actual pit entry time and duration
    // Check if actualPitEntryTime is provided as a number
    if (typeof actualPitEntryTime === 'number' && actualPitDuration !== undefined) {
      try {
        // Ensure the stint has a valid start time for duration calculation
        if (stintToUpdate.startTime === undefined || stintToUpdate.startTime === null) {
             return sendErrorResponse(res, `Cannot save actuals for stint ${stintToUpdate.stintNumber}: Stint start time is not defined.`, 400);
        }

        const actualEntryTimeSeconds = actualPitEntryTime; // Use the number directly
        
        // Calculate the actual duration of *this* stint based on the provided entry time
        const calculatedActualDuration = actualEntryTimeSeconds - stintToUpdate.startTime;

        // Validate calculated actual duration against minimum stint time *before* saving
        if (calculatedActualDuration < strategy.minStintTimeSeconds - 1) {
          return sendErrorResponse(res, `User-modified stint ${stintToUpdate.stintNumber} actual duration (${calculatedActualDuration.toFixed(2)}s) is less than minimum (${strategy.minStintTimeSeconds}s).`, 400);
        }

        // If validation passes, store the actual durations and entry time
        updateData.actualPitDuration = actualPitDuration;
        updateData.actualPitEntryTime = actualEntryTimeSeconds; // Store the number
      } catch (parseError) {
        // This catch block might be less relevant now that we expect a number,
        // but keep it for unexpected errors during processing.
        return sendErrorResponse(res, `Error processing actual times: ${parseError instanceof Error ? parseError.message : 'Unknown error'}`, 400);
      }
    }

    // Update the stint
    const updatedStint = await StintModel.findByIdAndUpdate(id, updateData, { new: true });
    if (!updatedStint) return sendErrorResponse(res, `Failed to update stint ${id}`, 500);

    // After updating the stint, recalculate all stints
    const strategyId = (strategy._id as mongoose.Types.ObjectId).toString(); // Explicitly cast to ObjectId to resolve 'unknown' type error

    // Trigger recalculation of all stints
    const { calculateStints } = require('../utils/stintCalculator');
    const allStints = await StintModel.find({ raceStrategyId: strategyId }).lean();
    
    const { calculatedStints, error } = calculateStints({
      raceDurationValue: strategy.raceDurationValue,
      raceDurationType: strategy.raceDurationType,
      avgLapTimeSeconds: strategy.avgLapTimeSeconds,
      minPitDurationSeconds: strategy.minPitDurationSeconds,
      mandatoryPitStops: strategy.mandatoryPitStops,
      minStintTimeSeconds: strategy.minStintTimeSeconds,
      maxStintTimeSeconds: strategy.maxStintTimeSeconds,
      pitWindowOpenValue: strategy.pitWindowOpenValue,
      pitWindowOpenType: strategy.pitWindowOpenType,
      pitWindowCloseValue: strategy.pitWindowCloseValue,
      pitWindowCloseType: strategy.pitWindowCloseType,
    }, allStints);

    if (error) {
      return sendErrorResponse(res, error, 400);
    }
    
    // Delete existing stints and save recalculated ones
    await StintModel.deleteMany({ raceStrategyId: strategyId });
    
    const stintDocsToSave = calculatedStints.map((stintInfo: StintCalculationInfo) => ({
      ...stintInfo,
      raceStrategyId: strategyId,
    })); // Use calculated stints which now include the correct actualStintDuration

    const savedStintDocs = await StintModel.insertMany(stintDocsToSave);

    // Update strategy with new stint IDs
    strategy.stints = savedStintDocs.map(doc => doc._id) as any;
    await strategy.save();

    // Emit WebSocket event
    sendWebSocketMessage({ 
      event: 'strategyUpdated', 
      payload: { ...strategy.toObject(), stints: savedStintDocs } 
    });
    
    res.status(200).json({
      message: 'Stint actuals saved and strategy recalculated',
      stint: updatedStint,
      stints: savedStintDocs
    });
  } catch (error) {
    return sendErrorResponse(res, `Server error: ${error instanceof Error ? error.message : 'Unknown error'}`, 500);
  }
};

/**
 * @description Get all stints for a strategy
 * @route GET /api/strategy/:id/stints
 * @access Public
 */
export const getStintsByStrategy = async (req: Request, res: Response) => {
  const { id: strategyId } = req.params;
  console.log(`[Stint Controller] GET /api/strategy/${strategyId}/stints - Fetching stints for strategy.`);

  try {
    // Validate strategyId format and ensure it's defined
    // Split the check to handle 'undefined' first, then validate format
    if (!strategyId) {
        return sendErrorResponse(res, 'Strategy ID is required.', 400);
    }
    if (!mongoose.Types.ObjectId.isValid(strategyId)) { // Now strategyId is guaranteed to be a string
        return sendErrorResponse(res, 'Invalid strategy ID format.', 400);
    }

    // Find the strategy to ensure it exists
    const strategy = await RaceStrategy.findById(strategyId);
    if (!strategy) {
        return sendErrorResponse(res, 'Race strategy not found.', 404);
    }

    // Fetch and sort the stints
    const stints = await StintModel.find({ raceStrategyId: strategyId }).sort('stintNumber').lean(); // Use lean() for performance

    console.log(`[Stint Controller] Found ${stints.length} stints for strategy ${strategyId}.`);
    res.status(200).json(stints);
  } catch (error) {
    console.error(`[Stint Controller] Error fetching stints for strategy ${strategyId}:`, error);
    sendErrorResponse(res, 'Failed to fetch stints.', 500, error);
  }
};

// Get a stint by ID
export const getStintById = async (req: Request, res: Response) => {
  const { id } = req.params;
  
  try {
    // Validate the stint ID
    if (!isValidObjectId(id)) {
      return sendErrorResponse(res, `Invalid stint ID: ${id}`, 400);
    }
    
    const stint = await StintModel.findById(id);
    
    if (!stint) {
      return sendErrorResponse(res, `Stint not found with ID: ${id}`, 404);
    }
    
    res.status(200).json(stint);
  } catch (error) {
    return sendErrorResponse(res, `Server error: ${error instanceof Error ? error.message : 'Unknown error'}`, 500);
  }
};

// Update the updateStintActuals function to calculate and store differences
export const updateStintActuals = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const { actualPitEntryTime, actualPitDuration } = req.body;

    // Validate input
    if (!isValidObjectId(id)) {
      return sendErrorResponse(res, 'Invalid stint ID format', 400);
    }

    // Find the stint
    const stint = await StintModel.findById(id);
    if (!stint) {
      return sendErrorResponse(res, 'Stint not found', 404);
    }

    // Update the stint with actual values
    stint.actualPitEntryTime = actualPitEntryTime;
    stint.actualPitDuration = actualPitDuration;
    
    // Calculate actual stint duration from actual pit entry time
    stint.actualStintDuration = actualPitEntryTime - stint.startTime;
    
    // Store planned values for comparison
    stint.plannedStintDuration = stint.duration;
    if (stint.pitEndTime && stint.endTime) {
      stint.plannedPitDuration = stint.pitEndTime - stint.endTime;
    }
    
    // Calculate differences
    if (stint.actualStintDuration && stint.plannedStintDuration) {
      stint.stintDurationDiff = stint.actualStintDuration - stint.plannedStintDuration;
    }
    
    if (stint.actualPitDuration && stint.plannedPitDuration) {
      stint.pitDurationDiff = stint.actualPitDuration - stint.plannedPitDuration;
    }

    // Save the updated stint
    await stint.save();

    // Emit WebSocket event
    sendWebSocketMessage({ 
      event: 'stintUpdated', 
      payload: { stint: stint.toObject() } 
    });

    res.status(200).json({ 
      message: 'Stint actuals updated successfully',
      stint: stint.toObject()
    });
  } catch (error) {
    sendErrorResponse(res, 'Failed to update stint actuals', 500, error);
  }
};


