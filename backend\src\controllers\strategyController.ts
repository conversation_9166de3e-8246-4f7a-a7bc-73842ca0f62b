// d:/Desktop/ManulilacRaceplanner/backend/src/controllers/strategyController.ts
import { Request, Response } from 'express';
import mongoose, { Types } from 'mongoose';
import RaceStrategy, { IRaceStrategy } from '../models/RaceStrategy';
import StintModel, { IStint, StintCalculationInfo } from '../models/Stint';
import { sendWebSocketMessage } from '../websocket/websocket';
import { sendErrorResponse, isValidObjectId } from '../utils/controllerUtils';
import { calculateStints } from '../utils/stintCalculator'; 
// Default strategy values
const DEFAULT_STRATEGY = {
    raceName: 'Default Race',
    raceDurationValue: 6 * 60 * 60, // 6 hours in seconds
    raceDurationType: 'time',
    mandatoryPitStops: 8,
    minStintTimeSeconds: 20 * 60, // 20 minutes
    maxStintTimeSeconds: 65 * 60, // 65 minutes
    avgLapTimeSeconds: 90, // 1:30 per lap
    pitStopDurationSeconds: 120, // 2 minutes
    pitWindowOpenValue: 20 * 60, // 20 minutes
    pitWindowOpenType: 'time',
    pitWindowCloseValue: 5.5 * 60 * 60, // 5.5 hours
    pitWindowCloseType: 'time',
    startTime: null, // Race hasn't started
    isActive: false
};

/**
 * @description Get the current race strategy
 * @route GET /api/strategy
 * @access Public
 */
export const getStrategy = async (req: Request, res: Response) => {
    console.log('[Strategy Controller] GET /api/strategy - Attempting to fetch active strategy.');
    try {
        let strategy = await RaceStrategy.findOne({ isActive: true });
        
        if (!strategy) {
            // If no active strategy exists, get the most recent one
            strategy = await RaceStrategy.findOne().sort({ createdAt: -1 });
            console.log('[Strategy Controller] No active strategy found.');
            
            if (!strategy) {
                // If no strategy exists at all, create the default one
                console.log('[Strategy Controller] No strategies found, creating default.');
                strategy = await RaceStrategy.create({
                    ...DEFAULT_STRATEGY,
                    isActive: true
                });
                console.log(`[Strategy Controller] Default strategy created with ID: ${strategy._id}`);
            } else {
                // Set the found strategy as active
                strategy.isActive = true;
                await strategy.save();
                console.log(`[Strategy Controller] Found strategy ID ${strategy._id} and set it as active.`);
            }
        }
        
        // Stints are calculated and saved via the /calculate-stints endpoint, triggered by the frontend if needed.
        
        // Populate stints for the response
        const populatedStrategy = await RaceStrategy.findById(strategy._id).populate('stints');
        
        if (!populatedStrategy) {
            return sendErrorResponse(res, 'Failed to populate strategy with stints.', 500);
        }
        
        console.log(`[Strategy Controller] Returning strategy with ${populatedStrategy.stints?.length || 0} stints`);
        
        res.status(200).json(populatedStrategy);
    } catch (error) {
        sendErrorResponse(res, 'Failed to fetch race strategy.', 500, error);
    }
};

/**
 * @description Update the current race strategy
 * @route PUT /api/strategy
 * @access Private (consider adding auth middleware later)
 */
export const updateStrategy = async (req: Request, res: Response) => {
    console.log('[Strategy Controller] PUT /api/strategy - Attempting to update strategy.');
    try {
        // Find the single strategy document and update it, or create if it doesn't exist (upsert)
        const updatedStrategy = await RaceStrategy.findOneAndUpdate({}, req.body, {
            new: true, // Return the updated document
            upsert: true, // Create if it doesn't exist
            runValidators: true, // Ensure schema validation runs on update
        });

        if (!updatedStrategy) {
             // Should not happen with upsert: true, but good practice to handle
             return sendErrorResponse(res, 'Could not find or update strategy.', 404);
             console.warn('[Strategy Controller] updateStrategy failed to return updated document despite upsert:true.');
        }
        console.log(`[Strategy Controller] Strategy updated/created with ID: ${updatedStrategy._id}`);

        // Emit WebSocket event after successful update
        // Use the correct function and format the message as an object
        sendWebSocketMessage({ event: 'strategyUpdated', payload: updatedStrategy.toObject() }); // Send plain object

        res.status(200).json(updatedStrategy);
    } catch (error) {
        // Handle validation errors specifically if needed
        if (error instanceof Error && error.name === 'ValidationError') {
             return sendErrorResponse(res, `Validation Error: ${error.message}`, 400, error);
        }
        sendErrorResponse(res, 'Failed to update race strategy.', 500, error);
    }
};

/**
 * @description Reset strategy to default values
 * @route POST /api/strategy/reset
 * @access Private
 */
export const resetStrategyToDefaults = async (req: Request, res: Response) => {
    console.log('[Strategy Controller] POST /api/strategy/reset - Attempting to reset strategy to defaults.');
    try {
        // Find the single strategy document and update it with defaults, or create if it doesn't exist
        const resetStrategy = await RaceStrategy.findOneAndUpdate(
            {}, 
            DEFAULT_STRATEGY,
            {
                new: true,
                upsert: true,
                runValidators: true,
            }
        );

        if (!resetStrategy) {
            console.error('[Strategy Controller] resetStrategyToDefaults failed to return updated document despite upsert:true.');
            return sendErrorResponse(res, 'Could not reset strategy to defaults.', 500);
        }
        console.log(`[Strategy Controller] Strategy reset to defaults with ID: ${resetStrategy._id}`);

        // Emit WebSocket event after successful reset
        sendWebSocketMessage({ event: 'strategyUpdated', payload: resetStrategy.toObject() }); // Send plain object

        res.status(200).json(resetStrategy);
    } catch (error) {
        sendErrorResponse(res, 'Failed to reset strategy to defaults.', 500, error);
    }
};

/**
 * @description Start the race by setting the start time
 * @route POST /api/strategy/start
 * @access Private
 */
export const startRace = async (req: Request, res: Response) => {
    console.log('[Strategy Controller] POST /api/strategy/start - Attempting to start race.');
    try {
        const startTime = req.body.startTime || new Date().toISOString();
        
        // Find the strategy and update the start time and active status
        const updatedStrategy = await RaceStrategy.findOneAndUpdate(
            {},
            { 
                startTime,
                isActive: true 
            },
            {
                new: true,
                upsert: true,
                runValidators: true,
            }
        );

        if (!updatedStrategy) {
            console.error('[Strategy Controller] startRace failed to return updated document despite upsert:true.');
            return sendErrorResponse(res, 'Could not start the race.', 500);
        }
        console.log(`[Strategy Controller] Race started for strategy ID: ${updatedStrategy._id} at ${updatedStrategy.startTime}`);

        // Emit WebSocket event after successfully starting the race
        sendWebSocketMessage({ event: 'strategyUpdated', payload: updatedStrategy.toObject() }); // Send plain object

        res.status(200).json(updatedStrategy);
    } catch (error) {
        sendErrorResponse(res, 'Failed to start the race.', 500, error);
    }
};

// Note: The createDefaultStrategyIfNeeded function was moved to seedData.ts
// It's called from index.ts after DB connection.

/**
 * @description Get all race strategies
 * @route GET /api/strategy/all
 * @access Public
 */
export const getAllStrategies = async (req: Request, res: Response) => {
  console.log('[Strategy Controller] GET /api/strategy/all - Attempting to fetch all strategies.');
  try {
    const strategies = await RaceStrategy.find().sort({ createdAt: -1 });
    res.status(200).json(strategies);
  } catch (error) {
    sendErrorResponse(res, 'Failed to fetch race strategies.', 500, error);
  }
};

/**
 * @description Create a new race strategy
 * @route POST /api/strategy
 * @access Private
 */
export const createStrategy = async (req: Request, res: Response) => {
  console.log('[Strategy Controller] POST /api/strategy - Attempting to create a new strategy.');
  try {
    // Check if this should be the active strategy
    const makeActive = req.body.isActive || false;
    
    // If making this active, deactivate all others (handled by pre-save hook)
    const newStrategy = new RaceStrategy({
      ...req.body,
      isActive: makeActive,
    });
    
    const savedStrategy = await newStrategy.save();
    console.log(`[Strategy Controller] New strategy created with ID: ${savedStrategy._id}`);
    
    // Emit WebSocket event after successful creation
    sendWebSocketMessage({ event: 'strategyCreated', payload: savedStrategy });

    res.status(201).json(savedStrategy);
  } catch (error) {
    if (error instanceof Error && error.name === 'ValidationError') {
      return sendErrorResponse(res, `Validation Error: ${error.message}`, 400, error);
    }
    sendErrorResponse(res, 'Failed to create race strategy.', 500, error);
  }
};

/**
 * @description Get a specific strategy by ID
 * @route GET /api/strategy/:id
 * @access Public
 */
export const getStrategyById = async (req: Request, res: Response) => {
  console.log(`[Strategy Controller] GET /api/strategy/${req.params.id} - Attempting to fetch strategy by ID.`);
  try {
    const strategy = await RaceStrategy.findById(req.params.id);
    
    if (!strategy) {
      return sendErrorResponse(res, 'Strategy not found.', 404);
      console.warn(`[Strategy Controller] Strategy with ID ${req.params.id} not found.`);
    }
    
    res.status(200).json(strategy);
  } catch (error) {
    sendErrorResponse(res, 'Failed to fetch strategy.', 500, error);
  }
};

/**
 * @description Delete a strategy
 * @route DELETE /api/strategy/:id
 * @access Private
 */
export const deleteStrategy = async (req: Request, res: Response) => {
  console.log(`[Strategy Controller] DELETE /api/strategy/${req.params.id} - Attempting to delete strategy by ID.`);
  try {
    const strategy = await RaceStrategy.findById(req.params.id);
    
    if (!strategy) {
      return sendErrorResponse(res, 'Strategy not found.', 404);
      console.warn(`[Strategy Controller] Strategy with ID ${req.params.id} not found for deletion.`);
    }
    
    // Don't allow deleting the active strategy
    if (strategy.isActive) {
      return sendErrorResponse(res, 'Cannot delete the active strategy.', 400);
      console.warn(`[Strategy Controller] Attempted to delete active strategy ID ${req.params.id}.`);
    }
    
    // Use deleteOne instead of remove (which is deprecated)
    await RaceStrategy.deleteOne({ _id: strategy._id });
    
    // Emit WebSocket event after successful deletion
    sendWebSocketMessage({ event: 'strategyDeleted', payload: { id: req.params.id } });
    console.log(`[Strategy Controller] Strategy ID ${req.params.id} deleted.`);
    
    res.status(200).json({ message: 'Strategy deleted successfully.' });
  } catch (error) {
    sendErrorResponse(res, 'Failed to delete strategy.', 500, error);
  }
};

/**
 * @description Set a strategy as active
 * @route PUT /api/strategy/:id/activate
 * @access Private
 */
export const activateStrategy = async (req: Request, res: Response) => {
  console.log(`[Strategy Controller] PUT /api/strategy/${req.params.id}/activate - Attempting to activate strategy by ID.`);
  try {
    // Find the strategy to activate
    const strategy = await RaceStrategy.findById(req.params.id);
    
    if (!strategy) {
      return sendErrorResponse(res, 'Strategy not found.', 404);
      console.warn(`[Strategy Controller] Strategy with ID ${req.params.id} not found for activation.`);
    }
    
    // Set this strategy as active (will deactivate others via pre-save hook)
    console.log(`[Strategy Controller] Activating strategy ID: ${strategy._id}`);
    strategy.isActive = true;
    await strategy.save();
    
    // Emit WebSocket event after successful activation
    sendWebSocketMessage({ event: 'strategyUpdated', payload: strategy });
    
    res.status(200).json(strategy);
  } catch (error) {
    sendErrorResponse(res, 'Failed to activate strategy.', 500, error);
  }
};

/**
 * @description Duplicate a strategy
 * @route POST /api/strategy/:id/duplicate
 * @access Private
 */
export const duplicateStrategy = async (req: Request, res: Response) => {
  console.log(`[Strategy Controller] POST /api/strategy/${req.params.id}/duplicate - Attempting to duplicate strategy by ID.`);
  try {
    // Find the strategy to duplicate
    const sourceStrategy = await RaceStrategy.findById(req.params.id);
    
    if (!sourceStrategy) {
      console.warn(`[Strategy Controller] Strategy with ID ${req.params.id} not found for duplication.`);
      return sendErrorResponse(res, 'Strategy not found.', 404);
    }
    
    // Convert to plain object and create a new strategy with modified data
    const duplicateData = sourceStrategy.toObject();
    
    // Create a new strategy document
    const newStrategy = new RaceStrategy({
      raceName: `${duplicateData.raceName} (Copy)`,
      raceDurationValue: duplicateData.raceDurationValue,
      raceDurationType: duplicateData.raceDurationType,
      mandatoryPitStops: duplicateData.mandatoryPitStops,
      minStintTimeSeconds: duplicateData.minStintTimeSeconds,
      maxStintTimeSeconds: duplicateData.maxStintTimeSeconds,
      avgLapTimeSeconds: duplicateData.avgLapTimeSeconds,
      minPitDurationSeconds: duplicateData.minPitDurationSeconds,
      pitWindowOpenValue: duplicateData.pitWindowOpenValue,
      pitWindowOpenType: duplicateData.pitWindowOpenType,
      pitWindowCloseValue: duplicateData.pitWindowCloseValue,
      pitWindowCloseType: duplicateData.pitWindowCloseType,
      isActive: false,
      startTime: undefined
    });
    
    const savedStrategy = await newStrategy.save();
    console.log(`[Strategy Controller] Strategy ID ${sourceStrategy._id} duplicated to new strategy ID: ${savedStrategy._id}`);
    
    // Emit WebSocket event after successful duplication
    sendWebSocketMessage({ event: 'strategyCreated', payload: savedStrategy });
    
    res.status(201).json(savedStrategy);
  } catch (error) {
    sendErrorResponse(res, 'Failed to duplicate strategy.', 500, error);
  }
};

/**
 * @description Calculate and save stints for a strategy
 * @route POST /api/strategy/:id/calculate-stints
 * @access Public
 */
export const calculateAndSaveStints = async (req: Request, res: Response) => {
  const { id } = req.params;
  const { modifiedStints } = req.body;

  console.log(`[Strategy Controller] POST /api/strategy/${id}/calculate-stints - Attempting to calculate and save stints.`);

  try {
    // Validate strategy ID
    if (!id || !isValidObjectId(id)) {
      return sendErrorResponse(res, `Invalid strategy ID: ${id}`, 400);
    }

    // Find the strategy
    const strategy = await RaceStrategy.findById(id).populate('stints');
    if (!strategy) {
      return sendErrorResponse(res, `Strategy not found with ID: ${id}`, 404);
    }

    // Fetch existing stints from the database
    // If modifiedStints are provided in the body, use those. Otherwise, fetch from DB.
    let stintsToCalculate = modifiedStints || await StintModel.find({ raceStrategyId: id }).sort({ stintNumber: 1 }).lean();

    // --- IMPORTANT: Clear user-modified actuals for recalculation ---
    // When recalculating, we want to ignore any previously saved actual times/durations.
    // Iterate through the stints and reset the user-modified flags and actuals.
    stintsToCalculate = stintsToCalculate.map((stint: StintCalculationInfo | IStint) => ({
        ...stint,
        isUserModified: false, // Mark as not user modified
        actualPitEntryTime: undefined, // Clear actual entry time
        actualPitDuration: undefined, // Clear actual pit duration
    }));

    const { calculatedStints, error } = calculateStints({
      raceDurationValue: strategy.raceDurationValue,
      raceDurationType: strategy.raceDurationType,
      avgLapTimeSeconds: strategy.avgLapTimeSeconds,
      minPitDurationSeconds: strategy.minPitDurationSeconds,
      mandatoryPitStops: strategy.mandatoryPitStops,
      minStintTimeSeconds: strategy.minStintTimeSeconds,
      maxStintTimeSeconds: strategy.maxStintTimeSeconds,
      pitWindowOpenValue: strategy.pitWindowOpenValue,
      pitWindowOpenType: strategy.pitWindowOpenType,
      pitWindowCloseValue: strategy.pitWindowCloseValue,
      pitWindowCloseType: strategy.pitWindowCloseType,
    }, stintsToCalculate); // Pass the potentially modified list to the calculator

    if (error) {
      return sendErrorResponse(res, error, 400);
    }

    // Delete existing stints for this strategy to prevent duplicates
    await StintModel.deleteMany({ raceStrategyId: id });

    // Create and save new Stint documents
    const stintDocsToSave = calculatedStints.map((stintInfo: StintCalculationInfo) => ({ // Explicitly type stintInfo
      ...stintInfo,
      raceStrategyId: id,
    }));

    const savedStintDocs = await StintModel.insertMany(stintDocsToSave);

    // Update the RaceStrategy with the new stint IDs
    const savedStintIds = savedStintDocs.map(doc => doc._id);
    strategy.stints = savedStintIds as any;
    await strategy.save();

    // Emit WebSocket event after successful calculation
    sendWebSocketMessage({ event: 'strategyUpdated', payload: { ...strategy.toObject(), stints: savedStintDocs } });

    res.status(200).json({
      message: 'Stints calculated and saved successfully',
      strategy: {
        ...strategy.toObject(),
        stints: savedStintDocs
      }
    });
  } catch (error) {
    sendErrorResponse(res, 'Failed to calculate and save stints.', 500, error);
  }
};

/**
 * @description Stop the current race
 * @route POST /api/strategy/stop
 * @access Public
 */
export const stopRace = async (req: Request, res: Response) => {
  try {
    const strategy = await RaceStrategy.findOne({ isActive: true });
    
    if (!strategy) {
      return sendErrorResponse(res, 'No active strategy found.', 404);
    }
    
    // Update the strategy to mark it as inactive and clear the start time
    strategy.isActive = false;
    strategy.startTime = undefined; // Explicitly set startTime to undefined
    await strategy.save();
    
    // Emit WebSocket event after successfully stopping the race
    sendWebSocketMessage({ event: 'strategyUpdated', payload: strategy.toObject() });
    
    res.status(200).json({ 
      message: 'Race stopped successfully',
      strategy
    });
  } catch (error) {
    sendErrorResponse(res, 'Failed to stop race.', 500, error);
  }
};
