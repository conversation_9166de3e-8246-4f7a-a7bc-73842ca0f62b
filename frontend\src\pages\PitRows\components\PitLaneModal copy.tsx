// d:\Downloads\Nuova cartella\my-app\frontend\src\pages\PitRows\components\PitLaneModal.tsx
import React, { useState } from "react";
import {
  IonModal,
  IonHeader,
  IonToolbar,
  IonTitle,
  IonButton,
  IonIcon,
  IonButtons,
  IonContent,
  IonList,
  IonItem,
  IonLabel,
  IonSegment,
  IonSegmentButton,
  // IonRippleEffect, // Removed if not explicitly needed on items
  IonFooter,
  IonBadge,
  IonText,
  // IonGrid, // Using IonList/IonItem now
  IonRow,
  IonCol,
} from "@ionic/react";
import { closeCircleOutline, helpCircleOutline } from "ionicons/icons";

import "./PitLaneModal.css"

import { Kart, Team } from "../../../types";

interface PitLaneModalProps {
  isOpen: boolean;
  onClose: () => void;
  teams: Team[]; // NEW: Add teams prop
  pitLaneKarts: Kart[];
  maintenanceKarts: Kart[];
  availableKarts: Kart[];
  onTeamSelect: (team: Team | null) => void; // NEW: Handler for team selection
  onPitKartSelect: (kart: Kart | null) => void; // RENAMED: Handler for pit/maintenance kart selection
  targetRowNumber?: number; // Optional: For modal title context
}

// Define the possible segment values
type ActiveSegmentType = 'teams' | 'pitlane' | 'maintenance' | 'available';

const PitLaneModal: React.FC<PitLaneModalProps> = ({
  isOpen,
  onClose,
  teams, // Destructure new prop
  pitLaneKarts,
  maintenanceKarts,
  availableKarts,
  onTeamSelect, // Destructure new handler
  onPitKartSelect, // Destructure renamed handler
  targetRowNumber,
}) => {
  // Default to 'teams' tab
  const [activeSegment, setActiveSegment] = useState<ActiveSegmentType>('teams');

  // Function to render list of Karts (Pit Lane / Maintenance)
  const renderKartList = (karts: Kart[]) => (
    <IonList className="kart-list" lines="full">
      {karts.length === 0 && <IonItem>No karts found.</IonItem>}
      {karts.map((kart) => (
        <IonItem
          key={kart._id} // Use kart._id as key
          button
          detail={false}
          className="kart-item ion-activatable ripple-parent"
          onClick={() => onPitKartSelect(kart)} // Use the specific handler
        >
          {/* Adjust columns as needed */}
          <IonBadge color="secondary" slot="start">{kart.number}</IonBadge>
        </IonItem>
      ))}
    </IonList>
  );

  // NEW: Function to render list of Teams
  const renderTeamList = (teamList: Team[]) => (
    <IonList className="kart-list" lines="full">
       {teamList.length === 0 && <IonItem>No teams found.</IonItem>}
       {teamList.map((team) => (
        <IonItem
          key={team._id} // Use team._id as key
          button
          detail={false}
          className="kart-item ion-activatable ripple-parent"
          onClick={() => onTeamSelect(team)} // Use the team select handler
        >
          <IonBadge color="secondary" slot="start">{team.number}</IonBadge>
          <IonLabel>
            <h2>{team.name}</h2>
            {/* Optionally show current kart number if available */}
            <p>
                {typeof team.currentKartId === 'object' && team.currentKartId !== null
                    ? `Current Kart: ${team.currentKartId.number}`
                    : 'No Kart Assigned'}
            </p>
          </IonLabel>
        </IonItem>
      ))}
    </IonList>
  );


  return (
    <IonModal isOpen={isOpen} onDidDismiss={onClose} className="pit-lane-modal">
      <IonHeader>
        <IonToolbar color="primary">
           {/* Add target row number to title if available */}
          <IonTitle>
            Select Kart {targetRowNumber ? `for Row ${targetRowNumber}` : ''}
          </IonTitle>
          <IonButtons slot="primary">
            <IonButton fill="clear" onClick={onClose}>
              <IonIcon icon={closeCircleOutline} />
            </IonButton>
          </IonButtons>
        </IonToolbar>
      </IonHeader>

      {/* Segment Buttons */}
      <IonToolbar>
        <IonSegment
            value={activeSegment}
            onIonChange={(e) => setActiveSegment(e.detail.value as ActiveSegmentType)}
            scrollable // Add if many segments
        >
          {/* NEW: Teams Segment */}
          <IonSegmentButton value="teams">
            <IonLabel>Teams</IonLabel>
          </IonSegmentButton>
          <IonSegmentButton value="pitlane">
            <IonLabel>Pit Lane</IonLabel>
          </IonSegmentButton>
          <IonSegmentButton value="maintenance">
            <IonLabel>Maintenance</IonLabel>
          </IonSegmentButton>
          <IonSegmentButton value="available">
            <IonLabel>Available</IonLabel>
          </IonSegmentButton>
        </IonSegment>
      </IonToolbar>

      {/* Content based on active segment */}
      <IonContent>
        {activeSegment === 'teams' && renderTeamList(teams)}
        {/*activeSegment === 'pitlane' && renderKartList(pitLaneKarts)*/}
        {activeSegment === 'maintenance' && renderKartList(maintenanceKarts)}
        {activeSegment === 'available' && renderKartList(availableKarts)}
      </IonContent>

      {/* Footer Button - Decide if "Unknown" makes sense for Team Swap */}
      {/* Maybe disable or hide it when 'teams' tab is active? */}
      <IonFooter>
        <IonButton
          expand="block"
          color="medium"
          className="unknown-kart-btn"
          // Decide action for "Unknown" - maybe only relevant for pit/maintenance?
          // onClick={() => onPitKartSelect(null)} // Or onTeamSelect(null)? Needs clarification.
          onClick={() => activeSegment === 'teams' ? onTeamSelect(null) : onPitKartSelect(null)} // Example: Call appropriate handler
          disabled={activeSegment === 'teams'} // Example: Disable for teams tab
        >
          <IonIcon icon={helpCircleOutline} slot="start" />
          Unknown / Clear
        </IonButton>
      </IonFooter>
    </IonModal>
  );
};

export default PitLaneModal;
