{"name": "manulilac-raceplanner", "version": "0.0.1", "type": "module", "scripts": {"start": "vite", "build": "tsc && vite build", "preview": "vite preview", "check-types": "tsc", "test": "vitest", "coverage": "vitest run --coverage"}, "dependencies": {"@ionic/react": "^8.5.5", "@reduxjs/toolkit": "^2.2.1", "@tailus/themer": "^0.1.8", "@tailwindcss/vite": "^4.0.17", "@tanstack/react-query": "^5.74.4", "axios": "^1.8.4", "date-fns": "^4.1.0", "lucide-react": "^0.479.0", "preline": "^3.0.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-redux": "^9.1.0", "react-router-dom": "^6.30.0", "tailwind-merge": "^3.0.2", "vite-plugin-svgr": "^4.2.0"}, "devDependencies": {"@tailwindcss/forms": "^0.5.10", "@tanstack/eslint-plugin-query": "^5.51.15", "@testing-library/jest-dom": "^6.4.2", "@testing-library/react": "^14.2.1", "@types/react": "^18.3.20", "@types/react-dom": "^18.3.6", "@typescript-eslint/eslint-plugin": "^7.1.1", "@typescript-eslint/parser": "^7.1.1", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.18", "eslint": "^8.57.0", "eslint-plugin-react": "^7.34.0", "jsdom": "^24.0.0", "postcss": "^8.4.35", "prettier": "3.2.5", "tailwindcss": "^3.4.17", "typescript": "^5.4.2", "vite": "^6.3.2", "vitest": "^3.1.2"}}