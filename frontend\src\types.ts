/**
 * Represents the structure of Kart data fetched from the API.
 */
export interface Kart {
  _id: string;
  number: number;
  tablet?: number; // Optional tablet number
  speed?: number; // Optional speed
  status: "on_track" | "in_pit_row" | "maintenance" | "retired" | "available";
  // Populated fields (adjust based on your API's population)
  currentTeamId?: Pick<Team, "_id" | "name" | "number"> | null; // Example: Populated Team subset
  currentRowId?: Pick<Row, "_id" | "rowNumber" | "color"> | null; // Example: Populated Row subset
  // Add other fields returned by GET /api/karts
  createdAt?: string;
  updatedAt?: string;
}

/**
 * Represents the structure of Team data fetched from the API.
 */
export interface Team {
  _id: string;
  name: string;
  number: number;
  // Populated fields (adjust based on your API's population)
  currentKartId?: Pick<Kart, "_id" | "number"> | null; // Example: Populated Kart subset
  pastKarts?: string[]; // Array of Kart IDs
  // Add other fields returned by GET /api/teams
  createdAt?: string;
  updatedAt?: string;
}

/**
 * Represents the structure of Row data fetched from the API.
 */
export interface Row {
  _id: string;
  rowNumber: number;
  color: string;
  // Populated fields (adjust based on your API's population)
  currentKartId?: Pick<
    Kart,
    "_id" | "number" /* Add previousTeamId if populated here */
  > | null; // Example: Populated Kart subset
  pastKarts?: string[]; // Array of Kart IDs
  // Add other fields returned by GET /api/rows
  createdAt?: string;
  updatedAt?: string;
}

/**
 * Represents the structure of AssignmentLog data fetched from the API.
 */
export interface AssignmentLog {
  _id: string;
  timestamp: string;
  eventType:
    | "ASSIGNED_TO_ROW"
    | "ASSIGNED_TO_TEAM"
    | "SWAP_TEAM_ROW"
    | "UNASSIGNED_FROM_ROW"
    | "UNASSIGNED_FROM_TEAM"
    | "COMPENSATION";
  // Populated fields
  kartId: Pick<Kart, "_id" | "number">;
  teamId?: Pick<Team, "_id" | "name" | "number"> | null;
  rowId?: Pick<Row, "_id" | "rowNumber" | "color"> | null;
  previousTeamId?: Pick<Team, "_id" | "name" | "number"> | null;
  previousRowId?: Pick<Row, "_id" | "rowNumber" | "color"> | null;
  // Compensation fields
  isInvalid?: boolean;
  reasonInvalid?: string;
  supersededByLogId?: string | null;
  isCompensation?: boolean;
  compensatesLogId?: string | null;
  // Timestamps
  createdAt?: string;
  updatedAt?: string;
}

// import { StintInfo } from './pages/RaceStrategy/types'; // StintInfo is defined in that file, not here.

export interface StrategyData {
  _id?: string;
  raceName: string;
  raceDurationValue: number;
  raceDurationType: 'laps' | 'time';
  mandatoryPitStops: number;
  minStintTimeSeconds: number;
  maxStintTimeSeconds: number;
  pitWindowOpenValue: number;
  pitWindowOpenType: 'laps' | 'time';
  pitWindowCloseValue: number;
  pitWindowCloseType: 'laps' | 'time';
  avgLapTimeSeconds: number;
  minPitDurationSeconds: number;
  startTime?: Date;
  isActive?: boolean;
  stints?: unknown[]; // Use 'unknown[]' for now since StintInfo from ./pages/RaceStrategy/types is not directly usable here due to import cycle potential
  drivers?: unknown[]; // Add drivers if it's part of your strategy data
}
