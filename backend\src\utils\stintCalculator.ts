// d:/Desktop/ManulilacRaceplanner/backend/src/utils/stintCalculator.ts
import { StintCalculationInfo, IStint } from '../models/Stint'; // Import IStint for type hinting

/**
 * Calculate stints for a race strategy, considering user-modified stints.
 *
 * The logic processes existing stints first. If a stint is marked `isUserModified`
 * AND has `actualPitEntryTime` and `actualPitDuration`, it uses these actuals
 * to determine the start time of the next stint.
 * Once a stint is encountered that is NOT user-modified (or lacks actuals),
 * the calculator switches to automatic calculation for that stint and all subsequent ones,
 * distributing the remaining time and mandatory pit stops.
 */
export const calculateStints = (
  strategyParams: {
    raceDurationValue: number;
    raceDurationType: string;
    avgLapTimeSeconds: number;
    minPitDurationSeconds: number;
    mandatoryPitStops: number;
    minStintTimeSeconds: number;
    maxStintTimeSeconds: number;
    pitWindowOpenValue: number;
    pitWindowOpenType: string;
    pitWindowCloseValue: number;
    pitWindowCloseType: string;
  },
  allStintsFromDb: (StintCalculationInfo | IStint)[] = [] // Array of all stints from DB (can be Mongoose docs or lean objects)
): {
  calculatedStints: StintCalculationInfo[];
  error?: string;
} => {
  const {
    raceDurationValue,
    raceDurationType,
    avgLapTimeSeconds,
    minPitDurationSeconds,
    mandatoryPitStops,
    minStintTimeSeconds,
    maxStintTimeSeconds,
    pitWindowOpenValue,
    pitWindowOpenType,
    pitWindowCloseValue,
    pitWindowCloseType
  } = strategyParams;

  const totalRaceTimeSeconds = raceDurationType === 'time'
    ? raceDurationValue
    : raceDurationValue * avgLapTimeSeconds;
  const totalPitTimeSeconds = mandatoryPitStops * minPitDurationSeconds;
  const totalDrivingTimeSeconds = totalRaceTimeSeconds - totalPitTimeSeconds;
  const totalStints = mandatoryPitStops + 1;

  const epsilon = 1e-9; // Small value for floating point comparisons

  console.log('[StintCalculator] Received strategyParams:', JSON.stringify(strategyParams, null, 2));
  console.log('[StintCalculator] Received existing stints:', JSON.stringify(allStintsFromDb.map(s => ({
      _id: (s as any)._id, // Include ID for debugging
      stintNumber: s.stintNumber,
      isUserModified: s.isUserModified,
      actualPitEntryTime: s.actualPitEntryTime,
      actualPitDuration: s.actualPitDuration,
      startTime: s.startTime,
      endTime: s.endTime, // Log existing times for comparison
      duration: s.duration,
      pitEndTime: s.pitEndTime,
  })), null, 2));


  // Log initial calculated race times
  console.log(`[StintCalculator] totalRaceTimeSeconds: ${totalRaceTimeSeconds.toFixed(2)}, totalPitTimeSeconds: ${totalPitTimeSeconds.toFixed(2)}, totalDrivingTimeSeconds: ${totalDrivingTimeSeconds.toFixed(2)}, totalStints: ${totalStints}`);

  // Basic feasibility check: Is there enough driving time for all stints to meet minimums?
  if (totalDrivingTimeSeconds < (totalStints * minStintTimeSeconds) - epsilon && totalStints > 0) {
    console.error(`[StintCalculator] Feasibility check failed: totalDrivingTimeSeconds (${totalDrivingTimeSeconds.toFixed(2)}) < (totalStints (${totalStints}) * minStintTimeSeconds (${minStintTimeSeconds}))`);
    return {
      calculatedStints: [],
      error: 'Strategy impossible: Total driving time is insufficient for minimum stint durations.'
    };
  }

  // Calculate actual pit window times in seconds
  const actualPitWindowOpenTime = pitWindowOpenType === 'time'
    ? pitWindowOpenValue
    : pitWindowOpenValue * avgLapTimeSeconds;

  const actualPitWindowCloseTime = pitWindowCloseType === 'time'
    ? totalRaceTimeSeconds - pitWindowCloseValue // Corrected calculation
    : totalRaceTimeSeconds - (pitWindowCloseValue * avgLapTimeSeconds);

  // Log pit window times
  console.log(`[StintCalculator] actualPitWindowOpenTime: ${actualPitWindowOpenTime.toFixed(2)}, actualPitWindowCloseTime: ${actualPitWindowCloseTime.toFixed(2)}`);

  // --- Stint Calculation Logic ---
  const calculatedStintsInfo: StintCalculationInfo[] = [];

  // Initialize tracking variables
  let currentStintStart = 0;
  let currentStartLap = 0;
  let stintNumber = 1;
  let remainingPitStops = mandatoryPitStops;

  // Sort existing stints by number to ensure correct processing order
  const sortedExistingStints = [...allStintsFromDb].sort((a, b) => a.stintNumber - b.stintNumber);

  // Process existing stints up to the last user-modified one with actuals
  // We iterate through the provided list. If a stint is marked userModified AND has actual times,
  // we use those actuals to determine the start of the next stint.
  // Once we hit a stint that is NOT userModified (or lacks actual times),
  // we calculate that stint and all subsequent ones automatically.
  let recalculateFromIndex = sortedExistingStints.length; // Default: recalculate all if no existing or none are modified

  for (let i = 0; i < sortedExistingStints.length; i++) {
    const stint = sortedExistingStints[i];

      // Use actual times if provided and marked as user modified
      if (stint && stint.isUserModified && stint.actualPitEntryTime && stint.actualPitDuration !== null && stint.actualPitDuration !== undefined) {
        console.log(`[StintCalculator] Using actual times for stint ${stint.stintNumber} (index ${i})`);
        
        // Calculate actual duration and pit end based on actuals
        const actualEntryTimeSeconds = stint.actualPitEntryTime; // Use the number directly (seconds from race start)
        // Ensure actual entry time is not before the calculated start time of this stint
        // This check is also done in the controller, but good to have here too.
        if (actualEntryTimeSeconds < currentStintStart - epsilon) {
             return {
                 // The controller should prevent this, but if it happens, log it
                 calculatedStints: calculatedStintsInfo, // Return already processed stints
                 error: `User-modified stint ${stint?.stintNumber} actual pit entry time (${actualEntryTimeSeconds.toFixed(2)}s) is before its calculated start time (${currentStintStart.toFixed(2)}s).`
             };
        }

        const actualDuration = actualEntryTimeSeconds - currentStintStart; // Duration from the start of this stint
        const pitEndTime = actualEntryTimeSeconds + stint.actualPitDuration;
        const stintLaps = Math.ceil(actualDuration / avgLapTimeSeconds); // Laps based on actual duration

        // Validate actual duration against minimum
        if (actualDuration < minStintTimeSeconds - epsilon && mandatoryPitStops > 0) {
             console.error(`[StintCalculator] User-modified stint ${stint?.stintNumber} duration check failed: actualDuration (${actualDuration.toFixed(2)}) < minStintTimeSeconds (${minStintTimeSeconds})`);
             return {
                 calculatedStints: calculatedStintsInfo, // Return already processed stints
                 error: `User-modified stint ${stint?.stintNumber} actual duration (${actualDuration.toFixed(2)}s) is less than minimum (${minStintTimeSeconds}s).`
             };
        }

        // Add the user-modified stint info to the result list
        calculatedStintsInfo.push({
          stintNumber: stintNumber++,
          startTime: currentStintStart,
          endTime: currentStintStart + actualDuration,
          pitEndTime: remainingPitStops > 0 ? pitEndTime : null, // Only include pitEndTime if there are remaining mandatory pits
          duration: actualDuration,
          startLap: currentStartLap,
          endLap: currentStartLap + stintLaps,
          laps: stintLaps,
          // Pit window validity check for the pit stop *after* this stint (if it's not the last mandatory pit)
          isPitWindowValid: (remainingPitStops > 0) ? (actualEntryTimeSeconds >= actualPitWindowOpenTime - epsilon &&
                            actualEntryTimeSeconds <= actualPitWindowCloseTime + epsilon) : true, // Final stint has no pit window constraint
          isExtraStint: stint?.isExtraStint, // Keep original flag? Or recalculate? Let's keep for now.
          isUserModified: true, // Keep the modified flag
          actualPitEntryTime: stint.actualPitEntryTime, // Preserve actuals
          actualPitDuration: stint.actualPitDuration, // Preserve actuals
          actualStintDuration: actualDuration, // Store calculated actual duration
        });

        currentStintStart = pitEndTime; // Next stint starts after the actual pit stop
        currentStartLap += stintLaps;
        if (remainingPitStops > 0) remainingPitStops--;
        recalculateFromIndex = i + 1; // Automatic calculation starts from the next index

      } else {
        // This stint or a subsequent one is not user-modified (or lacks actuals),
        // calculate automatically from here.
        console.log(`[StintCalculator] Automatic calculation starting from stint ${stint?.stintNumber} (index ${i}).`);
        recalculateFromIndex = i;
        break; // Stop processing user-modified/existing stints
      }

      // Basic check to prevent infinite loops or going past race end
      // If the current stint start time is already at or past the total race time,
      // and we've processed all mandatory pits, we can stop.
      if (currentStintStart >= totalRaceTimeSeconds - epsilon && remainingPitStops <= 0) {
           console.warn('[StintCalculator] Current stint start time reached or exceeded total race time during user-modified stint processing. Stopping calculation.');
           remainingPitStops = 0; // Ensure no more pits are expected
           recalculateFromIndex = sortedExistingStints.length; // Stop automatic calculation
           break;
      }
  }

  // --- Calculate Remaining Stints Automatically ---
  // This logic needs to be adapted to calculate from `currentStintStart` onwards,
  // covering `remainingPitStops` + 1 stints within the time `totalRaceTimeSeconds - currentStintStart`.

  const remainingTime = totalRaceTimeSeconds - currentStintStart;
  const remainingStintsToCalculate = remainingPitStops + 1; // Remaining pits + final stint

  console.log(`[StintCalculator] Calculating remaining ${remainingStintsToCalculate} stints from ${currentStintStart.toFixed(2)}s with ${remainingPitStops} pits left.`);

  if (remainingStintsToCalculate <= 0) {
       // This happens if the last user-modified stint was the final one, or if mandatoryPits was 0
       // and no stints were modified. If mandatoryPits was 0, the initial check handles it.
       // If the last modified stint was the final one, we are done.
       if (Math.abs(currentStintStart - totalRaceTimeSeconds) > epsilon) {
            // If we finished processing user-modified stints but aren't exactly at race end,
            // and there are no more mandatory pits, the last user-modified stint
            // should ideally end exactly at totalRaceTimeSeconds.
            // This indicates a potential issue with the last user-modified stint's actual times.
            // For now, let's just log a warning.
            console.warn(`[StintCalculator] Finished processing user-modified stints, but current time (${currentStintStart.toFixed(2)}s) does not match total race time (${totalRaceTimeSeconds.toFixed(2)}s).`);
       }
       // No more stints to calculate automatically.

  } else if (remainingStintsToCalculate === 1) {
       // Calculate the single final stint
       const finalStintActualDuration = remainingTime;
       const finalStintActualLaps = Math.ceil(finalStintActualDuration / avgLapTimeSeconds);
       const finalDurationBasedOnLaps = finalStintActualLaps * avgLapTimeSeconds;

       // Validate final stint duration against minimum
       if (finalDurationBasedOnLaps < minStintTimeSeconds - epsilon && totalStints > 0) {
           console.error(`[StintCalculator] Final stint duration check failed: finalDurationBasedOnLaps (${finalDurationBasedOnLaps.toFixed(2)}) < minStintTimeSeconds (${minStintTimeSeconds})`);
           return {
               calculatedStints: calculatedStintsInfo, // Return already processed stints
               error: `Strategy impossible: Final stint duration based on laps (${finalDurationBasedOnLaps.toFixed(2)}s) is less than minimum.`
           };
       }
       // Adjust duration if it overruns the total race time (shouldn't happen if remainingTime is calculated correctly, but safety)
       const finalEndTime = currentStintStart + finalDurationBasedOnLaps;
       const adjustedFinalDuration = (finalEndTime > totalRaceTimeSeconds + epsilon) ? totalRaceTimeSeconds - currentStintStart : finalDurationBasedOnLaps;
       const adjustedFinalLaps = Math.ceil(adjustedFinalDuration / avgLapTimeSeconds);

       calculatedStintsInfo.push({
           stintNumber: stintNumber++,
           startTime: currentStintStart,
           endTime: totalRaceTimeSeconds, // End at race end
           pitEndTime: null, // Final stint has no pit stop after it
           duration: totalRaceTimeSeconds - currentStintStart,
           startLap: currentStartLap,
           endLap: Math.floor(totalRaceTimeSeconds / avgLapTimeSeconds),
           laps: Math.floor((totalRaceTimeSeconds - currentStintStart) / avgLapTimeSeconds),
           isPitWindowValid: true, // Final stint has no pit window constraint
           isExtraStint: false, // Last stint is never an extra stint
           isUserModified: false,
       });

  } else {
       // Calculate multiple remaining stints using the "Splash and Go" logic
       // First, determine how many full stints we can fit
       const remainingDrivingTime = remainingTime - (remainingPitStops * minPitDurationSeconds);
       
       // Calculate how many full stints we can fit
       const fullStintLaps = Math.floor(maxStintTimeSeconds / avgLapTimeSeconds);
       const fullStintDuration = fullStintLaps * avgLapTimeSeconds;
       
       // Calculate splash stint properties
       const splashStintLaps = Math.ceil(minStintTimeSeconds / avgLapTimeSeconds);
       const splashStintDuration = splashStintLaps * avgLapTimeSeconds;
       
       // Calculate how many full stints we can fit
       // We need at least one bridge stint between full and splash stints
       // and we need to leave enough time for the required number of splash stints
       const minBridgeStintDuration = minStintTimeSeconds;
       
       // Calculate number of full stints (N) we can fit
       // Formula: remainingDrivingTime = N*fullStintDuration + 1*bridgeStintDuration + (remainingStintsToCalculate-N-1)*splashStintDuration
       // Solving for N: N = (remainingDrivingTime - minBridgeStintDuration - (remainingStintsToCalculate-1-N)*splashStintDuration) / fullStintDuration
       // Simplified: N = (remainingDrivingTime - minBridgeStintDuration - (remainingStintsToCalculate-1)*splashStintDuration) / (fullStintDuration - splashStintDuration)
       
       let numFullStints = 0;
       if (fullStintDuration > splashStintDuration + epsilon) {
           numFullStints = Math.floor(
               (remainingDrivingTime - minBridgeStintDuration - (remainingStintsToCalculate-1)*splashStintDuration) / 
               (fullStintDuration - splashStintDuration)
           );
       }
       
       // Ensure numFullStints is not negative and not more than remainingPitStops
       numFullStints = Math.max(0, numFullStints);
       numFullStints = Math.min(remainingPitStops, numFullStints);
       
       // Calculate number of splash stints
       const numSplashStints = remainingStintsToCalculate - 1 - numFullStints;
       
       // Calculate bridge stint duration
       const bridgeStintDuration = remainingDrivingTime - (numFullStints * fullStintDuration) - (numSplashStints * splashStintDuration);
       const bridgeStintLaps = Math.floor(bridgeStintDuration / avgLapTimeSeconds);
       const actualBridgeStintDuration = bridgeStintLaps * avgLapTimeSeconds;
       
       // Generate full stints
       for (let i = 0; i < numFullStints; i++) {
           const stintEndTime = currentStintStart + fullStintDuration;
           const pitEndTime = stintEndTime + minPitDurationSeconds;
           
           calculatedStintsInfo.push({
               stintNumber: stintNumber++,
               startTime: currentStintStart,
               endTime: stintEndTime,
               pitEndTime: pitEndTime,
               duration: fullStintDuration,
               startLap: currentStartLap,
               endLap: currentStartLap + fullStintLaps,
               laps: fullStintLaps,
               isPitWindowValid: (stintEndTime >= actualPitWindowOpenTime - epsilon &&
                                 stintEndTime <= actualPitWindowCloseTime + epsilon),
               isExtraStint: false,
               isUserModified: false,
           });
           
           currentStintStart = pitEndTime;
           currentStartLap += fullStintLaps;
       }
       
       // Generate bridge stint
       if (numFullStints < remainingPitStops) {
           // Calculate how much time we have until the pit window closes
           const timeUntilWindowClose = actualPitWindowCloseTime - currentStintStart;
           
           // Calculate how much time is needed for all remaining splash stints and their pit stops
           const numSplashStints = remainingPitStops - numFullStints - 1; // -1 for the bridge stint
           const timeNeededForSplashStints = numSplashStints * (splashStintDuration + minPitDurationSeconds);
           
           // Calculate the ideal bridge stint duration to make the last pit stop happen exactly at window close
           // We need to account for:
           // 1. The bridge stint duration
           // 2. The bridge stint pit stop duration
           // 3. All splash stint durations
           // 4. All splash stint pit stop durations (except the last one)
           
           // Time needed for all splash stints and their pit stops (except the last pit stop)
           const timeForSplashStintsExceptLastPit = (numSplashStints * splashStintDuration) + 
                                                   ((numSplashStints - 1) * minPitDurationSeconds);
           
           // Calculate the ideal bridge stint duration
           const idealBridgeStintDuration = Math.min(
               timeUntilWindowClose - timeForSplashStintsExceptLastPit - minPitDurationSeconds,
               maxStintTimeSeconds
           );

           // Ensure the bridge stint duration is at least the minimum stint time
           let adjustedBridgeStintDuration = Math.max(idealBridgeStintDuration, minStintTimeSeconds);

           // Add additional check to ensure we don't exceed the pit window
           const finalPitTime = currentStintStart + adjustedBridgeStintDuration + 
               (numSplashStints * (splashStintDuration + minPitDurationSeconds));
           
           if (finalPitTime > actualPitWindowCloseTime && numSplashStints > 0) {
               // Calculate how much we need to reduce the bridge stint by
               const reduction = finalPitTime - actualPitWindowCloseTime;
               // Ensure we don't go below minimum stint time
               const furtherAdjustedDuration = Math.max(adjustedBridgeStintDuration - reduction, minStintTimeSeconds);
               
               // Use the further adjusted duration
               const stintEndTime = currentStintStart + furtherAdjustedDuration;
               const pitEndTime = stintEndTime + minPitDurationSeconds;
               
               // Debug logging
               console.log(`[StintCalculator] Bridge stint further adjusted to ensure final pit within window:`);
               console.log(`  Original adjusted duration: ${adjustedBridgeStintDuration.toFixed(2)}s`);
               console.log(`  Further adjusted duration: ${furtherAdjustedDuration.toFixed(2)}s`);
               console.log(`  Final pit will now happen at: ${(stintEndTime + (numSplashStints * (splashStintDuration + minPitDurationSeconds))).toFixed(2)}s`);
               
               // Use the further adjusted duration
               adjustedBridgeStintDuration = furtherAdjustedDuration;
           }
           
           // Recalculate bridge stint laps based on adjusted duration
           const adjustedBridgeStintLaps = Math.floor(adjustedBridgeStintDuration / avgLapTimeSeconds);
           
           const stintEndTime = currentStintStart + adjustedBridgeStintDuration;
           const pitEndTime = stintEndTime + minPitDurationSeconds;
           
           // Debug logging
           console.log(`[StintCalculator] Bridge stint optimization:`);
           console.log(`  Ideal duration to use full window: ${idealBridgeStintDuration.toFixed(2)}s`);
           console.log(`  Adjusted duration (min ${minStintTimeSeconds}s): ${adjustedBridgeStintDuration.toFixed(2)}s`);
           console.log(`  Time until window close: ${timeUntilWindowClose.toFixed(2)}s`);
           console.log(`  Last pit will happen at: ${(stintEndTime + (numSplashStints * (splashStintDuration + minPitDurationSeconds))).toFixed(2)}s`);
           
           calculatedStintsInfo.push({
               stintNumber: stintNumber++,
               startTime: currentStintStart,
               endTime: stintEndTime,
               pitEndTime: pitEndTime,
               duration: adjustedBridgeStintDuration,
               startLap: currentStartLap,
               endLap: currentStartLap + adjustedBridgeStintLaps,
               laps: adjustedBridgeStintLaps,
               isPitWindowValid: (stintEndTime >= actualPitWindowOpenTime - epsilon &&
                                 stintEndTime <= actualPitWindowCloseTime + epsilon),
               isExtraStint: false,
               isUserModified: false,
           });
           
           currentStintStart = pitEndTime;
           currentStartLap += adjustedBridgeStintLaps;
       }
       
       // Generate splash stints
       for (let i = 0; i < numSplashStints; i++) {
           const stintEndTime = currentStintStart + splashStintDuration;
           const pitEndTime = stintEndTime + minPitDurationSeconds;
           
           calculatedStintsInfo.push({
               stintNumber: stintNumber++,
               startTime: currentStintStart,
               endTime: stintEndTime,
               pitEndTime: pitEndTime,
               duration: splashStintDuration,
               startLap: currentStartLap,
               endLap: currentStartLap + splashStintLaps,
               laps: splashStintLaps,
               isPitWindowValid: (stintEndTime >= actualPitWindowOpenTime - epsilon &&
                                 stintEndTime <= actualPitWindowCloseTime + epsilon),
               isExtraStint: true,
               isUserModified: false,
           });
           
           currentStintStart = pitEndTime;
           currentStartLap += splashStintLaps;
       }
  }

  // If mandatoryPitStops was 0 and no stints were modified, calculate a single stint
  if (mandatoryPitStops === 0 && calculatedStintsInfo.length === 0) {
       console.log('[StintCalculator] Calculating single stint for 0 mandatory stops.');
       if (totalRaceTimeSeconds < minStintTimeSeconds - epsilon) {
         return {
           calculatedStints: [],
           error: `Strategy impossible: Race duration (${totalRaceTimeSeconds}s) is less than minimum stint time (${minStintTimeSeconds}s) with 0 stops.`
         };
       }
       const finalLaps = Math.ceil(totalRaceTimeSeconds / avgLapTimeSeconds);
       const finalDuration = finalLaps * avgLapTimeSeconds; // Duration based on calculated laps
       // Ensure it doesn't exceed total race time
       const adjustedFinalDuration = (finalDuration > totalRaceTimeSeconds + epsilon) ? totalRaceTimeSeconds : finalDuration;
       const adjustedFinalLaps = Math.ceil(adjustedFinalDuration / avgLapTimeSeconds);


       // Check minimum duration for the single stint
       if (adjustedFinalDuration < minStintTimeSeconds - epsilon) {
            console.error(`[StintCalculator] Single stint duration check failed (0 stops): adjustedFinalDuration (${adjustedFinalDuration.toFixed(2)}) < minStintTimeSeconds (${minStintTimeSeconds})`);
            return {
              calculatedStints: [],
              error: `Strategy impossible: Single stint duration (${adjustedFinalDuration.toFixed(2)}s) is less than minimum (${minStintTimeSeconds}s) with 0 stops.`
            };
       }


       calculatedStintsInfo.push({
         stintNumber: 1,
         startTime: 0,
         endTime: adjustedFinalDuration,
         pitEndTime: null,
         duration: adjustedFinalDuration,
         startLap: 0,
         endLap: adjustedFinalLaps,
         laps: adjustedFinalLaps,
         isPitWindowValid: true, // No pit stop to validate
         isExtraStint: false, // Or true, depending on definition for single stint races
         isUserModified: false, // Automatically calculated
       });
  }

  // Ensure stint numbers are sequential and correct after all calculations
  calculatedStintsInfo.forEach((stint, index) => {
      stint.stintNumber = index + 1;
  });

  // Final check: Ensure the last stint ends exactly at totalRaceTimeSeconds (within epsilon)
  if (calculatedStintsInfo.length > 0) {
      const lastStint = calculatedStintsInfo[calculatedStintsInfo.length - 1];
      if (lastStint) { // Check if lastStint is defined
        if (Math.abs(lastStint.endTime - totalRaceTimeSeconds) > epsilon) {
            console.warn(`[StintCalculator] Last stint end time (${lastStint.endTime.toFixed(2)}s) does not match total race time (${totalRaceTimeSeconds.toFixed(2)}s). Adjusting last stint duration.`);
            lastStint.duration = totalRaceTimeSeconds - lastStint.startTime;
            lastStint.endTime = totalRaceTimeSeconds;
            lastStint.laps = Math.ceil(lastStint.duration / avgLapTimeSeconds); // Recalculate laps based on adjusted duration
            lastStint.endLap = lastStint.startLap + lastStint.laps; // Update end lap
        }
      }
  }

  // After all stints are calculated, ensure the last stint is never marked as extra
  if (calculatedStintsInfo.length > 0) {
    // Get the last stint
    const lastStint = calculatedStintsInfo[calculatedStintsInfo.length - 1];
    if (lastStint) { // Check if lastStint is defined
      // Ensure the last stint is never marked as extra and has no pit end time
      lastStint.isExtraStint = false;
      lastStint.pitEndTime = null;
      lastStint.isPitWindowValid = true; // No pit window constraint for last stint
    }
  }

  console.log(`[StintCalculator] Calculation complete. Generated ${calculatedStintsInfo.length} stints.`);
  calculatedStintsInfo.forEach((stint) => {
    // Add checks for stint properties if they can be undefined before calling .toFixed() or .toISOString()
      console.log(`[StintCalculator] Stint ${stint?.stintNumber}: Start: ${stint?.startTime?.toFixed(2)}s, End: ${stint?.endTime?.toFixed(2)}s, Dur: ${stint?.duration?.toFixed(2)}s, Laps: ${stint?.laps}, PitEnd: ${stint?.pitEndTime === null || stint?.pitEndTime === undefined ? 'N/A' : stint.pitEndTime.toFixed(2)}, PitValid: ${stint?.isPitWindowValid}, Modified: ${stint?.isUserModified}, ActualPitEntry: ${stint?.actualPitEntryTime ? new Date(stint.actualPitEntryTime).toISOString() : 'N/A'}, ActualPitDur: ${stint?.actualPitDuration !== null && stint?.actualPitDuration !== undefined ? stint.actualPitDuration.toFixed(2) + 's' : 'N/A'}, ActualStintDur: ${stint?.actualStintDuration !== null && stint?.actualStintDuration !== undefined ? stint.actualStintDuration.toFixed(2) + 's' : 'N/A'}`);
  });

  // Basic validation of the total number of stints
  if (calculatedStintsInfo.length !== totalStints && mandatoryPitStops > 0) {
       console.error(`[StintCalculator] Mismatch in calculated stint count. Expected ${totalStints}, got ${calculatedStintsInfo.length}.`);
       // This indicates a bug in the calculation logic for mandatory stops
       // This indicates a bug in the calculation logic for mandatory stops
       return {
           calculatedStints: calculatedStintsInfo, // Return the partial result for debugging
           error: `Internal calculation error: Mismatch in total stint count (${calculatedStintsInfo.length} vs expected ${totalStints}). Please check strategy parameters.`
       };
  }

  // Final check: Ensure all mandatory pits are accounted for before the final stint
  const stintsBeforeFinal = calculatedStintsInfo.slice(0, calculatedStintsInfo.length - 1);
  const pitsAccountedFor = stintsBeforeFinal.filter(stint => stint.pitEndTime !== null).length;
  if (pitsAccountedFor !== mandatoryPitStops) {
       console.warn(`[StintCalculator] Mismatch in mandatory pit stops accounted for. Expected ${mandatoryPitStops}, found ${pitsAccountedFor} before final stint.`);
       // This might happen with user modifications or edge cases. Decide if this should be a hard error.
       // For now, it's a warning.
  }


  return { calculatedStints: calculatedStintsInfo };
};
