import React from 'react';
import { StrategyData } from '../../../types';
import { StintInfo } from '../types';
import StintCard from './StintCard';
import './StintList.css';

// Extend StintInfo to match what StintCard expects
interface ExtendedStintInfo extends StintInfo {
  actualAvgLapTime?: number | null;
  bestLapTime?: number | null;
  isCompleted?: boolean;
}

interface StintListProps {
  stints: ExtendedStintInfo[];
  currentRaceTime: number;
  strategyData: StrategyData;
  totalRaceTime: number;
  onStintUpdated: (updatedStint: ExtendedStintInfo) => Promise<void>;
}

const StintList: React.FC<StintListProps> = ({ 
  stints, 
  currentRaceTime, 
  strategyData, 
  totalRaceTime,
  onStintUpdated
}) => {
  // Helper function to determine if a stint is completed
  const isStintDone = (stint: ExtendedStintInfo, isLastStint: boolean) => {
    // If stint is explicitly marked as completed, it's done
    if (stint.isUserModified) return true;
    
    // If race is not active, nothing is completed automatically
    if (!strategyData.startTime) return false;
    
    // For the last stint, check if race is over
    if (isLastStint) {
      return currentRaceTime >= totalRaceTime;
    }
    
    // For other stints, check if we've passed their end time
    return currentRaceTime > stint.endTime;
  };
  
  // Helper function to determine if a stint is active
  const isStintActive = (stint: ExtendedStintInfo) => {
    // If race is not active, nothing is active
    if (!strategyData.startTime) return false;
    
    // If stint is already completed, it's not active
    if (stint.isUserModified) return false;
    
    // A stint is active if current race time is between its start and end times
    return currentRaceTime >= stint.startTime && currentRaceTime <= stint.endTime;
  };

  // IMPORTANT: Show all stints regardless of race status
  // This fixes the issue where only 9 stints are shown instead of all 14
  const filteredStints = stints;
  
  // Use inline styles to force grid layout
  return (
    <div className="stint-list-container">
      <div 
        className="stint-list-grid"
      >
        {filteredStints.map((stint, index) => {
          const isLastStint = index === filteredStints.length - 1;
          const isDone = isStintDone(stint, isLastStint);
          const isActive = isStintActive(stint);
          
          // Ensure the last stint is never treated as an extra stint in the UI
          const isExtraStint = isLastStint ? false : stint.isExtraStint === true;
          
          return (
            <div key={`stint-${stint.stintNumber}`} style={{ width: '100%' }}>
              <StintCard
                stint={stint}
                isActive={isActive}
                isDone={isDone}
                isLastStint={isLastStint}
                isExtraStint={isExtraStint}
                onStintUpdated={onStintUpdated}
                totalRaceTime={totalRaceTime}
                stints={filteredStints}
              />
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default StintList;



