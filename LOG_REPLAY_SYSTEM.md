# Log Replay System Documentation

This system allows you to load log files from your desktop and replay them in real-time via websocket, emulating live track data. The app reads and parses the data as if it's coming from the actual track websocket, creating database objects as needed.

## System Overview

The log replay system consists of three main components:

1. **Log File Loader** - Upload/load log files from desktop
2. **Websocket Replay Service** - Send messages in real-time without timestamps
3. **Real-time Parser** - App reads and parses live data, creating objects

## Architecture

```
Desktop Log File → Upload → Replay Service → WebSocket → Real-time Parser → Database Objects
```

### Components

#### Backend Services
- `LogReplayService` - Controls replay timing and message sending
- `ApexRealtimeReceiver` - Processes incoming messages and creates database objects
- `WebSocket Server` - Handles real-time communication
- `ApexParser` - Parses Apex timing data format

#### Frontend Components
- `LogReplay` - User interface for controlling replay
- `WebSocket Client` - Receives live data updates

## How to Use

### 1. Start the System

The system automatically initializes when you start the backend server:

```bash
cd backend
npm run dev
```

### 2. Access the Log Replay Interface

Navigate to `/log-replay` in your frontend application.

### 3. Load a Log File

**Option A: Upload from Desktop**
1. Click "Select Log File"
2. Choose a `.txt` or `.log` file from your computer
3. Click "Upload & Load"

**Option B: Paste Log Content**
1. Copy log content from any source
2. Paste into the text area
3. Click "Load Content"

### 4. Control Replay

Use the control buttons:
- **Play** ▶️ - Start sending messages
- **Pause** ⏸️ - Pause replay (can resume)
- **Stop** ⏹️ - Stop replay completely
- **Reset** 🔄 - Reset to beginning

### 5. Adjust Speed

Use the speed slider to control replay speed:
- 0.1x = Very slow (10% speed)
- 1.0x = Normal speed
- 5.0x = Very fast (500% speed)

### 6. Monitor Live Data

Watch the "Live Messages" section to see:
- Real-time message processing
- Line numbers and content
- Timestamps of received data

## API Endpoints

### Upload Log File
```http
POST /api/replay/upload-log
Content-Type: multipart/form-data

FormData: logFile (file)
```

### Load Log Content
```http
POST /api/replay/load-log
Content-Type: application/json

{
  "logContent": "string"
}
```

### Control Replay
```http
POST /api/replay/start
POST /api/replay/pause
POST /api/replay/resume
POST /api/replay/stop
POST /api/replay/reset
```

### Set Speed
```http
POST /api/replay/speed
Content-Type: application/json

{
  "speed": 2.0
}
```

### Get Status
```http
GET /api/replay/status
```

## WebSocket Messages

### Outgoing (Server → Client)

#### Apex Data Message
```json
{
  "type": "apex_data",
  "content": "r17749c9|tn|1:10.062",
  "lineNumber": 42,
  "timestamp": "2024-11-09T13:53:15.141Z"
}
```

#### Replay Status
```json
{
  "type": "replay_status",
  "event": "started|paused|stopped|progress",
  "status": {
    "isPlaying": true,
    "currentLine": 42,
    "totalLines": 1000,
    "progress": 0.042,
    "elapsedTime": 5000,
    "estimatedTimeRemaining": 115000
  }
}
```

#### Database Object Created
```json
{
  "type": "apex_session_created|apex_team_created|apex_lap_recorded",
  "session|team|lap": { ... }
}
```

### Incoming (Client → Server)

#### Load Log
```json
{
  "command": "load_log",
  "logContent": "log content here..."
}
```

#### Control Commands
```json
{
  "command": "start|pause|resume|stop|reset"
}
```

#### Set Speed
```json
{
  "command": "set_speed",
  "speed": 2.0
}
```

## Real-time Data Processing

When messages are sent via websocket, the `ApexRealtimeReceiver` automatically:

1. **Parses Messages** - Extracts timing data from log format
2. **Creates Sessions** - From 'init' messages with race metadata
3. **Creates Teams/Karts** - From grid data and competitor information
4. **Records Lap Times** - Individual lap times with best lap tracking
5. **Tracks Pit Stops** - Entry/exit times and durations
6. **Updates Positions** - Real-time race positions and gaps

### Database Collections Created

All data is stored in `apex_` prefixed collections:

- `apex_sessions` - Race sessions
- `apex_teams` - Teams and drivers
- `apex_karts` - Kart assignments
- `apex_competitors` - Competitor ID mappings
- `apex_laps` - Individual lap times
- `apex_pitstops` - Pit stop data
- `apex_livedata` - Real-time positions

## Log File Format

The system expects Apex timing log format:

```
[timestamp] field|type|value
field2|type2|value2
```

### Example Log Entry
```
[2024-11-09T13:53:15.141Z] init|r|
title1||EKO 4H of Serres
title2||Final
track||Karting Track (1200m)
grid||<tbody><tr data-id="r0"...
r17749c9|tn|1:10.062
r17749c13|in|149
```

## Configuration

### Replay Service Config
```typescript
{
  speed: 1.0,           // Replay speed multiplier
  autoStart: false,     // Auto-start on load
  loopMode: false,      // Loop when finished
  skipTimestamps: true  // Remove timestamps from messages
}
```

### Realtime Receiver Config
```typescript
{
  sessionId: "custom-session-id",
  raceId: "race-001",
  enableLogging: true,
  autoCreateSession: true,
  broadcastUpdates: true
}
```

## Monitoring and Debugging

### Frontend Monitoring
- Connection status indicator
- Real-time message display
- Progress tracking with time estimates
- Speed control and status

### Backend Logging
- Message processing statistics
- Database object creation logs
- Error handling and reporting
- Performance metrics

### WebSocket Connection
- Automatic reconnection on disconnect
- Connection status updates
- Error message broadcasting

## Use Cases

1. **Race Replay Analysis** - Replay past races to analyze performance
2. **System Testing** - Test timing system with known data
3. **Development** - Develop features with consistent test data
4. **Training** - Train operators with realistic data flow
5. **Debugging** - Reproduce issues with specific log sequences

## Troubleshooting

### Common Issues

1. **WebSocket Connection Failed**
   - Check if backend server is running
   - Verify WebSocket URL (ws://localhost:5000)
   - Check firewall settings

2. **File Upload Failed**
   - Ensure file is .txt or .log format
   - Check file size (50MB limit)
   - Verify file contains valid log data

3. **No Data Created**
   - Check log format matches Apex timing format
   - Verify 'init' messages are present
   - Check backend logs for parsing errors

4. **Slow Performance**
   - Reduce replay speed
   - Check system resources
   - Monitor database performance

### Debug Mode

Enable detailed logging by setting environment variables:
```bash
DEBUG=apex:*
LOG_LEVEL=debug
```

## Integration with Existing System

The log replay system integrates seamlessly with your existing race strategy application:

- Uses separate `apex_` database collections
- Preserves existing user input data
- Can run alongside normal operations
- Provides real-time updates via websocket

This allows you to test and develop with realistic data while maintaining your production race strategy functionality.
