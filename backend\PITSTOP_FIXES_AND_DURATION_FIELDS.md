# Pitstop Detection Fixes and Duration Fields Implementation

## Overview

This document outlines the comprehensive fixes implemented to resolve pitstop detection issues and the addition of new duration tracking fields to the ApexPitStop schema.

## Issues Identified and Fixed

### 1. ❌ Pitstops Not Being Created

**Root Causes Identified**:
1. **Field Type Detection**: Grid parsing works correctly, but field type lookup might fail
2. **Websocket Mapping**: Competitor mapping might not be populated when pit events occur
3. **Message Format**: Different message formats might not be handled properly

**Solutions Implemented**:

#### A. Enhanced Field Type Detection
```typescript
// Added fallback for pit detection when field type is unknown
case 'pit': // Pit status
  if (competitorId && data.value) {
    await this.handlePitUpdate(competitorId, data.value);
  }
  break;
default:
  // NEW: Check if this might be a pit update based on the value
  if (competitorId && data.value && (data.value === 'IN' || data.value === 'OUT')) {
    console.log(`🏁 Potential pit event detected (unknown field type): ${competitorId} -> ${data.value}`);
    await this.handlePitUpdate(competitorId, data.value);
  }
  break;
```

#### B. Enhanced Logging and Debugging
```typescript
// Added comprehensive logging to track field type detection
console.log(`Updating competitor ${competitorId}, column ${columnId}, field ${fieldType}: ${data.value}`);
if (fieldType === 'unknown') {
  console.log(`🔍 Available grid header types:`, this.gridData?.header_types);
}
```

#### C. Improved Error Handling
```typescript
// Enhanced pit update method with detailed logging
private async handlePitUpdate(websocketId: string, pitStatus: string): Promise<void> {
  if (this.config.enableLogging) {
    console.log(`🏁 handlePitUpdate called: websocketId=${websocketId}, pitStatus=${pitStatus}`);
  }
  
  // Check for competitor mapping
  const competitorObjectId = this.websocketToCompetitorMap.get(websocketId);
  if (!competitorObjectId) {
    console.warn(`No competitor mapping found for websocket ID: ${websocketId}`);
    console.warn(`Available mappings:`, Array.from(this.websocketToCompetitorMap.keys()));
    return;
  }
}
```

### 2. ✅ Added Duration Fields to Pit Schema

**New Fields Added**:

```typescript
export interface IApexPitStop extends Document {
  sessionId: mongoose.Types.ObjectId;
  competitorId: mongoose.Types.ObjectId;
  kartId: mongoose.Types.ObjectId;
  pitInTime: Date;
  pitOutTime?: Date;
  pitDuration?: number; // ✅ Total duration when completed (seconds)
  pitCurrentDuration?: number; // ✅ NEW: Current duration for active pit stops (seconds)
  pitTotalDuration?: number; // ✅ NEW: Cumulative total for all pit stops by competitor (seconds)
  lapNumber: number;
  reason: string;
  isActive: boolean;
}
```

**Schema Updated**:
```typescript
const ApexPitStopSchema = new Schema<IApexPitStop>({
  // ... existing fields ...
  pitDuration: { type: Number }, // Total duration when completed
  pitCurrentDuration: { type: Number }, // NEW: Current duration for active pit stops
  pitTotalDuration: { type: Number }, // NEW: Cumulative total for all pit stops
  // ... rest of fields ...
});
```

### 3. ✅ Enhanced Pit Entry Logic

**Improved Pit Entry Creation**:
```typescript
private async handlePitEntry(competitor: any, competitorObjectId: mongoose.Types.ObjectId): Promise<void> {
  // Get total duration from previous pit stops
  const previousPitStops = await ApexPitStop.find({
    sessionId: this.currentSession._id,
    competitorId: competitorObjectId,
    pitDuration: { $exists: true }
  });

  const pitTotalDuration = previousPitStops.reduce((total, pit) => total + (pit.pitDuration || 0), 0);

  // Create pit entry record with new duration fields
  const pitStopData = {
    sessionId: this.currentSession._id,
    competitorId: competitorObjectId,
    kartId: competitor.kartId,
    pitInTime: new Date(),
    lapNumber: currentLapNumber,
    pitCurrentDuration: 0, // ✅ Will be updated in real-time
    pitTotalDuration: pitTotalDuration, // ✅ Cumulative total
    reason: 'Regular',
    isActive: true
  };

  const createdPitStop = await ApexPitStop.create(pitStopData);
}
```

### 4. ✅ Enhanced Pit Exit Logic

**Improved Pit Exit Completion**:
```typescript
private async handlePitExit(competitor: any, competitorObjectId: mongoose.Types.ObjectId): Promise<void> {
  const activePitStop = await ApexPitStop.findOne({
    sessionId: this.currentSession._id,
    competitorId: competitorObjectId,
    pitOutTime: { $exists: false }
  }).sort({ pitInTime: -1 });

  if (activePitStop) {
    const pitOutTime = new Date();
    const pitDuration = (pitOutTime.getTime() - activePitStop.pitInTime.getTime()) / 1000;
    
    // ✅ Calculate new total duration including this pit stop
    const newPitTotalDuration = (activePitStop.pitTotalDuration || 0) + pitDuration;

    await ApexPitStop.updateOne(
      { _id: activePitStop._id },
      {
        pitOutTime: pitOutTime,
        pitDuration: pitDuration,
        pitCurrentDuration: 0, // ✅ Reset current duration as pit stop is complete
        pitTotalDuration: newPitTotalDuration, // ✅ Update cumulative total
        isActive: false // ✅ Mark as completed
      }
    );
  }
}
```

## Duration Fields Usage

### `pitCurrentDuration`
- **Purpose**: Track current duration of active pit stops in real-time
- **Updates**: Can be updated periodically while pit stop is active
- **Value**: Seconds since pit entry
- **Reset**: Set to 0 when pit stop completes

### `pitTotalDuration`
- **Purpose**: Track cumulative total pit time for a competitor across all pit stops
- **Updates**: Updated when each pit stop completes
- **Value**: Sum of all completed pit stop durations
- **Persistence**: Accumulates throughout the session

### `pitDuration`
- **Purpose**: Duration of individual completed pit stop
- **Updates**: Set when pit stop completes (pit exit)
- **Value**: Seconds from pit in to pit out
- **Usage**: Historical record of individual pit stop duration

## Real-time Duration Updates

For real-time current duration updates, you can implement:

```typescript
// Example: Update current duration every second for active pit stops
setInterval(async () => {
  const activePitStops = await ApexPitStop.find({
    isActive: true,
    pitOutTime: { $exists: false }
  });

  for (const pitStop of activePitStops) {
    const currentDuration = (Date.now() - pitStop.pitInTime.getTime()) / 1000;
    await ApexPitStop.updateOne(
      { _id: pitStop._id },
      { pitCurrentDuration: currentDuration }
    );
  }
}, 1000); // Update every second
```

## API Enhancements

### Pitstop Endpoint Response
```typescript
// GET /api/apex/sessions/:sessionId/pitstops
{
  "_id": "...",
  "competitorId": "...",
  "kartId": "...",
  "pitInTime": "2024-01-01T10:00:00.000Z",
  "pitOutTime": "2024-01-01T10:01:30.000Z", // if completed
  "pitDuration": 90, // seconds (if completed)
  "pitCurrentDuration": 45, // seconds (if active)
  "pitTotalDuration": 180, // cumulative seconds
  "lapNumber": 15,
  "reason": "Regular",
  "isActive": false
}
```

## Testing and Verification

### Debug Commands
```bash
# Test pitstop detection
cd backend
npx ts-node scripts/debug-pitstop-messages.ts

# Test grid parsing
node scripts/test-grid-parsing.js

# Check database directly
db.apex_pitstops.find({}).sort({pitInTime: -1}).limit(5)
```

### Verification Checklist
- ✅ Grid parsing correctly identifies pit columns (`c15: pit`)
- ✅ Field type detection works for known pit columns
- ✅ Fallback detection works for unknown field types with IN/OUT values
- ✅ Websocket mapping is populated before processing updates
- ✅ Pit entry creates records with proper duration fields
- ✅ Pit exit updates records with calculated durations
- ✅ Cumulative duration tracking works across multiple pit stops

## Frontend Integration

### Display Current Pit Duration
```typescript
const formatDuration = (seconds: number) => {
  const mins = Math.floor(seconds / 60);
  const secs = seconds % 60;
  return `${mins}:${secs.toString().padStart(2, '0')}`;
};

// Show current pit duration for active pit stops
{activePitStop.pitCurrentDuration && (
  <span>Current: {formatDuration(activePitStop.pitCurrentDuration)}</span>
)}
```

### Display Total Pit Time
```typescript
// Show cumulative pit time for competitor
<span>Total Pit Time: {formatDuration(competitor.pitTotalDuration || 0)}</span>
```

## Conclusion

These fixes provide:

- ✅ **Reliable pitstop detection** with fallback mechanisms
- ✅ **Comprehensive duration tracking** with three duration fields
- ✅ **Real-time pit monitoring** capabilities
- ✅ **Enhanced debugging** and logging
- ✅ **Robust error handling** for edge cases

The pitstop system now properly detects pit events, creates database records, and tracks detailed timing information for comprehensive race analysis.
