/* src/components/Teams/TeamGrid.css */

.team-card {
    width: 100%;
    margin: 5px 0; /* Adjust spacing */
    /* position: relative; /* No longer needed if indicator isn't absolute */
    --inner-padding-top: 0; /* Remove default top padding */
    --inner-padding-bottom: 0; /* Remove default bottom padding */
    --inner-padding-start: 0; /* Remove default start padding */
    --inner-padding-end: 0; /* Remove default end padding */
}

/* Custom header padding */
.team-card-header-custom {
  padding-top: 0.6rem;
  padding-bottom: 0.6rem;
  padding-left: 0.8rem;
  padding-right: 0.8rem;
  /* position: relative; /* Keep if indicator needs absolute positioning */
}

/* Container for the top row (number and kart indicator) */
.team-card-top-row {
  display: flex;
  justify-content: space-between; /* Pushes number left, indicator right */
  align-items: center; /* Vertically align items */
  margin-bottom: 0.4rem; /* Space below top row */
  min-height: 20px; /* Ensure space even if no kart */
}

/* Style for the small team number */
.team-card-number-small {
  font-size: 0.9em;
  font-weight: 500;
  color: var(--ion-color-step-600, #666);
}

/* Kart indicator positioning */
.team-card-kart-indicator {
  padding: 2px 6px;
  font-size: 0.8em;
  min-width: 40px;
  text-align: center;
  /* margin-left: auto; /* Helps push to the right with flexbox */
}

/* Style for the main team name */
.team-card-name {
  font-size: 1.2em;
  font-weight: bold;
  color: var(--ion-text-color);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.2;
  /* margin-bottom: 0.2rem; /* Adjust if needed */
}

/* Remove styles for the deleted subtitle */
/* .team-card-details { ... } */
/* .team-card-details .detail-separator { ... } */

/* Reduce padding/margin for buttons inside the card */
.team-card-actions {
  padding-top: 0.2rem;
  padding-bottom: 0.2rem;
  display: flex;
  justify-content: space-around; /* Space out buttons */
  border-top: 1px solid var(--ion-color-step-150, #e0e0e0); /* Optional separator */
}

.team-card-actions ion-button {
  margin: 0;
  font-size: 0.8em; /* Smaller button text */
  --padding-start: 4px;
  --padding-end: 4px;
}

.team-card-actions ion-icon {
  margin-right: 4px; /* Space between icon and text */
}

/* Ensure grid columns have some padding */
ion-grid {
  padding: 5px;
}

ion-col {
  padding: 5px;
}


/* Styles for Info Modal List */
.team-info-modal-list ion-item {
  --inner-padding-end: 8px; /* Adjust padding */
}
.team-info-modal-list ion-label {
  font-weight: 500;
}
.team-info-modal-list ion-note {
  font-size: 0.95em;
}

/* History Item Styling */
.history-item {
  --min-height: 60px; /* Ensure enough space */
}

.history-item ion-icon {
  font-size: 1.4em; /* Slightly larger icon */
  margin-right: 14px;
  margin-top: 14px; /* Align icon better with multi-line text */
  align-self: flex-start; /* Keep icon at the top */
}

.history-item ion-label p {
  font-size: 0.95em;
  margin: 0 0 4px 0; /* Space below main text */
  line-height: 1.3;
  color: var(--ion-text-color); /* Use default text color */
}

.history-item .history-timestamp {
  font-size: 0.75em;
  color: var(--ion-color-step-500, #808080);
}

/* Style for invalid entries */
.history-item-invalid ion-label p {
  text-decoration: line-through;
  color: var(--ion-color-danger-shade);
}
.history-item-invalid .history-timestamp {
   color: var(--ion-color-danger-tint);
}

/* Styles for the Team Info Modal using Cards */
.team-info-modal ion-card {
  margin-bottom: 16px; /* Add space between cards */
}

.team-info-modal ion-card-content ion-list {
  padding-top: 0;
  padding-bottom: 0;
}

.team-info-modal .action-item-no-lines {
  --inner-border-width: 0; /* Remove line for action button item */
  padding-top: 8px;
  padding-bottom: 8px;
}

.team-info-modal .history-list-inset {
  margin-left: 0; /* Remove default list inset margin if needed */
  margin-right: 0;
}

.team-info-modal .history-item {
  --padding-start: 16px;
  --inner-padding-end: 16px;
}

.team-info-modal .history-timestamp {
  font-size: 0.8em;
  color: var(--ion-color-step-600, #666);
  margin-top: 4px;
  display: block;
}

.team-info-modal .history-item-invalid p {
  text-decoration: line-through;
  opacity: 0.7;
}

/* Ensure editable fields have consistent padding */
.team-info-modal .edit-name-container {
  width: 100%;
  display: flex;
  align-items: center;
}

.team-info-modal .edit-name-input {
  flex-grow: 1;
  /* --padding-start: 0; */ /* Adjust if needed */
  /* --padding-end: 8px; */ /* Adjust if needed */
}

.team-info-modal .display-name-container {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: flex-end; /* Align content to the right */
}

.team-info-modal .team-name-display {
  flex-grow: 1;
  text-align: right;
  margin-right: 8px; /* Space between text and edit button */
}
