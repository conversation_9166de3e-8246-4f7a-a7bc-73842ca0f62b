import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  IonPage,
  IonContent,
  IonItem,
  IonInput,
  IonButton,
  IonLoading,
  IonCard,
  IonCardContent,
  IonCardHeader,
  IonCardTitle,
  IonGrid,
  IonRow,
  IonAlert,
  IonHeader,
  IonToolbar,
  IonTitle
} from '@ionic/react';
import { IonCol, IonIcon } from '@ionic/react';
import { personAddOutline, mailOutline, lockClosedOutline, personOutline } from 'ionicons/icons';
import axios, { AxiosError } from 'axios';

const Register: React.FC = () => {
  const [name, setName] = useState('');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [showAlert, setShowAlert] = useState(false);
  const navigate = useNavigate();

  const handleRegister = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    
    if (password !== confirmPassword) {
      setError('Passwords do not match');
      setShowAlert(true);
      return;
    }
    
    setIsLoading(true);

    try {
      await axios.post(`${import.meta.env.VITE_API_URL}/auth/register`, {
        name,
        email,
        password
      });
      
      navigate('/login', { 
        state: { message: 'Registration successful! Please log in.' } 
      });
    } catch (err: unknown) {
      const axiosError = err as AxiosError<{message?: string}>;
      setError(axiosError.response?.data?.message || 'Registration failed. Please try again.');
      setShowAlert(true);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <IonPage>
      <IonHeader>
        <IonToolbar>
          <IonTitle>Register</IonTitle>
        </IonToolbar>
      </IonHeader>
      
      <IonContent className="ion-padding">
        <IonLoading isOpen={isLoading} message={'Creating account...'} />
        
        <IonAlert
          isOpen={showAlert}
          onDidDismiss={() => setShowAlert(false)}
          header="Registration Error"
          message={error ?? undefined}
          buttons={['OK']}
        />
        
        <IonGrid fixed>
          <IonRow className="ion-justify-content-center">
            <IonCol size="12" sizeMd="8" sizeLg="6" sizeXl="4">
              <div className="logo-container ion-text-center">
                <h1>Manulilac RacePlanner</h1>
              </div>
              
              <IonCard className="auth-card">
                <IonCardHeader>
                  <IonCardTitle className="ion-text-center">
                    <IonIcon icon={personAddOutline} color="primary" className="auth-header-icon" />
                    <h2>Create Account</h2>
                  </IonCardTitle>
                </IonCardHeader>
                
                <IonCardContent>
                  <form onSubmit={handleRegister}>
                    <IonItem lines="full" className="auth-input">
                      <IonIcon icon={personOutline} slot="start" color="medium" />
                      <IonInput
                        label="Full Name"
                        labelPlacement="floating"
                        placeholder="Enter your full name"
                        value={name}
                        onIonInput={e => setName(e.detail.value || '')}
                        required
                        autocomplete="name"
                      />
                    </IonItem>

                    <IonItem lines="full" className="auth-input">
                      <IonIcon icon={mailOutline} slot="start" color="medium" />
                      <IonInput
                        type="email"
                        label="Email"
                        labelPlacement="floating"
                        placeholder="Enter your email"
                        value={email}
                        onIonInput={e => setEmail(e.detail.value || '')}
                        required
                        autocomplete="email"
                      />
                    </IonItem>

                    <IonItem lines="full" className="auth-input">
                      <IonIcon icon={lockClosedOutline} slot="start" color="medium" />
                      <IonInput
                        type="password"
                        label="Password"
                        labelPlacement="floating"
                        placeholder="Create a password"
                        value={password}
                        onIonInput={e => setPassword(e.detail.value || '')}
                        required
                        autocomplete="new-password"
                      />
                    </IonItem>

                    <IonItem lines="full" className="auth-input">
                      <IonIcon icon={lockClosedOutline} slot="start" color="medium" />
                      <IonInput
                        type="password"
                        label="Confirm Password"
                        labelPlacement="floating"
                        placeholder="Confirm your password"
                        value={confirmPassword}
                        onIonInput={e => setConfirmPassword(e.detail.value || '')}
                        required
                        autocomplete="new-password"
                      />
                    </IonItem>

                    <div className="ion-padding-top">
                      <IonButton expand="block" type="submit" className="auth-button">
                        Register
                      </IonButton>
                    </div>
                  </form>

                  <div className="ion-text-center ion-padding-top">
                    <IonButton fill="clear" routerLink="/login" size="small">
                      Already have an account? Login
                    </IonButton>
                  </div>
                </IonCardContent>
              </IonCard>
            </IonCol>
          </IonRow>
        </IonGrid>
      </IonContent>
    </IonPage>
  );
};

export default Register;

