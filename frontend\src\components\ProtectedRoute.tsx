import React from 'react';
import { Navigate, Outlet } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';
import { IonLoading, IonContent, IonPage } from '@ionic/react';

const ProtectedRoute: React.FC = () => {
  const { isAuthenticated, isLoading, authChecked } = useAuth();
  
  // Only show loading on initial auth check
  if (isLoading && !authChecked) {
    return (
      <IonPage>
        <IonContent className="ion-padding">
          <IonLoading 
            isOpen={true} 
            message={'Loading...'} 
            duration={3000} // 3 second max loading time
          />
        </IonContent>
      </IonPage>
    );
  }
  
  // If not authenticated, redirect to login
  if (!isAuthenticated && authChecked) {
    return <Navigate to="/login" replace />;
  }
  
  // If authenticated, render the protected content
  return <Outlet />;
};

export default ProtectedRoute;




