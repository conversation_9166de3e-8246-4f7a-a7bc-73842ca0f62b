import { Schema, model, Types } from "mongoose";
import { Document } from 'mongoose'; // Import Document

// Interface extending Document
export interface IPit extends Document {
  // _id: string; // Changed
  _id: Types.ObjectId; // Use ObjectId type
  teamId: Types.ObjectId;
  kartId?: Types.ObjectId | null; // Optionality is correct based on schema default: null
  duration: number; // seconds
  timestamp: Date; // Time pit stop ended/was recorded
  // createdAt?: Date; // Optional via timestamps
  // updatedAt?: Date; // Optional via timestamps
}

// Schema remains the same
const pitSchema = new Schema<IPit>({
  teamId: {
    type: Schema.Types.ObjectId,
    ref: "Team",
    required: [true, "Team ID is required for pit stop"],
    index: true,
  },
  kartId: {
    type: Schema.Types.ObjectId,
    ref: "Kart",
    default: null,
    index: true,
  },
  duration: {
    type: Number,
    required: [true, "Pit duration is required"],
    min: [0, "Duration cannot be negative"],
  },
  timestamp: {
    type: Date,
    default: Date.now,
    index: true,
  },
}, {
   timestamps: false
});

export const Pit = model<IPit>("Pit", pitSchema);
