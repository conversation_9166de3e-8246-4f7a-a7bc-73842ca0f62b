// backend/src/controllers/pitlaneController.ts
import { Request, Response } from "express";
import { Pitlane } from "../models/Pitlane";
import { Pit } from "../models/Pit";
import { Kart } from "../models/Kart"; // Import Kart model
import { isValidObjectId, sendErrorResponse } from "../utils/controllerUtils"; // Use helpers
import { sendWebSocketMessage } from "../websocket/websocket"; // Import WS helper

const PIT_EVENT = "pitUpdate"; // Example WS event name

// Kart enters the pitlane
export const kartEntersPitlane = async (req: Request, res: Response) => {
  const { kartId } = req.body;

  if (!kartId || !isValidObjectId(kartId)) {
    return sendErrorResponse(res, "Valid Kart ID is required.", 400);
  }

  try {
    // Find or create the Pitlane document
    // Using findOneAndUpdate with upsert is more atomic
    const pitlane = await Pitlane.findOneAndUpdate(
        {}, // Find the single pitlane document
        { $addToSet: { karts: { kartId: kartId, enteredAt: new Date() } } }, // Add kart if not already present
        { upsert: true, new: true } // Create if doesn't exist, return updated doc
    ).populate('karts.kartId', 'number'); // Populate kart number for response

    // TODO: Add WebSocket notification if needed for pitlane entry
    // sendWebSocketMessage({ event: "pitlaneUpdate", payload: pitlane });

    res.status(200).json(pitlane);
  } catch (error) {
    sendErrorResponse(res, "Error entering pitlane", 500, error);
  }
};

// Kart exits the pitlane (and creates Pit record)
export const kartExitsPitlane = async (req: Request, res: Response) => {
    const { kartId } = req.body; // Only need kartId from request

    if (!kartId || !isValidObjectId(kartId)) {
        return sendErrorResponse(res, "Valid Kart ID is required.", 400);
    }

    try {
      // 1. Find the Pitlane document and the specific kart entry to remove
      const pitlane = await Pitlane.findOne(); // Assuming one pitlane doc
      if (!pitlane) {
        return sendErrorResponse(res, "Pitlane state not found.", 404);
      }

      // Find the index of the kart entry in the array
      const kartEntryIndex = pitlane.karts.findIndex(
          (entry) => entry.kartId?.toString() === kartId
      );

      if (kartEntryIndex === -1) {
        return sendErrorResponse(res, `Kart ${kartId} not found in pitlane.`, 404);
      }

      // Get the entry data before removing it
      const kartEntry = pitlane.karts[kartEntryIndex];
      const enteredAt = kartEntry?.enteredAt;

      if (!enteredAt) {
          // This indicates a data consistency issue
          console.error(`[kartExitsPitlane] Kart ${kartId} found in pitlane but missing enteredAt time.`);
          // Remove the inconsistent entry
          pitlane.karts.splice(kartEntryIndex, 1);
          await pitlane.save();
          return sendErrorResponse(res, `Kart ${kartId} had inconsistent data (missing entry time) but was removed.`, 500);
      }

      // 2. Remove the kart entry from the pitlane array
      pitlane.karts.splice(kartEntryIndex, 1);

      // 3. Fetch the Kart document to get the currentTeamId
      const kart = await Kart.findById(kartId);
      if (!kart) {
          // Kart not found, but we should still remove it from pitlane state
          await pitlane.save();
          console.warn(`[kartExitsPitlane] Kart ${kartId} not found in DB but was removed from pitlane state.`);
          return sendErrorResponse(res, `Kart ${kartId} not found in database.`, 404);
      }
      const teamId = kart.currentTeamId; // Get teamId from the Kart document

      if (!teamId) {
          // Kart is exiting pitlane but isn't assigned to a team? Handle as needed.
          // Maybe log a warning, or maybe it's valid if it goes straight to maintenance/available.
          console.warn(`[kartExitsPitlane] Kart ${kartId} exited pitlane but has no currentTeamId.`);
          // Decide if a Pit record should still be created without a teamId
      }

      // 4. Calculate duration and create Pit record
      const exitedAt = new Date();
      const pitStopDuration = (exitedAt.getTime() - enteredAt.getTime()) / 1000; // Duration in seconds

      const pitStop = new Pit({
        kartId: kart._id,
        teamId: teamId, // Use teamId fetched from the Kart
        // pitLaneId: pitlane._id, // pitLaneId might not be necessary on the Pit record itself
        duration: pitStopDuration,
        timestamp: exitedAt, // Record when the pit stop *ended*
      });

      // 5. Save changes (Pitlane state and new Pit record)
      // Consider using a transaction here if atomicity is critical
      await pitStop.save();
      await pitlane.save();

      // 6. Send WebSocket notification about the new Pit record
      sendWebSocketMessage({ event: PIT_EVENT, payload: { type: "create", pit: pitStop } });

      res.status(200).json({ message: "Kart exited the pitlane", pitStop });

    } catch (error) {
      sendErrorResponse(res, "Error exiting pitlane", 500, error);
    }
  };
