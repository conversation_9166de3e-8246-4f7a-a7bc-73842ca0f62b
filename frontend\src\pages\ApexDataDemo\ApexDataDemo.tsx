import React from 'react';
import {
  IonPage,
  IonHeader,
  IonToolbar,
  IonTitle,
  IonContent,
  IonCard,
  IonCardHeader,
  IonCardTitle,
  IonCardContent,
  IonItem,
  IonLabel,
  IonNote,
  IonIcon,
  IonButton,
  IonButtons,
  IonMenuButton
} from '@ionic/react';
import { 
  informationCircleOutline, 
  checkmarkCircleOutline, 
  closeCircleOutline,
  refreshOutline 
} from 'ionicons/icons';
import { useDataContext } from '../../context/DataContext';
import ApexDataControls from '../../components/ApexDataControls/ApexDataControls';

const ApexDataDemo: React.FC = () => {
  const {
    useApexDatabase,
    selectedSession,
    selectedTeam,
    availableSessions,
    availableTeams,
    isLoadingSessions,
    isLoadingTeams,
    refreshSessions,
    refreshTeams,
    error
  } = useDataContext();

  return (
    <IonPage>
      <IonHeader>
        <IonToolbar>
          <IonButtons slot="start">
            <IonMenuButton />
          </IonButtons>
          <IonTitle>Apex Data Demo</IonTitle>
          <IonButtons slot="end">
            <IonButton onClick={() => {
              refreshSessions();
              refreshTeams();
            }}>
              <IonIcon icon={refreshOutline} />
            </IonButton>
          </IonButtons>
        </IonToolbar>
      </IonHeader>

      <IonContent className="ion-padding">
        {/* Demo Description */}
        <IonCard>
          <IonCardHeader>
            <IonCardTitle>Apex Database Integration Demo</IonCardTitle>
          </IonCardHeader>
          <IonCardContent>
            <p>
              This demo shows how the Apex Database integration works. You can toggle between 
              manual data entry and live Apex database data, select sessions and teams, and 
              see how the data flows through the application.
            </p>
          </IonCardContent>
        </IonCard>

        {/* Apex Data Controls */}
        <ApexDataControls 
          showTeamSelection={true}
          title="Data Source Configuration"
          description="Toggle between manual entry and live Apex database data"
        />

        {/* Current State Display */}
        <IonCard>
          <IonCardHeader>
            <IonCardTitle>Current State</IonCardTitle>
          </IonCardHeader>
          <IonCardContent>
            {/* Apex Database Status */}
            <IonItem>
              <IonIcon 
                icon={useApexDatabase ? checkmarkCircleOutline : closeCircleOutline} 
                color={useApexDatabase ? 'success' : 'medium'} 
                slot="start" 
              />
              <IonLabel>
                <h3>Apex Database</h3>
                <p>{useApexDatabase ? 'Enabled' : 'Disabled'}</p>
              </IonLabel>
            </IonItem>

            {/* Sessions Status */}
            <IonItem>
              <IonIcon 
                icon={informationCircleOutline} 
                color="primary" 
                slot="start" 
              />
              <IonLabel>
                <h3>Available Sessions</h3>
                <p>
                  {isLoadingSessions ? 'Loading...' : `${availableSessions.length} sessions found`}
                </p>
                {availableSessions.length > 0 && (
                  <IonNote color="medium">
                    Latest: {availableSessions[0]?.title1} - {availableSessions[0]?.title2}
                  </IonNote>
                )}
              </IonLabel>
            </IonItem>

            {/* Selected Session */}
            {selectedSession && (
              <IonItem>
                <IonIcon 
                  icon={checkmarkCircleOutline} 
                  color="success" 
                  slot="start" 
                />
                <IonLabel>
                  <h3>Selected Session</h3>
                  <p><strong>{selectedSession.title1} - {selectedSession.title2}</strong></p>
                  <IonNote color="medium">
                    Track: {selectedSession.track}<br />
                    Created: {new Date(selectedSession.createdAt).toLocaleString()}<br />
                    Session ID: {selectedSession._id}
                  </IonNote>
                </IonLabel>
              </IonItem>
            )}

            {/* Teams Status */}
            {useApexDatabase && selectedSession && (
              <IonItem>
                <IonIcon 
                  icon={informationCircleOutline} 
                  color="primary" 
                  slot="start" 
                />
                <IonLabel>
                  <h3>Available Teams</h3>
                  <p>
                    {isLoadingTeams ? 'Loading...' : `${availableTeams.length} teams found`}
                  </p>
                  {availableTeams.length > 0 && (
                    <IonNote color="medium">
                      Sample: {availableTeams.slice(0, 3).map(t => t.name).join(', ')}
                      {availableTeams.length > 3 && ` and ${availableTeams.length - 3} more...`}
                    </IonNote>
                  )}
                </IonLabel>
              </IonItem>
            )}

            {/* Selected Team */}
            {selectedTeam && (
              <IonItem>
                <IonIcon 
                  icon={checkmarkCircleOutline} 
                  color="success" 
                  slot="start" 
                />
                <IonLabel>
                  <h3>Selected Team</h3>
                  <p><strong>{selectedTeam.name}</strong></p>
                  <IonNote color="medium">
                    Kart Number: #{selectedTeam.kartNumber}<br />
                    Nationality: {selectedTeam.nationality}<br />
                    Status: {selectedTeam.status}<br />
                    Team ID: {selectedTeam.teamId}
                  </IonNote>
                </IonLabel>
              </IonItem>
            )}

            {/* Error Display */}
            {error && (
              <IonItem>
                <IonIcon 
                  icon={closeCircleOutline} 
                  color="danger" 
                  slot="start" 
                />
                <IonLabel>
                  <h3>Error</h3>
                  <p color="danger">{error}</p>
                </IonLabel>
              </IonItem>
            )}
          </IonCardContent>
        </IonCard>

        {/* Usage Instructions */}
        <IonCard>
          <IonCardHeader>
            <IonCardTitle>How to Use</IonCardTitle>
          </IonCardHeader>
          <IonCardContent>
            <ol>
              <li><strong>Enable Apex Database:</strong> Toggle the switch to enable live data from Apex racing sessions.</li>
              <li><strong>Select Session:</strong> Choose a session from the dropdown. Sessions are automatically loaded when you enable the Apex database.</li>
              <li><strong>Select Team:</strong> Choose a team to follow for race strategy data. Teams are loaded based on the selected session.</li>
              <li><strong>View Data:</strong> The selected session and team data will be used throughout the application for race strategy, pit stops, and live timing.</li>
            </ol>
            
            <h4>Data Flow:</h4>
            <ul>
              <li><strong>Sessions:</strong> Loaded from <code>/debug/sessions</code> API endpoint</li>
              <li><strong>Teams:</strong> Loaded from <code>/apex/sessions/{'{sessionId}'}/teams</code> API endpoint</li>
              <li><strong>Persistence:</strong> Settings are saved to localStorage and restored on page reload</li>
              <li><strong>Global State:</strong> Data is available throughout the app via DataContext</li>
            </ul>
          </IonCardContent>
        </IonCard>

        {/* Technical Details */}
        <IonCard>
          <IonCardHeader>
            <IonCardTitle>Technical Details</IonCardTitle>
          </IonCardHeader>
          <IonCardContent>
            <IonItem>
              <IonLabel>
                <h3>Context State</h3>
                <pre style={{ fontSize: '12px', overflow: 'auto' }}>
                  {JSON.stringify({
                    useApexDatabase,
                    selectedSessionId: selectedSession?._id || null,
                    selectedTeamId: selectedTeam?._id || null,
                    sessionsCount: availableSessions.length,
                    teamsCount: availableTeams.length,
                    isLoadingSessions,
                    isLoadingTeams,
                    error
                  }, null, 2)}
                </pre>
              </IonLabel>
            </IonItem>
          </IonCardContent>
        </IonCard>
      </IonContent>
    </IonPage>
  );
};

export default ApexDataDemo;
