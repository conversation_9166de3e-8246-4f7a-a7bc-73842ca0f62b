import React, { useState } from 'react';
import { 
  <PERSON>Header, 
  IonToolbar, 
  IonTitle, 
  IonContent, 
  IonButtons, 
  IonButton, 
  IonIcon,
  IonModal
} from '@ionic/react';
import { closeOutline } from 'ionicons/icons';
import { StrategyData } from '../../../types';
import StrategyList from './StrategyList';
import EditStrategyModal from './EditStrategyModal';

interface StrategyModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSelectStrategy: (strategy: StrategyData) => void;
  activeStrategyId: string | null;
  apiBaseUrl: string;
}

const StrategyModal: React.FC<StrategyModalProps> = ({ 
  isOpen, 
  onClose, 
  onSelectStrategy,
  activeStrategyId,
  apiBaseUrl
}) => {
  const [view, setView] = useState<'list' | 'create' | 'edit'>('list');
  const [selectedStrategy, setSelectedStrategy] = useState<StrategyData | null>(null);

  // Handle strategy selection
  const handleSelectStrategy = (strategy: StrategyData) => {
    onSelectStrategy(strategy);
    onClose();
  };

  // Handle edit strategy
  const handleEditStrategy = (strategy: StrategyData) => {
    setSelectedStrategy(strategy);
    setView('edit');
  };

  // Handle create new strategy
  const handleCreateStrategy = () => {
    setSelectedStrategy(null);
    setView('create');
  };

  // Handle strategy save (create or edit)
  const handleSaveStrategy = (strategy: StrategyData) => {
    onSelectStrategy(strategy);
    setView('list');
  };

  // Reset view when modal closes
  const handleClose = () => {
    setView('list');
    onClose();
  };

  return (
    <IonModal isOpen={isOpen} onDidDismiss={handleClose}>
      <IonHeader>
        <IonToolbar>
          <IonTitle>
            {view === 'list' ? 'Race Strategies' : 
             view === 'create' ? 'Create Strategy' : 'Edit Strategy'}
          </IonTitle>
          <IonButtons slot="end">
            {view !== 'list' ? (
              <IonButton onClick={() => setView('list')}>
                Back
              </IonButton>
            ) : (
              <IonButton onClick={handleClose}>
                <IonIcon icon={closeOutline} />
              </IonButton>
            )}
          </IonButtons>
        </IonToolbar>
      </IonHeader>
      
      <IonContent>
        {view === 'list' && (
          <StrategyList
            onSelectStrategy={handleSelectStrategy}
            onEditStrategy={handleEditStrategy}
            onCreateStrategy={handleCreateStrategy}
            activeStrategyId={activeStrategyId}
            apiBaseUrl={apiBaseUrl}
          />
        )}
        
        {(view === 'create' || view === 'edit') && (
          <EditStrategyModal
            isOpen={true}
            onClose={() => setView('list')}
            initialStrategy={selectedStrategy}
            onSave={handleSaveStrategy}
            isNewStrategy={view === 'create'}
            apiBaseUrl={apiBaseUrl}
          />
        )}
      </IonContent>
    </IonModal>
  );
};

export default StrategyModal;
