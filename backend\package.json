{"name": "manulilac-raceplanner-backend", "version": "0.0.1", "main": "build/index.js", "scripts": {"watch": "tsc --watch", "start:dev": "node --watch build/index.js", "dev": "concurrently \"npm run watch\" \"npm run start:dev\"", "check-types": "tsc --noEmit", "prettier": "prettier --write .", "build": "tsc", "start": "node build/index.js"}, "dependencies": {"@types/cors": "^2.8.17", "@types/express": "^5.0.1", "@types/jsonwebtoken": "^9.0.9", "@types/multer": "^1.4.12", "@types/node": "^22.15.0", "@types/ws": "^8.18.1", "argon2": "^0.40.1", "axios": "^1.8.1", "cors": "^2.8.5", "dotenv": "^16.4.7", "express": "^4.21.2", "express-async-handler": "^1.2.0", "http": "^0.0.1-security", "jsonwebtoken": "^9.0.2", "mongodb": "^6.14.0", "mongoose": "^8.9.5", "multer": "^2.0.1", "url": "^0.11.4", "ws": "^8.18.1"}, "devDependencies": {"@typescript-eslint/eslint-plugin": "^7.18.0", "@typescript-eslint/parser": "^7.18.0", "concurrently": "^8.2.2", "eslint": "^8.57.0", "prettier": "3.2.5", "typescript": "^5.7.3"}}