// src/components/Karts/kartUtils.ts
import { Kart, Team, Row } from '../../types'; // Adjust path as needed

// Define Speed levels and colors
export const speedLevels: { [key: number]: { label: string; value: number; color: string } } = {
    0: { label: 'Super Fast', value: 0, color: 'superfast' },
    1: { label: 'Fast', value: 1, color: 'success' },
    2: { label: 'Average', value: 2, color: 'warning' },
    3: { label: 'Slow', value: 3, color: 'danger' },
    4: { label: 'Unknown', value: 4, color: 'medium' },
};

// Helper to get the CSS background class
export const getSpeedBgClass = (speedValue: number | undefined | null): string => {
    const level = speedLevels[speedValue ?? 4]; // Default to Unknown (4) if speed is null/undefined
    return level ? `speed-bg-${level.color}` : 'speed-bg-medium';
};

// Helper to get location display info
export const getLocationDisplay = (kart: Kart): {
    text: string;
    color?: string;
    isRowColor?: boolean;
    rowColorClass?: string;
} => {
    switch (kart.status) {
        case 'on_track':
        { // Add block scope
            const team = kart.currentTeamId as Team | null;
            const teamText = team?.name ? `${team.name}` : 'Racing';
            return { text: teamText, color: 'success' };
        }
        case 'in_pit_row':
        { // Add block scope
            const row = kart.currentRowId as Row | null;
            if (row?.color) {
                return { text: `Row ${row.rowNumber}`, isRowColor: true, rowColorClass: 'row-bg-' + row.color.toLowerCase(), color: 'medium' };
            }
            return { text: 'In Pits', color: 'warning' };
        }
        case 'maintenance':
            return { text: 'Maintenance', color: 'danger' };
        case 'available':
            return { text: 'Available', color: 'primary' };
        case 'retired':
            return { text: 'Retired', color: 'dark' };
        default:
            return { text: 'Unknown', color: 'medium' };
    }
};