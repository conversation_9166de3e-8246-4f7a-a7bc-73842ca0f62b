.data-table .header-row {
  font-weight: bold;
  background-color: var(--ion-color-light);
  padding: 10px 0;
}

.data-table .data-row {
  border-bottom: 1px solid var(--ion-color-light);
  padding: 10px 0;
}

.data-table-header {
  padding-bottom: 0;
}

.data-table-title-container{
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
}

@media (max-width: 576px) {
  .data-table .header-row  {
    display: none;
  }

  .data-table .data-row {
    margin-bottom: 20px;
    border: 1px solid var(--ion-color-light);
    border-radius: 8px;
    padding: 10px;
  }

  .data-table .data-row ion-col {
    display: flex;
    justify-content: space-evenly;
    align-items: center;
    padding: 5px;
  }

  .data-table .data-row ion-col::before {
    font-weight: bold;
    margin-right: 10px;
  }
}
