// backend/src/routes/rowRoutes.ts
import express from 'express';
import {
    createRow,
    getRows,
    // getRowById, // Remove or keep if you need lookup by ID elsewhere
    getRowByNumber, // Add new controller for lookup by number
    updateRow,
    deleteRow,
    swapKartWithTeam, // Keep swap as is (uses IDs)
    swapKartWithTeamSmart, // New smart swap for apex teams
    assignKartToRow,
    unassignKartFromRow, // <-- Import new controller
} from '../controllers/rowController';

const router = express.Router();

// --- Row CRUD ---
router.post('/', createRow); // Create (uses sequential number)
router.get('/', getRows); // Get all
router.get('/:rowNumber', getRowByNumber); // Get by number
router.put('/:rowNumber', updateRow); // Update by number
router.delete('/:rowNumber', deleteRow); // Delete by number

// --- Specific Actions ---
// Swap still uses IDs as it links two specific documents
router.post('/:rowId/swap-team-kart/:teamId', swapKartWithTeam);
// --- NEW: Smart swap that works with both regular and apex teams ---
router.post('/:rowId/smart-swap/:teamId', swapKartWithTeamSmart);
// --- NEW: Assign Kart to Row ---
router.post('/:rowId/assign-kart/:kartId', assignKartToRow); // <-- Add this route
// --- NEW: Unassign Kart from Row (Empty Row) ---
router.delete('/:rowId/kart', unassignKartFromRow); // <-- Add this route

export default router;
