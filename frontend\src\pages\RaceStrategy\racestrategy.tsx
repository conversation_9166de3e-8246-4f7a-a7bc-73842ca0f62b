import React from 'react';
import {
    IonPage,
    IonContent,
    IonHeader,
    IonToolbar,
    IonTitle,
    IonButtons,
    IonButton,
    IonIcon,
    IonToast,
    IonRefresher,
    IonRefresherContent,
    IonModal,
    IonMenuButton,
} from '@ionic/react';
import { settingsOutline, refreshOutline } from 'ionicons/icons';

// Components
import RaceHeader from './components/RaceHeader';
import StintList from './components/StintList';
import ErrorDisplay from './components/ErrorDisplay';
import NoDataMessage from './components/NoDataMessage';
import EditStrategyModal from './components/EditStrategyModal';
import StrategyModal from './components/StrategyModal';
import RaceManagementModal from './components/RaceManagementModal';

// Hook
import { useRaceStrategyLogic } from './useRaceStrategyLogic';

// Context
import { useDataContext } from '../../context/DataContext';

// Styles
import './racestrategy.css';

// --- API Base URL ---
const API_BASE_URL = import.meta.env.VITE_API_URL;

const RaceStrategy: React.FC = () => {
    // Data Context
    const {
        useApexDatabase,
        selectedSession,
        selectedTeam,
        availableTeams,
        isLoadingTeams,
        refreshTeams,
        error: contextError
    } = useDataContext();

    const {
        strategyData,
        isLoading,
        error,
        toastMessage,
        toastColor,
        showEditStrategyModal,
        currentRaceTime,
        showStrategyModal,
        showManagementModal,
        showDeleteAlert,
        stintStrategy,
        fetchStrategyData,
        handleEditStrategy,
        handleDeleteStrategy,
        confirmDeleteStrategy,
        handleStrategyUpdate,
        handleCreateStrategy,
        handleRefresh,
        handleSelectStrategy,
        handleCalculateStints,
        handleStintUpdated,
        setShowEditStrategyModal,
        setShowStrategyModal,
        setShowManagementModal,
        setShowDeleteAlert,
        setToastMessage,
        getCurrentStintNumber,
        getPitStopsDone,
        getExtraStintsUsed,
        getTotalExtraStints,
    } = useRaceStrategyLogic();

    return (
        <IonPage>
            <IonHeader>
                <IonToolbar>
                    <IonButtons slot="start">
                        <IonMenuButton></IonMenuButton>
                    </IonButtons>
                    <IonTitle>Race Strategy</IonTitle>
                    <IonButtons slot="end">
                        <IonButton onClick={() => setShowManagementModal(true)}>
                            <IonIcon icon={settingsOutline} />
                        </IonButton>
                    </IonButtons>
                </IonToolbar>
            </IonHeader>

            <IonContent>
                <IonRefresher slot="fixed" onIonRefresh={handleRefresh}>
                    <IonRefresherContent pullingIcon={refreshOutline}></IonRefresherContent>
                </IonRefresher>

                <div className="ion-padding">
                    {!isLoading && error && (
                        <ErrorDisplay error={error} onRetry={fetchStrategyData} />
                    )}

                    {!isLoading && !error && strategyData && (
                        <div className="compact-race-header">
                            <RaceHeader
                                strategyData={strategyData}
                                currentRaceTime={currentRaceTime}
                                progress={stintStrategy?.progress || 0}
                                onClick={() => setShowManagementModal(true)}
                                currentStint={getCurrentStintNumber()}
                                totalStints={stintStrategy?.stints.length || 0}
                                pitsDone={getPitStopsDone()}
                                totalPits={strategyData?.mandatoryPitStops || 0}
                                extrasUsed={getExtraStintsUsed()}
                                totalExtras={getTotalExtraStints()}
                                stintStrategy={stintStrategy}
                            />
                        </div>
                    )}

                    {/* Message when strategy is loaded but stints are not calculated */}
                    {!isLoading && !error && strategyData && !stintStrategy && (
                        <p className="ion-text-center ion-padding-top">Stints not calculated. Open Race Management to calculate.</p>
                    )}

                    {/* Stints view */}
                    {!isLoading && !error && strategyData && stintStrategy && (
                        <>
                            <StintList
                                stints={stintStrategy.stints}
                                currentRaceTime={currentRaceTime}
                                strategyData={strategyData}
                                totalRaceTime={stintStrategy.totalRaceTimeSeconds}
                                onStintUpdated={handleStintUpdated}
                            />
                        </>
                    )}

                    {/* No data message */}
                    {!isLoading && !error && !strategyData && (
                        <NoDataMessage onCreateStrategy={() => setShowStrategyModal(true)} />
                    )}
                </div>

                {/* Toast for notifications */}
                {toastMessage && (
                    <IonToast
                        isOpen={!!toastMessage}
                        onDidDismiss={() => setToastMessage(null)}
                        message={toastMessage}
                        duration={2000}
                        color={toastColor}
                    />
                )}

                {/* Edit Strategy Modal */}
                <EditStrategyModal
                    isOpen={showEditStrategyModal}
                    onClose={() => setShowEditStrategyModal(false)}
                    initialStrategy={strategyData}
                    onSave={handleStrategyUpdate}
                    isNewStrategy={false}
                    apiBaseUrl={API_BASE_URL}
                />

                {/* Strategy Selection Modal */}
                <StrategyModal
                    isOpen={showStrategyModal}
                    onClose={() => setShowStrategyModal(false)}
                    onSelectStrategy={handleSelectStrategy}
                    activeStrategyId={strategyData?._id || null}
                    apiBaseUrl={API_BASE_URL}
                />

                {/* Race Management Modal */}
                <RaceManagementModal
                    isOpen={showManagementModal}
                    onClose={() => setShowManagementModal(false)}
                    strategyData={strategyData || {
                        raceName: '',
                        raceDurationValue: 0,
                        raceDurationType: 'time',
                        mandatoryPitStops: 0,
                        minStintTimeSeconds: 0,
                        maxStintTimeSeconds: 0,
                        pitWindowOpenValue: 0,
                        pitWindowOpenType: 'time',
                        pitWindowCloseValue: 0,
                        pitWindowCloseType: 'time',
                        avgLapTimeSeconds: 0,
                        minPitDurationSeconds: 0
                    }}
                    onEditStrategy={handleEditStrategy}
                    onDeleteStrategy={handleDeleteStrategy}
                    onCreateStrategy={handleCreateStrategy}
                    onSwitchStrategy={() => setShowStrategyModal(true)}
                    onRecalculateStints={handleCalculateStints}
                />

                {/* Delete Confirmation Alert */}
                {showDeleteAlert && (
                    <IonModal isOpen={showDeleteAlert} onDidDismiss={() => setShowDeleteAlert(false)}>
                        <div className="ion-padding">
                            <h2>Confirm Delete</h2>
                            <p>Are you sure you want to delete this strategy? This action cannot be undone.</p>
                            <IonButton onClick={() => setShowDeleteAlert(false)}>Cancel</IonButton>
                            <IonButton color="danger" onClick={confirmDeleteStrategy}>Delete</IonButton>
                        </div>
                    </IonModal>
                )}
            </IonContent>
        </IonPage>
    );
};

export default RaceStrategy;
