// d:/Desktop/ManulilacRaceplanner/backend/src/utils/seedData.ts
import { Row } from '../models/Row';
import RaceStrategy, { IRaceStrategy } from '../models/RaceStrategy'; // Adjust path as needed

// Define the default rows you want
const defaultRowsData = [
  { rowNumber: 1, color: 'blue' },
];

// Define the default race strategy
const DEFAULT_STRATEGY: Partial<IRaceStrategy> = {
    raceName: 'Default Race',
    raceDurationValue: 120, // e.g., 120 minutes
    raceDurationType: 'time',
    mandatoryPitStops: 1,
    minStintTimeSeconds: 60 * 5, // 5 minutes
    maxStintTimeSeconds: 60 * 65, // 65 minutes
    pitWindowOpenValue: 60 * 10, // 10 minutes into race
    pitWindowOpenType: 'time',
    pitWindowCloseValue: 60 * 110, // 110 minutes into race
    pitWindowCloseType: 'time',
    avgLapTimeSeconds: 95, // 1 minute 35 seconds
    minPitDurationSeconds: 70, // 1 minute 10 seconds
    // startTime: undefined, // Let user set this
    // calculatedStintTimeSeconds: undefined,
    // actualStintTimesSeconds: [],
    // actualPitTimesSeconds: [],
};

// --- Internal Seeding Functions ---

const seedDefaultRows = async () => {
  console.log('[Seed] Attempting to seed default rows...');
  try {
    for (const rowData of defaultRowsData) {
      // Use findOneAndUpdate with upsert: true
      // This finds a row by rowNumber. If it exists, it does nothing (or updates if you add $set).
      // If it doesn't exist (upsert: true), it creates it using the $setOnInsert data.
      const result = await Row.findOneAndUpdate(
        { rowNumber: rowData.rowNumber }, // Find condition
        {
          $setOnInsert: { // Only set these fields when inserting (upserting)
            rowNumber: rowData.rowNumber,
            color: rowData.color,
            currentKartId: null,
            pastKarts: [],
          }
        },
        { upsert: true, new: true }
      );
      console.log(`[Seed] Row ${rowData.rowNumber} ${result.isNew ? 'created' : 'already exists'}`);
    }
    console.log('[Seed] Default rows seeding completed.');
  } catch (error) {
    console.error('[Seed] Error seeding default rows:', error);
    throw error; // Re-throw to indicate seeding failure during startup
  }
};

const seedDefaultStrategy = async () => {
    console.log('[Seed] Attempting to seed default race strategy...');
    try {
        const count = await RaceStrategy.countDocuments();
        if (count === 0) {
            console.log('[Seed] Initializing default race strategy in database...');
            await RaceStrategy.create(DEFAULT_STRATEGY);
            console.log('[Seed] Default race strategy created successfully.');
        } else {
            console.log('[Seed] Race strategy already exists in database.');
        }
    } catch (error) {
        console.error('[Seed] Error seeding default race strategy:', error);
        throw error; // Re-throw to indicate seeding failure during startup
    }
};

// --- Exported Combined Seeding Function ---

/**
 * Seeds the database with initial data (Default Rows and Default Race Strategy)
 * if they do not already exist. Should be called once on application startup
 * after the database connection is established.
 */
export const seedInitialData = async () => {
    console.log('[Seed] Starting initial data seeding...');
    try {
        await seedDefaultRows();
        await seedDefaultStrategy();
        console.log('[Seed] Initial data seeding finished successfully.');
    } catch (error) {
        console.error('[Seed] Failed during initial data seeding process. Application startup might be affected.');
        // Decide if the application should exit if seeding fails critically
        // process.exit(1);
    }
};
