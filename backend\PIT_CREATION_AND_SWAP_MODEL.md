# Pit Creation Fix and Kart Swap Model

## 🔧 **Fix 1: Avoid Creating Pit Without Pit In Message**

### **Problem**
The system was automatically creating pit stops when it received pit duration updates but no active pit stop existed, leading to incorrect pit entries.

### **Before (Automatic Pit Creation)**
```typescript
// ❌ Automatically created pit stops from duration updates
if (!activePitStop && durationSeconds > 0) {
  console.log(`⚠️ Pit duration update for ${competitor.name} but no active pit stop found - creating pit entry`);
  await this.handlePitEntry(competitor, competitorObjectId);
}
```

**Problem:** This created pit stops without explicit pit in messages, leading to:
- Incorrect pit timing
- Missing proper pit entry detection
- Inconsistent pit data

### **After (Wait for Explicit Pit In)**
```typescript
// ✅ Wait for explicit pit in message instead of creating automatically
if (!activePitStop && durationSeconds > 0) {
  console.log(`⚠️ Pit duration update for ${competitor.name} but no active pit stop found - skipping (waiting for pit in message)`);
  return; // Skip processing until proper pit in message arrives
}
```

**Benefits:**
- **Accurate pit detection**: Only creates pit stops from explicit pit in messages (`r{id}c1|si|`)
- **Proper timing**: Pit entry time reflects actual pit in message timing
- **Data integrity**: No phantom pit stops from duration-only messages

### **Expected Behavior**
```
🔍 OTR FIELD DETECTED: competitorId=17788, data.type=to, data.value=14.
⚠️ Pit duration update for TEAM NAME but no active pit stop found - skipping (waiting for pit in message)

🏁 PIT IN detected: 17788 (status field si)
🏁 Creating pit entry for: TEAM NAME
✅ Pit entry recorded successfully

🔍 OTR FIELD DETECTED: competitorId=17788, data.type=to, data.value=30.
⏱️ Updated pit current duration: TEAM NAME - 30s
```

## 🔧 **Fix 2: Kart Swap Database Model**

### **New ApexKartSwap Model**

**Interface:**
```typescript
export interface IApexKartSwap extends Document {
  sessionId: mongoose.Types.ObjectId;
  teamId: mongoose.Types.ObjectId; // Team that performed the swap
  pitStopId: mongoose.Types.ObjectId; // Pit stop during which swap occurred
  oldKartId: mongoose.Types.ObjectId; // Kart being swapped out
  newKartId: mongoose.Types.ObjectId; // Kart being swapped in
  pitRowId?: mongoose.Types.ObjectId; // Optional reference to pit row where swap occurred
  swapTime: Date; // When the swap occurred
  raceTimeAtSwap?: number; // Race time when swap occurred (seconds)
  reason?: string; // Optional reason for swap (mechanical, strategic, etc.)
  isActive: boolean;
}
```

**Schema Features:**
```typescript
const ApexKartSwapSchema = new Schema<IApexKartSwap>({
  sessionId: { type: Schema.Types.ObjectId, ref: 'ApexSession', required: true, index: true },
  teamId: { type: Schema.Types.ObjectId, ref: 'ApexTeam', required: true, index: true },
  pitStopId: { type: Schema.Types.ObjectId, ref: 'ApexPitStop', required: true, index: true },
  oldKartId: { type: Schema.Types.ObjectId, ref: 'ApexKart', required: true, index: true },
  newKartId: { type: Schema.Types.ObjectId, ref: 'ApexKart', required: true, index: true },
  pitRowId: { type: Schema.Types.ObjectId, ref: 'PitRow', required: false },
  swapTime: { type: Date, default: Date.now, required: true },
  raceTimeAtSwap: { type: Number, required: false },
  reason: { type: String, required: false },
  isActive: { type: Boolean, default: true }
}, {
  timestamps: true,
  collection: 'apex_kart_swaps'
});
```

**Optimized Indexes:**
```typescript
// Efficient queries for different use cases
ApexKartSwapSchema.index({ sessionId: 1, teamId: 1 }); // Team swap history
ApexKartSwapSchema.index({ pitStopId: 1 }); // Swaps during specific pit stop
ApexKartSwapSchema.index({ oldKartId: 1 }); // Track kart being swapped out
ApexKartSwapSchema.index({ newKartId: 1 }); // Track kart being swapped in
ApexKartSwapSchema.index({ swapTime: -1 }); // Recent swaps first
```

## 🎯 **Frontend Integration**

### **Check if Pit Stop Has Kart Swap**
```typescript
// Query to check if a pit stop had a kart swap
const hasKartSwap = await ApexKartSwap.findOne({
  pitStopId: pitStopObjectId,
  isActive: true
});

if (hasKartSwap) {
  console.log(`Pit stop had kart swap: ${hasKartSwap.oldKartId} → ${hasKartSwap.newKartId}`);
}
```

### **Get Team's Swap History**
```typescript
// Get all swaps for a team in a session
const teamSwaps = await ApexKartSwap.find({
  sessionId: sessionObjectId,
  teamId: teamObjectId,
  isActive: true
}).sort({ swapTime: -1 });

console.log(`Team has performed ${teamSwaps.length} kart swaps`);
```

### **Get Kart Usage History**
```typescript
// Track which teams used a specific kart
const kartUsage = await ApexKartSwap.find({
  $or: [
    { oldKartId: kartObjectId },
    { newKartId: kartObjectId }
  ],
  sessionId: sessionObjectId,
  isActive: true
}).populate('teamId');
```

### **Pit Stop with Swap Information**
```typescript
// Enhanced pit stop query with swap information
const pitStopWithSwap = await ApexPitStop.aggregate([
  { $match: { _id: pitStopObjectId } },
  {
    $lookup: {
      from: 'apex_kart_swaps',
      localField: '_id',
      foreignField: 'pitStopId',
      as: 'kartSwap'
    }
  },
  {
    $addFields: {
      hasKartSwap: { $gt: [{ $size: '$kartSwap' }, 0] },
      swapDetails: { $arrayElemAt: ['$kartSwap', 0] }
    }
  }
]);
```

## 📊 **Use Cases**

### **1. Track Team Strategy**
```typescript
// Identify teams that frequently swap karts
const teamSwapStats = await ApexKartSwap.aggregate([
  { $match: { sessionId: sessionObjectId, isActive: true } },
  { $group: { _id: '$teamId', swapCount: { $sum: 1 } } },
  { $sort: { swapCount: -1 } }
]);
```

### **2. Kart Reliability Analysis**
```typescript
// Find karts that are frequently swapped out (potential issues)
const problematicKarts = await ApexKartSwap.aggregate([
  { $match: { sessionId: sessionObjectId, isActive: true } },
  { $group: { _id: '$oldKartId', swapOutCount: { $sum: 1 } } },
  { $sort: { swapOutCount: -1 } }
]);
```

### **3. Pit Stop Analysis**
```typescript
// Analyze pit stops with and without swaps
const pitStopAnalysis = await ApexPitStop.aggregate([
  { $match: { sessionId: sessionObjectId } },
  {
    $lookup: {
      from: 'apex_kart_swaps',
      localField: '_id',
      foreignField: 'pitStopId',
      as: 'swaps'
    }
  },
  {
    $group: {
      _id: null,
      totalPitStops: { $sum: 1 },
      pitStopsWithSwaps: { $sum: { $cond: [{ $gt: [{ $size: '$swaps' }, 0] }, 1, 0] } },
      avgDurationWithSwap: { $avg: { $cond: [{ $gt: [{ $size: '$swaps' }, 0] }, '$pitDuration', null] } },
      avgDurationWithoutSwap: { $avg: { $cond: [{ $eq: [{ $size: '$swaps' }, 0] }, '$pitDuration', null] } }
    }
  }
]);
```

## ✅ **Benefits**

### **1. Accurate Pit Detection**
- **No phantom pit stops**: Only creates pits from explicit pit in messages
- **Proper timing**: Accurate pit entry and duration tracking
- **Data integrity**: Consistent pit stop data

### **2. Comprehensive Swap Tracking**
- **Complete audit trail**: Track all kart swaps with full context
- **Strategic analysis**: Understand team swap strategies
- **Performance correlation**: Analyze swap impact on performance

### **3. Frontend Integration**
- **Easy queries**: Optimized indexes for common frontend needs
- **Flexible analysis**: Support for various analytical queries
- **Real-time updates**: Track swaps as they happen

### **4. Database Efficiency**
- **Proper relationships**: ObjectId references for optimal performance
- **Targeted indexes**: Fast queries for common use cases
- **Scalable design**: Handles large numbers of swaps efficiently

## 🔧 **Expected Results**

**Run your log replay and you should see:**

1. **No automatic pit creation** from duration updates
2. **Proper pit creation** only from pit in messages (`si`)
3. **Clean pit timing** with accurate entry detection
4. **New ApexKartSwap collection** ready for swap tracking
5. **Optimized database queries** for frontend integration

**Both changes improve data accuracy and provide comprehensive kart swap tracking capabilities!** 🏁
