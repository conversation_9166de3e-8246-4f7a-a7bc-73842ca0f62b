import React, { useState, useEffect } from 'react';
import {
  IonModal,
  IonHeader,
  IonToolbar,
  IonTitle,
  IonButtons,
  IonButton,
  IonContent,
  IonIcon,
  IonBadge,
  IonInput,
} from '@ionic/react';
import {
  pencilOutline,
  closeOutline
} from 'ionicons/icons';
import { StintInfo } from '../types';
import { displaySecondsAsHHMMSS, displaySecondsAsMMSS, displaySecondsAsMMSSFFF } from '../utils/timeFormatters';
import { stintApi } from '../../../services/apiService';
import axios from 'axios'; // Import axios for type checking
import './StintDetailsModal.css';

interface StintDetailsModalProps {
  isOpen: boolean;
  onClose: () => void;
  stint: StintInfo;
  isActive: boolean;
  isDone: boolean;
  isLastStint: boolean; // Added to accept the prop
  isExtraStint: boolean;
  stints?: StintInfo[];
  onStintUpdated: (stint: StintInfo) => Promise<void>;
  setErrorMessage: (message: string) => void;
  setShowErrorToast: (show: boolean) => void;
}

const StintDetailsModal: React.FC<StintDetailsModalProps> = ({
  isOpen,
  onClose,
  stint,
  isActive,
  onStintUpdated,
  setErrorMessage,
  setShowErrorToast
}) => {
  // State for edit dialog
  const [showEditDialog, setShowEditDialog] = useState<boolean>(false);

  // Split input states for stint duration
  const [stintHours, setStintHours] = useState<string>("00");
  const [stintMinutes, setStintMinutes] = useState<string>("00");
  const [stintSeconds, setStintSeconds] = useState<string>("00");

  // Split input states for pit duration
  const [pitMinutes, setPitMinutes] = useState<string>("00");
  const [pitSeconds, setPitSeconds] = useState<string>("00");
  const [pitMilliseconds, setPitMilliseconds] = useState<string>("000");

  // Calculate planned pit duration
  const plannedPitDuration = stint.pitEndTime && stint.endTime
    ? stint.pitEndTime - stint.endTime
    : null;

  // Initialize input fields when dialog opens
  useEffect(() => {
    if (showEditDialog) {
      // Initialize stint duration fields
      if (stint.actualStintDuration) {
        const hours = Math.floor(stint.actualStintDuration / 3600);
        const minutes = Math.floor((stint.actualStintDuration % 3600) / 60);
        const seconds = Math.floor(stint.actualStintDuration % 60);

        setStintHours(hours.toString().padStart(2, '0'));
        setStintMinutes(minutes.toString().padStart(2, '0'));
        setStintSeconds(seconds.toString().padStart(2, '0'));
      } else {
        // Use planned duration as default
        const hours = Math.floor(stint.duration / 3600);
        const minutes = Math.floor((stint.duration % 3600) / 60);
        const seconds = Math.floor(stint.duration % 60);

        setStintHours(hours.toString().padStart(2, '0'));
        setStintMinutes(minutes.toString().padStart(2, '0'));
        setStintSeconds(seconds.toString().padStart(2, '0'));
      }

      // Initialize pit duration fields
      if (stint.actualPitDuration) {
        const minutes = Math.floor(stint.actualPitDuration / 60);
        const seconds = Math.floor(stint.actualPitDuration % 60);
        const milliseconds = Math.round((stint.actualPitDuration % 1) * 1000);

        setPitMinutes(minutes.toString().padStart(2, '0'));
        setPitSeconds(seconds.toString().padStart(2, '0'));
        setPitMilliseconds(milliseconds.toString().padStart(3, '0'));
      } else if (plannedPitDuration) {
        const minutes = Math.floor(plannedPitDuration / 60);
        const seconds = Math.floor(plannedPitDuration % 60);

        setPitMinutes(minutes.toString().padStart(2, '0'));
        setPitSeconds(seconds.toString().padStart(2, '0'));
        setPitMilliseconds("000");
      } else {
        setPitMinutes("00");
        setPitSeconds("00");
        setPitMilliseconds("000");
      }
    }
  }, [showEditDialog, stint, plannedPitDuration]);

  // Input filtering functions
  const filterNumericInput = (value: string, max: number, digits: number = 2): string => {
    // Remove non-numeric characters
    let filtered = value.replace(/[^0-9]/g, '');

    // Limit to max value
    const num = parseInt(filtered, 10);
    if (!isNaN(num) && num > max) {
      filtered = max.toString();
    }

    // Pad with leading zeros
    return filtered.padStart(digits, '0').slice(-digits);
  };

  // Handle saving stint updates
  const handleSaveStint = async () => {
    try {
      // Calculate stint duration in seconds
      const stintDurationSeconds =
        parseInt(stintHours, 10) * 3600 +
        parseInt(stintMinutes, 10) * 60 +
        parseInt(stintSeconds, 10);

      // Calculate pit duration in seconds
      const pitDurationSeconds =
        parseInt(pitMinutes, 10) * 60 +
        parseInt(pitSeconds, 10) +
        parseInt(pitMilliseconds, 10) / 1000;

      // Calculate the actual pit entry time
      const actualPitEntryTime = stint.startTime + stintDurationSeconds;

      // Ensure stint._id is defined before making the API call
      if (!stint._id) {
        setErrorMessage("Stint ID is missing. Cannot save changes.");
        setShowErrorToast(true);
        return;
      }

      // Call the API to update the stint
      const response = await stintApi.updateActuals(
        stint._id,
        actualPitEntryTime,
        pitDurationSeconds
      );

      // Call the parent component's callback with the updated stint
      if (response && response.stint) {
        await onStintUpdated(response.stint);
      }

      setShowEditDialog(false);
    } catch (error: unknown) {
      console.error('Error updating stint:', error);

      // Extract error message from the response if available
      let message = 'Failed to update stint. Please try again.';
      if (axios.isAxiosError(error)) {
        // Access specific Axios error properties
        message = error.response?.data?.message || error.message || message;
      } else if (error instanceof Error) {
        // Standard Error object
        message = error.message;
      }

      setErrorMessage(message);
      setShowErrorToast(true);
    }
  };

  return (
    <>
      <IonModal isOpen={isOpen} onDidDismiss={onClose}>
        <IonHeader className="stint-header">
          <IonToolbar className="stint-header-toolbar">
            <IonTitle className="stint-title">
              Stint {stint.stintNumber}
              {isActive && <IonBadge color="success" className="stint-badge">ACTIVE</IonBadge>}
            </IonTitle>
            <IonButtons slot="end">
              <IonButton onClick={() => setShowEditDialog(true)}>
                <IonIcon slot="icon-only" icon={pencilOutline} />
              </IonButton>
              <IonButton onClick={onClose}>
                <IonIcon slot="icon-only" icon={closeOutline} />
              </IonButton>
            </IonButtons>
          </IonToolbar>
        </IonHeader>

        <IonContent className="stint-modal-content">
          <div className="stint-main">
            {/* Time Display Section */}
            <div className="time-display">
              <div className="time-display-item">
                <div className="time-display-label">Start Time</div>
                <div className="time-display-value">{displaySecondsAsHHMMSS(stint.startTime)}</div>
              </div>
              <div className="time-display-item">
                <div className="time-display-label">End Time</div>
                <div className="time-display-value">{displaySecondsAsHHMMSS(stint.endTime)}</div>
              </div>
              {stint.pitEndTime && (
                <div className="time-display-item">
                  <div className="time-display-label">Pit End</div>
                  <div className="time-display-value">{displaySecondsAsHHMMSS(stint.pitEndTime)}</div>
                </div>
              )}
            </div>

            {/* Stats Display */}
            <div className="stats-display">
              <div className="stat-item">
                <div className="stat-label">Duration</div>
                <div className="stat-value">{displaySecondsAsMMSS(stint.duration)}</div>
              </div>
              <div className="stat-item">
                <div className="stat-label">Laps</div>
                <div className="stat-value">{stint.laps}</div>
              </div>
              {stint.pitEndTime && (
                <div className="stat-item">
                  <div className="stat-label">Pit Time</div>
                  <div className="stat-value">
                    {plannedPitDuration ? displaySecondsAsMMSSFFF(plannedPitDuration) : "N/A"}
                  </div>
                </div>
              )}
            </div>
          </div>
        </IonContent>
      </IonModal>

      {/* Edit Modal with Modern UI */}
      <IonModal isOpen={showEditDialog} onDidDismiss={() => setShowEditDialog(false)}>
        <IonHeader>
          <IonToolbar>
            <IonTitle>Edit Stint {stint.stintNumber}</IonTitle>
            <IonButtons slot="end">
              <IonButton onClick={() => setShowEditDialog(false)}>
                <IonIcon slot="icon-only" icon={closeOutline} />
              </IonButton>
            </IonButtons>
          </IonToolbar>
        </IonHeader>

        <IonContent className="ion-padding">
          {/* Stint Duration */}
          <div className="edit-card">
            <h2>Stint Duration</h2>
            <div className="time-input-group">
              <div className="time-input-container">
                <IonInput
                  type="number"
                  value={stintHours}
                  onIonChange={(e) => setStintHours(filterNumericInput(e.detail.value || "", 23))}
                  placeholder="00"
                  min="0"
                  max="23"
                  className="time-input"
                ></IonInput>
                <label>Hours</label>
              </div>
              
              <div className="separator">:</div>
              
              <div className="time-input-container">
                <IonInput
                  type="number"
                  value={stintMinutes}
                  onIonChange={(e) => setStintMinutes(filterNumericInput(e.detail.value || "", 59))}
                  placeholder="00"
                  min="0"
                  max="59"
                  className="time-input"
                ></IonInput>
                <label>Minutes</label>
              </div>
              
              <div className="separator">:</div>
              
              <div className="time-input-container">
                <IonInput
                  type="number"
                  value={stintSeconds}
                  onIonChange={(e) => setStintSeconds(filterNumericInput(e.detail.value || "", 59))}
                  placeholder="00"
                  min="0"
                  max="59"
                  className="time-input"
                ></IonInput>
                <label>Seconds</label>
              </div>
            </div>
          </div>
          
          {/* Pit Duration - Only show if there's a pit end time */}
          {stint.pitEndTime && (
            <div className="edit-card">
              <h2>Pit Duration</h2>
             
              <div className="time-input-group">
                <div className="time-input-container">
                  <IonInput
                    type="number"
                    value={pitMinutes}
                    onIonChange={(e) => setPitMinutes(filterNumericInput(e.detail.value || "", 59))}
                    placeholder="00"
                    min="0"
                    max="59"
                    className="time-input"
                  ></IonInput>
                  <label>Minutes</label>
                </div>
                
                <div className="separator">:</div>
                
                <div className="time-input-container">
                  <IonInput
                    type="number"
                    value={pitSeconds}
                    onIonChange={(e) => setPitSeconds(filterNumericInput(e.detail.value || "", 59))}
                    placeholder="00"
                    min="0"
                    max="59"
                    className="time-input"
                  ></IonInput>
                  <label>Seconds</label>
                </div>
                
                <div className="separator">.</div>
                
                <div className="time-input-container">
                  <IonInput
                    type="number"
                    value={pitMilliseconds}
                    onIonChange={(e) => setPitMilliseconds(filterNumericInput(e.detail.value || "", 999, 3))}
                    placeholder="000"
                    min="0"
                    max="999"
                    className="time-input ms-input"
                  ></IonInput>
                  <label>Millisec</label>
                </div>
              </div>
            </div>
          )}
          
          {/* Action Buttons */}
          <div className="button-group">
            <IonButton expand="block" onClick={handleSaveStint}>
              Save Changes
            </IonButton>
            <IonButton expand="block" fill="outline" onClick={() => setShowEditDialog(false)}>
              Cancel
            </IonButton>
          </div>
        </IonContent>
      </IonModal>
    </>
  );
};

export default StintDetailsModal;
