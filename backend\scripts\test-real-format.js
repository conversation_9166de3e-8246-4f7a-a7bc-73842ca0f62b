const mongoose = require('mongoose');

const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/raceplanner';

async function testRealFormat() {
  try {
    await mongoose.connect(MONGODB_URI);
    console.log('✅ Connected to MongoDB');

    // Import models
    const { ApexSession, ApexPitStop, ApexTeam } = require('../dist/models/ApexModels');
    const { ApexParserSimple } = require('../dist/services/apexParserSimple');

    // Clean up
    await ApexSession.deleteMany({ title1: { $regex: /Real Format Test/ } });

    console.log('\n🔍 Testing Real 24h Serres Format...\n');

    const parser = new ApexParserSimple({ enableLogging: true });

    // Create session with grid (based on your HTML structure)
    const gridMessage = `init|r|
title1||Real Format Test
title2||24h Serres Format
track||Test Track
grid||<tbody><tr data-id="r0" class="head" data-pos="0"><td data-id="c1" data-type="sta" data-pr="1"></td><td data-id="c2" data-type="rk" data-pr="1">Rnk</td><td data-id="c3" data-type="no" data-pr="1">Kart</td><td data-id="c4" data-type="nat" data-pr="5" data-width="5">Nation</td><td data-id="c5" data-type="dr" data-pr="1" data-width="17" data-min="16">Team</td><td data-id="c6" data-type="s1" data-pr="3" data-width="7" data-min="6">S1</td><td data-id="c7" data-type="s2" data-pr="3" data-width="7" data-min="6">S2</td><td data-id="c8" data-type="s3" data-pr="3" data-width="7" data-min="6">S3</td><td data-id="c9" data-type="llp" data-pr="2" data-width="9" data-min="7">Last lap</td><td data-id="c10" data-type="gap" data-pr="1" data-width="2" data-min="8">Gap</td><td data-id="c11" data-type="int" data-pr="5" data-width="6" data-min="7">Interv.</td><td data-id="c12" data-type="blp" data-pr="6" data-width="7" data-min="7">Best lap</td><td data-id="c13" data-type="tlp" data-pr="5" data-width="3" data-min="4">Laps</td><td data-id="c14" data-type="otr" data-pr="2" data-width="6" data-min="4">On track</td><td data-id="c15" data-type="pit" data-pr="2" data-width="4" data-min="7">Pits</td></tr><tr data-id="r17768"><td data-id="r17768c1">N/A</td><td data-id="r17768c2">1</td><td data-id="r17768c3">99</td><td data-id="r17768c4">FRA</td><td data-id="r17768c5">TEST TEAM 1</td><td data-id="r17768c6">00:00:00</td><td data-id="r17768c7">00:00:00</td><td data-id="r17768c8">00:00:00</td><td data-id="r17768c9">00:00:00</td><td data-id="r17768c10">00:00:00</td><td data-id="r17768c11">00:00:00</td><td data-id="r17768c12">00:00:00</td><td data-id="r17768c13">0</td><td data-id="r17768c14">00:00:00</td><td data-id="r17768c15">0</td></tr><tr data-id="r17778"><td data-id="r17778c1">N/A</td><td data-id="r17778c2">2</td><td data-id="r17778c3">88</td><td data-id="r17778c4">GER</td><td data-id="r17778c5">TEST TEAM 2</td><td data-id="r17778c6">00:00:00</td><td data-id="r17778c7">00:00:00</td><td data-id="r17778c8">00:00:00</td><td data-id="r17778c9">00:00:00</td><td data-id="r17778c10">00:00:00</td><td data-id="r17778c11">00:00:00</td><td data-id="r17778c12">00:00:00</td><td data-id="r17778c13">0</td><td data-id="r17778c14">00:00:00</td><td data-id="r17778c15">0</td></tr></tbody>`;

    console.log('📊 Creating session...');
    await parser.parseMessage(gridMessage);
    await new Promise(resolve => setTimeout(resolve, 1000));

    console.log('\n⏱️ Setting race duration...');
    await parser.parseMessage('dyn1|text|5400000'); // 90 minutes
    await new Promise(resolve => setTimeout(resolve, 500));

    await parser.parseMessage('dyn1|text|5385000'); // 15 seconds elapsed
    await new Promise(resolve => setTimeout(resolve, 500));

    console.log('\n🏁 Testing PIT IN sequence...');
    
    // PIT IN sequence from your example
    console.log('--- r17768c1|si| (pit in status) ---');
    await parser.parseMessage('r17768c1|si|');
    await new Promise(resolve => setTimeout(resolve, 500));

    console.log('--- r17768c14|to|00. (0 seconds in pit) ---');
    await parser.parseMessage('r17768c14|to|00.');
    await new Promise(resolve => setTimeout(resolve, 500));

    console.log('--- r17768c15|in|7 (7th pit stop) ---');
    await parser.parseMessage('r17768c15|in|7');
    await new Promise(resolve => setTimeout(resolve, 500));

    console.log('\n⏱️ Testing PIT SECONDS COUNTER...');
    
    // Pit seconds counter from your example
    console.log('--- r17778c14|to|2:30. (2 minutes 30 seconds in pit) ---');
    await parser.parseMessage('r17778c14|to|2:30.');
    await new Promise(resolve => setTimeout(resolve, 500));

    console.log('\n🏁 Testing PIT OUT sequence...');
    
    // PIT OUT sequence from your example
    console.log('--- r17778c1|so| (pit out status) ---');
    await parser.parseMessage('r17778c1|so|');
    await new Promise(resolve => setTimeout(resolve, 500));

    // Check results
    console.log('\n📊 Checking database results...');
    
    const sessions = await ApexSession.find({ title1: { $regex: /Real Format Test/ } }).lean();
    if (sessions.length === 0) {
      console.log('❌ No sessions found');
      return;
    }

    const session = sessions[0];
    const pitStops = await ApexPitStop.find({ sessionId: session._id }).lean();
    const teams = await ApexTeam.find({ sessionId: session._id }).lean();
    
    console.log(`\n🏁 Found ${pitStops.length} pit stops`);
    console.log(`👥 Found ${teams.length} teams`);
    
    if (pitStops.length > 0) {
      console.log('\n📊 Pit Stop Details:');
      pitStops.forEach((pit, index) => {
        console.log(`   ${index + 1}. Competitor: ${pit.competitorId}`);
        console.log(`      Pit In Time: ${pit.pitInTime}`);
        console.log(`      Pit Out Time: ${pit.pitOutTime || 'ACTIVE'}`);
        console.log(`      Current Duration: ${pit.pitCurrentDuration || 0}s`);
        console.log(`      Race Time In: ${pit.raceTimeAtPitIn || 'N/A'}s`);
        console.log(`      Race Time Out: ${pit.raceTimeAtPitOut || 'N/A'}s`);
        console.log('');
      });
    }

    if (teams.length > 0) {
      console.log('👥 Team Status:');
      teams.forEach((team, index) => {
        console.log(`   ${index + 1}. ${team.name}: status = ${team.status}`);
      });
    }

    // Verify parsing results
    console.log('\n🔍 Parsing Verification:');
    
    // Check if pit in was detected
    const pitInDetected = pitStops.some(pit => pit.pitInTime && !pit.pitOutTime);
    console.log(`   Pit IN detection: ${pitInDetected ? '✅ WORKING' : '❌ NOT WORKING'}`);
    
    // Check if pit current duration was updated
    const pitDurationUpdated = pitStops.some(pit => pit.pitCurrentDuration && pit.pitCurrentDuration > 0);
    console.log(`   Pit duration update: ${pitDurationUpdated ? '✅ WORKING' : '❌ NOT WORKING'}`);
    
    // Check if race time is calculated
    const raceTimeWorking = pitStops.some(pit => pit.raceTimeAtPitIn && pit.raceTimeAtPitIn > 0);
    console.log(`   Race time calculation: ${raceTimeWorking ? '✅ WORKING' : '❌ NOT WORKING'}`);
    
    // Check team status
    const teamStatusWorking = teams.some(team => team.status === 'in_pit');
    console.log(`   Team status update: ${teamStatusWorking ? '✅ WORKING' : '❌ NOT WORKING'}`);

    if (pitInDetected && pitDurationUpdated && raceTimeWorking && teamStatusWorking) {
      console.log('\n✅ All parsing features are working correctly!');
    } else {
      console.log('\n❌ Some parsing features need attention!');
    }

    // Clean up timer
    parser.cleanup();

    // Clean up database
    await ApexSession.deleteMany({ title1: { $regex: /Real Format Test/ } });

  } catch (error) {
    console.error('❌ Test failed:', error);
  } finally {
    await mongoose.disconnect();
    console.log('\n🔌 Disconnected from MongoDB');
  }
}

testRealFormat().catch(console.error);
