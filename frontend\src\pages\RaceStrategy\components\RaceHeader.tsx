import React from 'react';
import {
  IonBadge,
  IonIcon,
  IonProgressBar
} from '@ionic/react';
import {
  timeOutline,
  flagOutline,
} from 'ionicons/icons';
import { StrategyData } from '../../../types';
import { StrategyResult } from '../types';
import { displaySecondsAsHHMMSS } from '../utils/timeFormatters';
import './RaceHeader.css';

interface RaceHeaderProps {
  strategyData: StrategyData;
  currentRaceTime: number;
  progress: number;
  onClick: () => void;
  currentStint: number;
  totalStints: number;
  pitsDone: number;
  totalPits: number;
  extrasUsed: number;
  totalExtras: number;
  stintStrategy: StrategyResult | null;
}

const RaceHeader: React.FC<RaceHeaderProps> = ({ 
  strategyData, 
  currentRaceTime, 
  progress, 
  onClick,
  currentStint,
  totalStints,
  pitsDone,
  totalPits,
  extrasUsed,
  totalExtras
}) => {
  // Calculate remaining time if race is time-based
  const remainingTime = strategyData.raceDurationType === 'time' 
    ? Math.max(0, strategyData.raceDurationValue - currentRaceTime) 
    : null;
  
  // Determine progress color based on race completion
  const progressColor = progress > 0.8 ? "danger" : progress > 0.5 ? "warning" : "primary";
  
  // Format time display
  const formatTime = (seconds: number) => {
    return displaySecondsAsHHMMSS(seconds);
  };
  
  return (
    <div className="race-header-compact" onClick={onClick}>
      <div className="race-header-top">
        <div className="race-title">
          <h2>{strategyData.raceName}</h2>
          {strategyData.startTime && (
            <IonBadge color={progressColor} className="race-status-badge">
              {progress < 1 ? 'ACTIVE' : 'FINISHED'}
            </IonBadge>
          )}
        </div>
        
        <div className="race-type">
          <IonIcon icon={strategyData.raceDurationType === 'time' ? timeOutline : flagOutline} />
          <span>
            {strategyData.raceDurationType === 'time'
              ? formatTime(strategyData.raceDurationValue)
              : `${strategyData.raceDurationValue} Laps`}
          </span>
        </div>
      </div>
      
      {strategyData.startTime && (
        <>
          <div className="race-time-display">
            <div className="race-time-current">
              <IonIcon icon={timeOutline} />
              <span>{formatTime(currentRaceTime)}</span>
            </div>
            
            {remainingTime !== null && (
              <div className="race-time-remaining">
                <span>Remaining:</span>
                <span>{formatTime(remainingTime)}</span>
              </div>
            )}
          </div>
          
          <IonProgressBar 
            value={progress} 
            color={progressColor} 
            className="race-progress-bar"
          />
        </>
      )}
      
      <div className="race-stats-grid">
        <div className="race-stat">
          <div className="stat-label">Stint</div>
          <div className="stat-value">{currentStint}/{totalStints}</div>
        </div>
        
        <div className="race-stat">
          <div className="stat-label">Pit Stops</div>
          <div className="stat-value">{pitsDone}/{totalPits}</div>
        </div>
        
        <div className="race-stat">
          <div className="stat-label">Drivers</div>
          <div className="stat-value">{strategyData.drivers?.length || 1}</div>
        </div>
        
        {totalExtras > 0 && (
          <div className="race-stat">
            <div className="stat-label">Extra Stints</div>
            <div className="stat-value">{extrasUsed}/{totalExtras}</div>
          </div>
        )}
      </div>
    </div>
  );
};

export default RaceHeader;


