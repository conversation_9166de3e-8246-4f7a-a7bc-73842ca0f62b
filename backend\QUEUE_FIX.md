# Queue Processing Fix

## Problem Identified

The message queue was filling up to 300+ messages instead of the normal ~20 during grid creation. This was caused by a recursive loop in the queue processing logic.

## Root Cause

The issue was in the `processQueuedMessages()` method:

### ❌ **Before (Broken)**
```typescript
private async processQueuedMessages(): Promise<void> {
  while (this.messageQueue.length > 0 && !this.isProcessingGrid && !this.isProcessingMessage) {
    const queuedMessage = this.messageQueue.shift();
    if (queuedMessage) {
      // ❌ This calls parseMessage() which can trigger queueing again!
      await this.parseMessage(queuedMessage.message);
    }
  }
}
```

**Problem**: When `processQueuedMessages()` called `this.parseMessage()`, it could trigger the queueing logic again if processing flags were set, creating an infinite loop or causing messages to accumulate.

## Solution Applied

### ✅ **After (Fixed)**
```typescript
private async processQueuedMessages(): Promise<void> {
  while (this.messageQueue.length > 0 && !this.isProcessingGrid && !this.isProcessingMessage) {
    const queuedMessage = this.messageQueue.shift();
    if (queuedMessage) {
      console.log(`📤 Processing queued message (${this.messageQueue.length} remaining)`);
      
      // ✅ Process directly without going through parseMessage to avoid re-queueing
      try {
        const message = parseLogMessage(queuedMessage.message);
        
        if (!message || !message.data) {
          continue; // Skip invalid messages
        }
        
        if (message.data.grid) {
          this.isProcessingGrid = true;
          try {
            await this.handleGridMessage(message.data);
          } finally {
            this.isProcessingGrid = false;
          }
        } else {
          this.isProcessingMessage = true;
          try {
            await this.handleUpdateMessage(message.data);
          } finally {
            this.isProcessingMessage = false;
          }
        }
      } catch (error) {
        console.error('Error processing queued message:', error);
        this.isProcessingGrid = false;
        this.isProcessingMessage = false;
      }
    }
  }
}
```

## Additional Safety Measures

### ✅ **1. Queue Overflow Detection**
```typescript
// Safety check: if queue gets too large, something is wrong
if (this.messageQueue.length > 100) {
  console.error(`🚨 Queue overflow detected! ${this.messageQueue.length} messages queued.`);
  console.error(`🔍 Processing flags: grid=${this.isProcessingGrid}, message=${this.isProcessingMessage}`);
  // Reset flags to try to recover
  this.isProcessingGrid = false;
  this.isProcessingMessage = false;
}
```

### ✅ **2. Better Error Handling with Finally Blocks**
```typescript
// Main message processing
this.isProcessingGrid = true;
try {
  await this.handleGridMessage(message.data);
} finally {
  this.isProcessingGrid = false; // ✅ Always reset flag
}
```

### ✅ **3. Enhanced Queue Monitoring**
```typescript
console.log(`⏳ Message queued (${this.messageQueue.length} in queue) - Processing: grid=${this.isProcessingGrid}, message=${this.isProcessingMessage}`);
```

## Expected Behavior

### **Normal Grid Creation (Fixed)**
```
🏁 Grid found - creating database elements
⏳ Message queued (1 in queue) - Processing: grid=true, message=false
⏳ Message queued (2 in queue) - Processing: grid=true, message=false
...
⏳ Message queued (15 in queue) - Processing: grid=true, message=false
📤 Processing queued message (14 remaining)
📤 Processing queued message (13 remaining)
...
📤 Processing queued message (0 remaining)
```

### **Queue Overflow Detection (Safety)**
```
🚨 Queue overflow detected! 101 messages queued. This indicates a processing issue.
🔍 Processing flags: grid=true, message=false
```

## Key Improvements

### ✅ **Eliminated Recursive Loop**
- Queue processing no longer calls `parseMessage()`
- Direct processing prevents re-queueing
- Proper flag management with finally blocks

### ✅ **Better Error Recovery**
- Processing flags always reset in finally blocks
- Queue overflow detection and auto-recovery
- Detailed logging for debugging

### ✅ **Improved Monitoring**
- Queue size and processing state logging
- Error tracking for queued messages
- Clear indication of queue processing progress

## Testing

To verify the fix works:

1. **Run log replay** with a large file
2. **Monitor queue size** - should stay under 20-30 messages
3. **Check for overflow warnings** - should not appear
4. **Verify processing completes** - queue should empty after grid creation

### **Expected Log Output**
```
🏁 Grid found - creating database elements
⏳ Message queued (1 in queue) - Processing: grid=true, message=false
⏳ Message queued (15 in queue) - Processing: grid=true, message=false
📤 Processing queued message (14 remaining)
📤 Processing queued message (0 remaining)
✅ Grid processing completed
```

### **No More Queue Overflow**
- Queue should not exceed 100 messages
- Processing should complete normally
- No recursive queueing loops

The queue processing is now robust and should handle message processing efficiently without accumulating hundreds of messages!
