#!/usr/bin/env ts-node

/**
 * Debug script to test pitstop message parsing
 * This script helps identify why pitstops are not being created
 */

import mongoose from 'mongoose';
import { ApexParserSimple } from '../src/services/apexParserSimple';
import { ApexSession, ApexTeam, ApexKart, ApexCompetitor, ApexPitStop } from '../src/models/ApexModels';

// Test configuration
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/raceplanner';

async function connectToDatabase() {
  try {
    await mongoose.connect(MONGODB_URI);
    console.log('✅ Connected to MongoDB');
  } catch (error) {
    console.error('❌ Failed to connect to MongoDB:', error);
    process.exit(1);
  }
}

async function cleanupTestData() {
  console.log('🧹 Cleaning up test data...');
  await ApexSession.deleteMany({ title1: 'Debug Pitstop Session' });
  await ApexTeam.deleteMany({ name: 'Debug Team' });
  await ApexKart.deleteMany({ kartNumber: 888 });
  await ApexCompetitor.deleteMany({ name: 'Debug Driver' });
  await ApexPitStop.deleteMany({ reason: 'Debug' });
}

async function testPitstopMessageParsing() {
  console.log('\n🔍 Testing Pitstop Message Parsing...\n');

  const parser = new ApexParserSimple({ enableLogging: true });

  // Test different message formats to see which one works
  const testMessages = [
    // Standard format with grid
    {
      name: 'Grid with pit column',
      message: `init|r|
title1||Debug Pitstop Session
title2||Debug Test
track||Debug Track
grid||<tbody><tr data-id="r0"><td data-id="c1" data-type="no">No</td><td data-id="c2" data-type="name">Name</td><td data-id="c9" data-type="pit">Pit</td></tr><tr data-id="r99999"><td data-id="r99999c1">888</td><td data-id="r99999c2">Debug Driver</td><td data-id="r99999c9"></td></tr></tbody>`
    },
    // Pit IN message
    {
      name: 'Pit IN message',
      message: 'r99999c9|pit|IN'
    },
    // Alternative pit message formats
    {
      name: 'Alternative pit IN format 1',
      message: 'r99999c9|PIT|IN'
    },
    {
      name: 'Alternative pit IN format 2', 
      message: 'r99999c9||IN'
    },
    {
      name: 'Alternative pit IN format 3',
      message: 'r99999c9|sta|PIT'
    }
  ];

  for (const test of testMessages) {
    console.log(`\n📊 Testing: ${test.name}`);
    console.log(`📝 Message: ${test.message.substring(0, 100)}${test.message.length > 100 ? '...' : ''}`);
    
    try {
      await parser.parseMessage(test.message);
      console.log('✅ Message parsed successfully');
      
      // Wait a moment for database operations
      await new Promise(resolve => setTimeout(resolve, 1000));
      
    } catch (error) {
      console.error('❌ Error parsing message:', error);
    }
  }

  // Check what was created in the database
  console.log('\n📊 Database Check Results:');
  
  const sessions = await ApexSession.find({ title1: 'Debug Pitstop Session' }).lean();
  console.log(`Sessions created: ${sessions.length}`);
  
  const competitors = await ApexCompetitor.find({}).lean();
  console.log(`Competitors created: ${competitors.length}`);
  
  const pitstops = await ApexPitStop.find({}).lean();
  console.log(`Pitstops created: ${pitstops.length}`);
  
  if (pitstops.length > 0) {
    console.log('✅ Pitstop details:', pitstops.map(p => ({
      competitorId: p.competitorId,
      pitInTime: p.pitInTime,
      lapNumber: p.lapNumber,
      reason: p.reason
    })));
  }

  // Test manual pitstop creation to verify database works
  console.log('\n📊 Testing Manual Pitstop Creation:');

  if (competitors.length > 0 && sessions.length > 0) {
    try {
      const session = sessions[0];
      const competitor = competitors[0];

      if (session && competitor && competitor.kartId) {
        const manualPitStop = await ApexPitStop.create({
          sessionId: session._id,
          competitorId: competitor._id,
          kartId: competitor.kartId,
          pitInTime: new Date(),
          lapNumber: 1,
          pitCurrentDuration: 0,
          pitTotalDuration: 0,
          reason: 'Manual Test',
          isActive: true
        });

        console.log('✅ Manual pitstop created successfully:', manualPitStop._id);
      } else {
        console.log('❌ Missing session or competitor data for manual pitstop creation');
      }
    } catch (error) {
      console.error('❌ Failed to create manual pitstop:', error);
    }
  }

  return {
    sessionsCreated: sessions.length,
    competitorsCreated: competitors.length,
    pitstopsCreated: pitstops.length
  };
}

async function main() {
  try {
    await connectToDatabase();
    await cleanupTestData();
    
    const results = await testPitstopMessageParsing();
    
    console.log('\n📊 Debug Results Summary:');
    console.log(`   Sessions Created: ${results.sessionsCreated}`);
    console.log(`   Competitors Created: ${results.competitorsCreated}`);
    console.log(`   Pitstops Created: ${results.pitstopsCreated}`);
    
    if (results.pitstopsCreated === 0) {
      console.log('\n❌ No pitstops were created - investigating further...');
      
      // Check if competitors have proper websocket mapping
      const competitors = await ApexCompetitor.find({}).lean();
      if (competitors.length > 0) {
        console.log('🔍 Competitor details:', competitors.map(c => ({
          _id: c._id,
          name: c.name,
          websocketId: c.websocketId,
          kartId: c.kartId
        })));
      }
    } else {
      console.log('\n✅ Pitstops were created successfully!');
    }
    
  } catch (error) {
    console.error('❌ Debug test failed:', error);
  } finally {
    await cleanupTestData();
    await mongoose.disconnect();
    console.log('🔌 Disconnected from MongoDB');
  }
}

// Run the debug test
if (require.main === module) {
  main().catch(console.error);
}
