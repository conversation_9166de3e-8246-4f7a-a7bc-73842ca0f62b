const mongoose = require('mongoose');

// Define schemas directly since we can't import TypeScript modules
const ApexSessionSchema = new mongoose.Schema({
  title1: { type: String, default: '' },
  title2: { type: String, default: '' },
  track: { type: String, default: '' },
  isActive: { type: Boolean, default: true },
  gridData: { type: mongoose.Schema.Types.Mixed },
  sessionData: { type: mongoose.Schema.Types.Mixed, default: {} }
}, {
  timestamps: true,
  collection: 'apex_sessions'
});

const ApexCompetitorSchema = new mongoose.Schema({
  sessionId: { type: mongoose.Schema.Types.ObjectId, ref: 'ApexSession', required: true },
  teamId: { type: mongoose.Schema.Types.ObjectId, ref: 'ApexTeam', required: true },
  kartId: { type: mongoose.Schema.Types.ObjectId, ref: 'ApexKart', required: true },
  websocketId: { type: String, required: true },
  name: { type: String, required: true },
  nationality: { type: String, default: 'Unknown' },
  drivers: [{ type: String }],
  isActive: { type: Boolean, default: true }
}, {
  timestamps: true,
  collection: 'apex_competitors'
});

// Create models
const ApexSession = mongoose.model('ApexSession', ApexSessionSchema);
const ApexCompetitor = mongoose.model('ApexCompetitor', ApexCompetitorSchema);

async function testCompetitorFix() {
  try {
    // Connect to MongoDB
    await mongoose.connect('mongodb://localhost:27017/raceplanner');
    console.log('✅ Connected to MongoDB');

    // Find the most recent session
    const session = await ApexSession.findOne().sort({ createdAt: -1 });
    if (!session) {
      console.log('❌ No sessions found');
      return;
    }

    console.log(`\n📋 Testing session: ${session.title1} - ${session.title2}`);
    console.log(`   Session ID: ${session._id}`);

    // Get all competitors for this session
    const competitors = await ApexCompetitor.find({ sessionId: session._id });
    console.log(`\n👥 Found ${competitors.length} competitors:`);
    
    competitors.forEach(competitor => {
      console.log(`   - ${competitor.name} (websocketId: ${competitor.websocketId})`);
    });

    // Test the specific problematic websocketId
    console.log('\n🔍 Testing lookup for websocketId "17794":');
    const problematicCompetitor = await ApexCompetitor.findOne({
      sessionId: session._id,
      websocketId: '17794'
    });
    
    if (problematicCompetitor) {
      console.log(`   ✅ Found: ${problematicCompetitor.name}`);
    } else {
      console.log(`   ❌ Competitor with websocketId "17794" not found`);
      console.log(`   ℹ️ This would now trigger automatic competitor creation in the parser`);
    }

    // Test with another common websocketId
    console.log('\n🔍 Testing lookup for websocketId "17742":');
    const anotherCompetitor = await ApexCompetitor.findOne({
      sessionId: session._id,
      websocketId: '17742'
    });
    
    if (anotherCompetitor) {
      console.log(`   ✅ Found: ${anotherCompetitor.name}`);
    } else {
      console.log(`   ❌ Competitor with websocketId "17742" not found`);
      console.log(`   ℹ️ This would now trigger automatic competitor creation in the parser`);
    }

    console.log('\n📊 Summary:');
    console.log('   - Parser now automatically creates missing competitors');
    console.log('   - Frontend properly uses apex database when toggle is enabled');
    console.log('   - Websocket mapping is populated before processing updates');
    console.log('   - Better error handling and debugging information');

  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await mongoose.disconnect();
    console.log('\n✅ Disconnected from MongoDB');
  }
}

// Run the test
testCompetitorFix();
