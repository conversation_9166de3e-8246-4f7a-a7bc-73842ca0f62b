import { Response } from "express";
import mongoose, { ClientSession } from "mongoose";

/**
 * Checks if a string is a valid MongoDB ObjectId.
 */
export const isValidObjectId = (id: string): boolean => {
  return typeof id === 'string' && id.length > 0 && mongoose.Types.ObjectId.isValid(id);
};

/**
 * Sends a standardized JSON error response and logs the error server-side.
 */
export const sendErrorResponse = (res: Response, message: string, statusCode: number = 500, error?: unknown): void => { // <--- Add : void here
    console.error(`Error: ${message}`, error instanceof Error ? error.stack : error); // Log full error details on backend
    // Determine a user-friendly message, avoiding leaking sensitive details in production errors
    const clientMessage = (statusCode === 500 && process.env.NODE_ENV === 'production')
        ? 'An unexpected internal server error occurred.'
        : (error instanceof Error ? error.message : message);
    // Optionally include error details in non-production environments
    const errorDetails = process.env.NODE_ENV !== 'production'
        ? (error instanceof Error ? String(error) : JSON.stringify(error))
        : undefined; // Don't send detailed error string in production

    res.status(statusCode).json({
        message: clientMessage,
        // Only include the 'error' field if details are available (non-production)
        ...(errorDetails && { error: errorDetails })
    });
};

/**
 * Safely ends a Mongoose session if it's active.
 */
export const safelyEndSession = async (session: ClientSession | null) => {
    if (session) {
        try {
            // Check if session is active before ending
            if (session.inTransaction() || session.hasEnded === false) {
                 await session.endSession();
                 // console.log("[Session] Session ended."); // Optional: log session end
            }
        } catch (endSessionError) {
            console.error(">>> CRITICAL: Error ending session:", endSessionError);
        }
    }
};

/**
 * Safely aborts a Mongoose transaction (if active) and ends the session.
 */
export const abortTransactionAndEndSession = async (session: ClientSession | null, operation: string, error?: unknown) => {
    console.error(`[${operation}] Error occurred:`, error);
    if (session && session.inTransaction()) {
        try {
            console.log(`[${operation}] Aborting transaction due to error...`);
            await session.abortTransaction();
            console.log(`[${operation}] Transaction aborted.`);
        } catch (abortError) {
            console.error(`[${operation}] >>> CRITICAL: Error occurred while aborting transaction:`, abortError);
        }
    } else if (session) {
         console.log(`[${operation}] Error occurred outside active transaction.`);
    }
    // Always attempt to end the session after handling the transaction state
    await safelyEndSession(session);
};