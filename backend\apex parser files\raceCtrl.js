appApex.controller('raceCtrl',
    ['$scope',
        '$log',
        '$location',
        '$ionicScrollDelegate',
        '$interval',
        '$ionicModal',
        'apexParserService',
        'raceService',
        '$state',
        'slideListService',
        '$rootScope',
        'configurationService',
        'localStorageService',
        function ($scope, $log, $location, $ionicScrollDelegate, $interval, $ionicModal, apexParserService, raceService, $state, slideListService, $rootScope, configurationService, localStorageService) {

            $scope.isRaceEnded = false; //Indica se la gara è in corso             
            $scope.title = raceService.raceTitle;
            $scope.raceClockTime = ''; //Tempo residuo di gara
            $scope.selectedDriver = -1; //driver attualmente selezionato            
            $scope.driverSelectedModal = null;
            $scope.grid = []; //Griglia di gara
            $scope.gridLocked = []; //Sotto griglia di gara personalizzata
            $scope.track = '';
            $scope.msg = '';
            $scope.light = '';
            $scope.raceTimerCallback = '';
            $scope.raceText = '';
            $scope.isS1S2Enable = false; // nasconde s1 e s2 dei piloti non selezionati
            $scope.isTeamManagerCompactView = false;
            $scope.isDriverAutoSelectEnable = false;
            $scope.showSpeed = false;
            $scope.speed = 0;
            $scope.maxSpeed = 0;
            $scope.isDemo = false;
            //Debug
            $scope.lastSocketMsg = '';


            $scope.onDragLeft = function (driver) {
                if (!driver)
                    return;

                driver.isLocked = !driver.isLocked;
            }

            $scope.hasOneLocked = function () {
                if (!$scope.grid || $scope.grid.length == 0)
                    return false;

                var result = false;
                angular.forEach($scope.grid, function (item) {
                    if (item.isLocked == true)
                        result = true;
                });
                return result;
            }

            $scope.differenceBetweenLaps = function (lap1, lap2) {
                if (!lap1 && !lap2) {
                    return 0;
                }

                var lap1 = parseFloat(lap1);
                var lap2 = parseFloat(lap2);

                var result = '';

                // if(lap1 < lap2)
                //     result = '+';
                // else
                //     result = '-'


                result = result + (lap2 - lap1).toFixed(3);

                //console.log('sto calcolando la differenza tra giri')

                return result;
            }

            //1 s1, 2 s2
            $scope.differenceBetweenSectors = function (driver, sectorType) {
                if (!driver)
                    return 0;
                if (sectorType != 1 && sectorType != 2)
                    return 0;

                var result = 0;
                var sector = 0;
                var sectorOld = 0;

                if (sectorType == 1) {
                    sector = angular.copy(driver.s1);
                    sectorOld = angular.copy(driver.s1Old);
                }
                else if (sectorType == 2) {
                    sector = driver.s2;
                    sectorOld = driver.s2Old;
                }

                if (!sector || sector == '')
                    return '';

                if (!sectorOld || sectorOld == '')
                    return '';

                result = sector - sectorOld;

                if (result > 0)
                    return '+' + result.toFixed(3);

                if (result == 0)
                    result = '';

                //console.log('sto calcolando la differenza tra settori')
                return result.toFixed(3);
            }

            //Timeout callbacks
            $scope.raceTimeoutCallback = null;
            $scope.startTimerCallBack = null;

            $ionicModal.fromTemplateUrl('app/apex/templates/driverDetail.html', {
                scope: $scope,
                animation: 'slide-in-up'
            }).then(function (modal) {
                $scope.driverSelectedModal = modal;
            });
            $scope.openModal = function () {
                $scope.driverSelectedModal.show();
            };
            $scope.closeModal = function () {
                $scope.driverSelectedModal.hide();
                $scope.raceDriverSelected = null;
            };
            // Cleanup the modal when we're done with it!
            $scope.$on('$destroy', function () {
                $scope.driverSelectedModal.remove();
                $scope.raceDriverSelected = null;
            });

            $scope.selectDriver = function (item) {
                if ($scope.selectedDriver && $scope.selectedDriver.name == item.name) {
                    $scope.selectedDriver = -1
                    return;
                }
                else {
                    $scope.selectedDriver = item;
                    slideToDriver(item.name);
                }
            }

            $scope.openDriverDetail = function (driver) {
                if (!driver)
                    $scope.raceDriverSelected = ''
                else
                    $scope.raceDriverSelected = driver;

                $scope.openModal();
            }

            var slideToDriver = function (locationParam) {

                if (!locationParam)
                    return;
                $location.hash(locationParam);
                var ionicScroll = $ionicScrollDelegate.$getByHandle('driverScroll');
                ionicScroll.anchorScroll(true);

                var gridId = 'grid';
                var itemsLength = $scope.grid.length;
                var scrollTop = ionicScroll.getScrollPosition().top;

                var gridHeight = slideListService.getGridHeight(gridId);//1
                var screenHeight = slideListService.getScreenHeight();//2
                var itemHeight = slideListService.getItemHeight(gridHeight, itemsLength);//3
                var numberOfItemsInScreen = slideListService.getNumbersOfItemInScreen(screenHeight, itemHeight);//4
                var isScrollPositive = slideListService.isScrollDirectionPositive(scrollTop, gridHeight);
                var scrollBy = slideListService.calculateScrollBy(numberOfItemsInScreen, itemHeight);

                if (!isScrollPositive)
                    scrollBy = -scrollBy;

                ionicScroll.scrollBy(0, scrollBy, true)
            };

            var openSocket = function () {

                var raceSocket = {};
                raceSocket.connect = function () {
                    if (!$rootScope.apiEndpoint) {
                        //setTimeout(openSocket(), 5000)
                        return;
                    }
                    var ws = new WebSocket($rootScope.apiEndpoint);
                    ws.onopen = function () {
                        //$log.info('Socket di gara aperto');
                        raceSocket.ws = ws;
                    };
                    ws.onerror = function (e) {
                        //$log.error('Socket errore: ' + e);
                    };
                    ws.onclose = function () {
                        //$log.info('Socket di gara chiuso');
                        $interval.cancel($scope.raceTimerCallback);
                        openSocket();
                    };
                    ws.onmessage = function (msgevent) {
                        //$log.info('Socket ricevuto: ' + msgevent.data);
                        var msg = msgevent.data;
                        $scope.lastSocketMsg = msg;
                        parseSocketMessage(msg);

                    };
                };
                raceSocket.connect();
            }

            var startTimer = function (duration, display) {
                var timer = duration, minutes, seconds;
                $interval.cancel($scope.raceTimerCallback);
                var result = $interval(function () {
                    minutes = parseInt(timer / 60, 10);
                    seconds = parseInt(timer % 60, 10);
                    hours = parseInt(timer / (1 * 60 * 60));

                    minutes = minutes < 10 ? '0' + minutes : minutes;
                    seconds = seconds < 10 ? '0' + seconds : seconds;
                    hours = hours < 10 ? '0' + hours : hours;

                    // $scope.raceClockTime = hours + ':' + minutes + ':' + seconds;

                    // function msToTime(ms) {
                    //     var milliseconds = parseInt((ms % 1000) / 100),
                    //       seconds = Math.floor((ms / 1000) % 60),
                    //       minutes = Math.floor((ms / (1000 * 60)) % 60),
                    //       hours = Math.floor((ms / (1000 * 60 * 60)) % 24);

                    //     hours = (hours < 10) ? '0' + hours : hours;
                    //     minutes = (minutes < 10) ? '0' + minutes : minutes;
                    //     seconds = (seconds < 10) ? '0' + seconds : seconds;

                    //     return hours + ':' + minutes + ':' + seconds;
                    //   }

                    function parseMillisecondsIntoReadableTime(milliseconds) {
                        //Get hours from milliseconds
                        var hours = milliseconds / (1000 * 60 * 60);
                        var absoluteHours = Math.floor(hours);
                        var h = absoluteHours > 9 ? absoluteHours : '0' + absoluteHours;

                        //Get remainder from hours and convert to minutes
                        var minutes = (hours - absoluteHours) * 60;
                        var absoluteMinutes = Math.floor(minutes);
                        var m = absoluteMinutes > 9 ? absoluteMinutes : '0' + absoluteMinutes;

                        //Get remainder from minutes and convert to seconds
                        var seconds = (minutes - absoluteMinutes) * 60;
                        var absoluteSeconds = Math.floor(seconds);
                        var s = absoluteSeconds > 9 ? absoluteSeconds : '0' + absoluteSeconds;


                        return h + ':' + m + ':' + s;
                    }


                    $scope.raceClockTime = raceService.parseMillisecondsIntoReadableTime(timer)


                    if (--timer < 0) {
                        timer = duration;
                    }

                    if ($scope.raceClockTime == '00:00:00') {
                        $interval.cancel($scope.raceTimerCallback);
                    }

                    timer = timer - 1000
                }, 1000);
                return result;
            }

            var parseSocketMessage = function (dataParam) {
                if (!dataParam)
                    return;

                var dataArray = dataParam.split("\n");

                angular.forEach(dataArray, function (data) {

                    var dataSplitted = data.split('|');

                    if (!dataSplitted)
                        return;

                    for (var i = 0; i < dataSplitted.length;) {
                        var item = dataSplitted[i];
                        if (item.includes('\n')) {
                            var split = item.split('\n');
                            dataSplitted[i] = split[0];
                            dataSplitted.splice(i + 1, 0, split[1]);
                        }
                        else {
                            i = i + 1;
                        }
                    }

                    var item = dataSplitted[0].toLowerCase().trim();
                    var itemData = dataSplitted[2];

                    switch (item) {
                        case 'init':

                            data = dataParam;
                            var light = data.split('light|');
                            $scope.isLightGreen = raceService.isLightGreen(light[1] ? light[1].substring(0, 2) : '');
                            raceService.lightGreen = $scope.isLightGreen;
                            $scope.isLightRed = raceService.isLightRed(light[1] ? light[1].substring(0, 2) : '')
                            $scope.isRaceEnded = raceService.isRaceEnded(light[1] ? light[1].substring(0, 2) : '')

                            var grid = data.split('grid||');
                            $scope.grid = apexParserService.getGrid(grid[1], $scope.isLightRed);

                            angular.forEach($scope.grid, function (driver) {
                                if (driver && driver.position && driver.bestTimeLabel && driver.bestTimeLabel.length < 4) {
                                    driver.position = '';
                                }
                            });

                            //Verifico se l'utente ha richiesto l'auto select in griglia
                            checkDriverAutoSelect();


                            var dyn1 = data.split('dyn1|');
                            if (dyn1[1] ? dyn1[1].substring(0, 4) == 'text' : false) {
                                var text = dyn1[1].split('|')[1] ? dyn1[1].split('|')[1].split('\nlight')[0] : '';
                                if (text != 'light|lf')
                                    $scope.raceClockTime = text;
                            }
                            else if (dyn1[1] ? dyn1[1].substring(0, 9) == 'countdown' : false) {
                                if (dyn1[1]) {
                                    var time = 0;
                                    try {
                                        //dyn1[1]?.split('countdown|')[1]?.split('light')[0]
                                        var countdown = dyn[1] ? dyn[1].split('countdown|')[1] : null;
                                        var countdownLight = countdown ? countdown.split('light')[0] : 0
                                        time = parseFloat(countdownLight)
                                    } catch (ex) {
                                        time = 0
                                    }
                                    $scope.raceTimerCallback = startTimer(time);
                                }

                            }
                            var title2 = data.split('title2||');
                            title2 = title2[1] ? title2[1].split('dyn1')[0] : 'kRace';

                            var title1 = data.split('title1||');
                            title1 = title1[1] ? title1[1].split('title2')[0] : 'kRace';

                            $scope.title = title1 + ' ' + title2;
                            if (!$scope.title)
                                $scope.title = 'kRace'
                            raceService.raceTitle = $scope.title;

                            var msg = data.split('msg||');
                            $scope.msg = msg[1];

                            var comments = data.split('comments||');
                            if (comments[1])
                                $scope.comments = comments[1] ? comments[1].split('title1')[0] : '';

                            var track = data.split('track||');
                            $scope.track = track[1] ? track[1].split('com')[0] : '';

                            break;

                        case 'dyn1':
                            if (dataSplitted[1] == 'text' && itemData)
                                $scope.raceClockTime = itemData;
                            else if (dataSplitted[1] == 'countdown') {
                                if (itemData == '0') {
                                    $scope.isRaceEnded = true;
                                    $scope.isLightGreen = false;
                                    $scope.isLightRed = false;
                                }
                                if (itemData)
                                    $scope.raceTimerCallback = startTimer(itemData);
                            }

                            break;
                        case 'light':
                            $scope.isLightGreen = raceService.isLightGreen(dataSplitted[1])
                            $scope.isLightRed = raceService.isLightRed(dataSplitted[1])
                            $scope.isRaceEnded = raceService.isRaceEnded(dataSplitted[1])
                            break;
                        case 'com':
                            var msgConsole = data.split('com||');
                            $scope.console = $scope.console ? $scope.console : '' + msgConsole[1]
                            break;
                        case 'dyn2':
                            var msgConsole = data.split('com||');
                            $scope.console = $scope.console ? $scope.console : '' + msgConsole[1]
                            break;
                        default:
                            if (!item.startsWith('r'))
                                return;
                            for (var i = 0; i < dataSplitted.length - 2; i += 3) {
                                var rowNameInfo = dataSplitted[i];
                                var typeInfo = dataSplitted[i + 1];
                                var valueInfo = dataSplitted[i + 2];
                                var dataId = raceService.buildDataId(rowNameInfo);

                                //Recupero il pilota dalla griglia di gara
                                var driver = raceService.getDriverModelFromGrid(dataId, $scope.grid);
                                if (!driver) {
                                    console.log("Non ho trovato il pilota con dataId: " + dataId)
                                    continue;
                                }

                                //Popolo il modello dei pilota con i dati in ingresso
                                raceService.populateDriverModel(driver, rowNameInfo, typeInfo, valueInfo, dataId, $scope.grid);

                                console.log("Ho aggiornato il pilota: " + driver.name + " - " + dataId + ", RowNameInfo/TypeInfo/valueInfo: " + rowNameInfo + "-" + typeInfo + "-" + valueInfo);

                                if ($scope.selectedDriver
                                    && $scope.selectedDriver != -1
                                    && driver
                                    && driver.name == $scope.selectedDriver.name
                                    && driver.isPositionChange) {
                                    slideToDriver($scope.selectedDriver.name);
                                }
                            }
                            break;
                    }
                });

                raceService.clearBestTimes($scope.grid);

                raceService.calculateGaps($scope.grid);

                if (!$scope.$$phase) {
                    $scope.$apply();
                }

            }

            var checkDriverAutoSelect = function () {

                if (!$scope.isDriverAutoSelectEnable)
                    return;

                //recupero la lista di piloti da autoselezionare
                var driverAutoSelectList = localStorageService.getValue(configurationService.driverAutoSelectTextKey);

                if (!driverAutoSelectList) {
                    return;
                }

                var driverAutoSelectList = driverAutoSelectList.split(';');

                var isAutoSelectDone = false;

                //Li cerco
                angular.forEach(driverAutoSelectList, function (item) {
                    if (isAutoSelectDone)
                        return;
                    angular.forEach($scope.grid, function (driver) {
                        if (item.toLowerCase().trim() == driver.name.toLowerCase().trim()) {
                            if ($scope.selectedDriver.name != driver.name) {
                                //Assegno
                                $scope.selectDriver(driver);
                            }
                            isAutoSelectDone = true;
                            return;

                        }
                    });
                });

            }

            $scope.onIsTeamManagerCompactViewChange = function () {
                $scope.isTeamManagerCompactView = !$scope.isTeamManagerCompactView;
                console.log($scope.isTeamManagerCompactView)
                $scope.$apply();
            }

            $scope.checkSpeed = function () {
                if (!$scope.showSpeed)
                    return;

                // onSuccess Callback
                // This method accepts a Position object, which contains the
                // current GPS coordinates
                //
                var onSuccess = function (position) {
                    var speed = position.coords.speed * 3.6; //conversione da m/s a km/h
                    speed = parseInt(speed);
                    // if(speed <= 0)
                    // return;

                    // alert('Latitude: '          + position.coords.latitude          + '\n' +
                    //     'Longitude: '         + position.coords.longitude         + '\n' +
                    //     'Altitude: '          + position.coords.altitude          + '\n' +
                    //     'Accuracy: '          + position.coords.accuracy          + '\n' +
                    //     'Altitude Accuracy: ' + position.coords.altitudeAccuracy  + '\n' +
                    //     'Heading: '           + position.coords.heading           + '\n' +
                    //     'Speed: '             + position.coords.speed             + '\n' +
                    //     'Timestamp: '         + position.timestamp                + '\n');
                    $scope.speed = speed ? speed : 0;
                    if ($scope.speed > $scope.maxSpeed)
                        $scope.maxSpeed = $scope.speed;

                    $scope.title = $scope.speed + " km/h";
                    console.log("speed:" + $scope.speed)
                    console.log("speed:" + $scope.maxSpeed)
                    $scope.$apply();
                };

                // onError Callback receives a PositionError object
                //
                function onError(error) {
                    console.log('code: ' + error.code + '\n' +
                        'message: ' + error.message + '\n');
                }

                var watchOptions = {
                    'enableHighAccuracy': true
                }

                navigator.geolocation.watchPosition(onSuccess, onError, watchOptions);
            }

            var constructor = function () {

                if (!$scope.isDemo) {
                    openSocket();
                }
                else
                    testSocket();
            }

            // var testLapLine = function (driverName, seconds) {
            //     function started(duration, driverName) {

            //         var TotalSeconds = 40;
            //         var documentWidth = $(document).width();
            //         var start = Date.now();
            //         var intervalSetted = null;

            //         function timer() {

            //             var diff = duration - (((Date.now() - start) / 1000) | 0);

            //             var seconds = (diff % 60) | 0;

            //             seconds = seconds < 10 ? '0' + seconds : seconds;

            //             //$('#timer').html('00:' + seconds);

            //             var progresBarWidth = (seconds * documentWidth / TotalSeconds);



            //             $('#laptime_'+ driverName).animate({
            //                 width: progresBarWidth + 'px'
            //             }, 1000);
            //             $('.laptime').animate({
            //                 width: progresBarWidth + 'px'
            //             }, 1000);

            //             if (diff <= 0) {
            //                 clearInterval(intervalSetted);
            //             }

            //         }

            //         timer();
            //         intervalSetted = setInterval(timer, 1000);
            //     }

            //     started(40, driverName);
            // }

            var testSocket = function () {
                var testSocket = '';
                //testSocket = 'r2c6|ti|37.614 r2c7|ib|37.614 r2c8|in|0.044 r2c9|in|9 r2|*|37.61';
                //testSocket = 'dyn1|countdown|558000'
                testSocket = 'init|p|' +
                    'best|hide|' +
                    'css|no1|border-bottom-color:#005CB9 !important; color:#FFFFFF !important;' +
                    'css|no2|border-bottom-color:#FF8000 !important; color:#000000 !important;' +
                    'css|no3|border-bottom-color:#00FF00 !important; color:#000000 !important;' +
                    'css|pena1|color:#f00; font-size:11px; font-weight: bold; text-align:center;' +
                    'effects||Effetti' +
                    'comments||Commenti' +
                    'title1||MINI GP' +
                    'title2||Qualifiche' +
                    //'dyn1|countdown|290.40' +
                    'dyn1|countdown|86400000' +
                    'light|lr|' +
                    'wth1|wm|' +
                    'wth2||' +
                    'wth3||' +
                    'track||Pista Karting (440m)' +
                    'com||' +
                    "grid||<tbody><tr data-id='r0' class='head' data-pos='0'><td data-id='c1' data-type='sta' data-pr='1'></td><td data-id='c2' data-type='rk' data-pr='1'>Cla</td><td data-id='c3' data-type='no' data-pr='1'>Kart</td><td data-id='c4' data-type='dr' data-pr='1' data-width='20' data-min='16'>Team</td><td data-id='c5' data-type='s1' data-pr='3' data-width='9' data-min='6'>S1</td><td data-id='c6' data-type='s2' data-pr='3' data-width='9' data-min='6'>S2</td><td data-id='c7' data-type='llp' data-pr='2' data-width='11' data-min='7'>Ultimo T.</td><td data-id='c8' data-type='gap' data-pr='1' data-width='11' data-min='8'>Distacco</td><td data-id='c9' data-type='int' data-pr='5' data-width='8' data-min='7'>Interv.</td><td data-id='c10' data-type='blp' data-pr='6' data-width='9' data-min='7'>M.giro</td><td data-id='c11' data-type='otr' data-pr='2' data-width='8' data-min='8'>In pista</td><td data-id='c12' data-type='pit' data-pr='2' data-width='5' data-min='7'>Pit stop</td><td data-id='c13' data-type='' data-pr='6' data-width='6' data-min='5'>Pena</td></tr><tr data-id='r22208' data-pos='3'><td data-id='r22208c1' class='in'></td><td class='rk'><div><p data-id='r22208c2' class=''>3</p></div></td><td class='no'><div data-id='r22208c3' class='notc255'>17</div></td><td data-id='r22208c4' class='dr'>EFFEGRAFICA</td><td data-id='r22208c5' class='tn'>29.562</td><td data-id='r22208c6' class=''></td><td data-id='r22208c7' class='tn'>59.137</td><td data-id='r22208c8' class='ib'>9.438</td><td data-id='r22208c9' class='in'>1.200</td><td data-id='r22208c10' class='in'>58.314</td><td data-id='r22208c11' class='in'>0:03</td><td data-id='r22208c12' class='in'>4</td><td data-id='r22208c13' class=''></td></tr><tr data-id='r22207' data-pos='5'><td data-id='r22207c1' class='in'></td><td class='rk'><div><p data-id='r22207c2' class=''>5</p></div></td><td class='no'><div data-id='r22207c3' class='notc255'>33</div></td><td data-id='r22207c4' class='dr'>DAMMUSO</td><td data-id='r22207c5' class='tn'>29.463</td><td data-id='r22207c6' class='tn'>29.882</td><td data-id='r22207c7' class='tn'>59.345</td><td data-id='r22207c8' class='ib'>40.314</td><td data-id='r22207c9' class='in'>4.978</td><td data-id='r22207c10' class='in'>58.151</td><td data-id='r22207c11' class='in'>0:13</td><td data-id='r22207c12' class='in'>4</td><td data-id='r22207c13' class=''></td></tr><tr data-id='r22205' data-pos='2'><td data-id='r22205c1' class='sr'></td><td class='rk'><div><p data-id='r22205c2' class=''>2</p></div></td><td class='no'><div data-id='r22205c3' class='notc255'>13</div></td><td data-id='r22205c4' class='dr'>BERTO BROTHERS</td><td data-id='r22205c5' class='tn'>29.489</td><td data-id='r22205c6' class=''></td><td data-id='r22205c7' class='tn'>59.470</td><td data-id='r22205c8' class='ib'>8.238</td><td data-id='r22205c9' class='in'>8.238</td><td data-id='r22205c10' class='in'>58.282</td><td data-id='r22205c11' class='in'>0:03</td><td data-id='r22205c12' class='in'>4</td><td data-id='r22205c13' class=''></td></tr><tr data-id='r22209' data-pos='1'><td data-id='r22209c1' class='sr'></td><td class='rk'><div><p data-id='r22209c2' class=''>1</p></div></td><td class='no'><div data-id='r22209c3' class='notc255'>21</div></td><td data-id='r22209c4' class='dr'>CKC OFFICIAL RT</td><td data-id='r22209c5' class='tn'>29.390</td><td data-id='r22209c6' class='tn'>29.768</td><td data-id='r22209c7' class='tn'>59.158</td><td data-id='r22209c8' class='in'>Giro 153</td><td data-id='r22209c9' class='in'></td><td data-id='r22209c10' class='in'>58.204</td><td data-id='r22209c11' class='in'>0:04</td><td data-id='r22209c12' class='in'>4</td><td data-id='r22209c13' class=''></td></tr><tr data-id='r22206' data-pos='9'><td data-id='r22206c1' class='in'></td><td class='rk'><div><p data-id='r22206c2' class=''>9</p></div></td><td class='no'><div data-id='r22206c3' class='notc255'>26</div></td><td data-id='r22206c4' class='dr'>CKC OFFICIAL N1</td><td data-id='r22206c5' class='tn'>29.322</td><td data-id='r22206c6' class=''></td><td data-id='r22206c7' class='tn'>59.096</td><td data-id='r22206c8' class='ib'>11.623</td><td data-id='r22206c9' class='in'>9.867</td><td data-id='r22206c10' class='in'>57.996</td><td data-id='r22206c11' class='in'>0:21</td><td data-id='r22206c12' class='in'>5</td><td data-id='r22206c13' class=''></td></tr><tr data-id='r22219' data-pos='4'><td data-id='r22219c1' class='in'></td><td class='rk'><div><p data-id='r22219c2' class=''>4</p></div></td><td class='no'><div data-id='r22219c3' class='notc255'>4</div></td><td data-id='r22219c4' class='dr'>RABBIT</td><td data-id='r22219c5' class='tn'>29.480</td><td data-id='r22219c6' class=''></td><td data-id='r22219c7' class='tn'>58.847</td><td data-id='r22219c8' class='ib'>35.336</td><td data-id='r22219c9' class='in'>25.898</td><td data-id='r22219c10' class='in'>58.427</td><td data-id='r22219c11' class='in'>0:16</td><td data-id='r22219c12' class='in'>4</td><td data-id='r22219c13' class=''></td></tr><tr data-id='r22204' data-pos='12'><td data-id='r22204c1' class='in'></td><td class='rk'><div><p data-id='r22204c2' class=''>12</p></div></td><td class='no'><div data-id='r22204c3' class='notc255'>38</div></td><td data-id='r22204c4' class='dr'>AMG RT</td><td data-id='r22204c5' class='tn'>29.085</td><td data-id='r22204c6' class=''></td><td data-id='r22204c7' class='tn'>58.890</td><td data-id='r22204c8' class='ib'>34.994</td><td data-id='r22204c9' class='in'>11.548</td><td data-id='r22204c10' class='in'>58.116</td><td data-id='r22204c11' class='in'>0:16</td><td data-id='r22204c12' class='in'>5</td><td data-id='r22204c13' class=''></td></tr><tr data-id='r22215' data-pos='6'><td data-id='r22215c1' class='in'></td><td class='rk'><div><p data-id='r22215c2' class=''>6</p></div></td><td class='no'><div data-id='r22215c3' class='notc16711680'>35</div></td><td data-id='r22215c4' class='dr'>MPK</td><td data-id='r22215c5' class='tn'>29.292</td><td data-id='r22215c6' class='tn'>30.001</td><td data-id='r22215c7' class='tn'>59.293</td><td data-id='r22215c8' class='ib'>1 Giro</td><td data-id='r22215c9' class='in'>1 Giro</td><td data-id='r22215c10' class='in'>58.474</td><td data-id='r22215c11' class='in'>0:18</td><td data-id='r22215c12' class='in'>4</td><td data-id='r22215c13' class=''></td></tr><tr data-id='r22216' data-pos='13'><td data-id='r22216c1' class='in'></td><td class='rk'><div><p data-id='r22216c2' class=''>13</p></div></td><td class='no'><div data-id='r22216c3' class='notc65535'>29</div></td><td data-id='r22216c4' class='dr'>PPG APULIA</td><td data-id='r22216c5' class='tn'>29.821</td><td data-id='r22216c6' class='tn'>30.150</td><td data-id='r22216c7' class='tn'>59.971</td><td data-id='r22216c8' class='ib'>50.265</td><td data-id='r22216c9' class='in'>15.271</td><td data-id='r22216c10' class='in'>58.715</td><td data-id='r22216c11' class='in'>0:17</td><td data-id='r22216c12' class='in'>4</td><td data-id='r22216c13' class=''></td></tr><tr data-id='r22214' data-pos='8'><td data-id='r22214c1' class='sr'></td><td class='rk'><div><p data-id='r22214c2' class=''>8</p></div></td><td class='no'><div data-id='r22214c3' class='notc65535'>8</div></td><td data-id='r22214c4' class='dr'>MODDERS</td><td data-id='r22214c5' class='tn'>29.472</td><td data-id='r22214c6' class='tn'>29.874</td><td data-id='r22214c7' class='tn'>59.346</td><td data-id='r22214c8' class='ib'>1.780</td><td data-id='r22214c9' class='in'>1.780</td><td data-id='r22214c10' class='in'>58.947</td><td data-id='r22214c11' class='in'>0:22</td><td data-id='r22214c12' class='in'>4</td><td data-id='r22214c13' class=''></td></tr><tr data-id='r22213' data-pos='10'><td data-id='r22213c1' class='in'></td><td class='rk'><div><p data-id='r22213c2' class=''>10</p></div></td><td class='no'><div data-id='r22213c3' class='notc65535'>31</div></td><td data-id='r22213c4' class='dr'>KARTONAUTI</td><td data-id='r22213c5' class='tn'>29.846</td><td data-id='r22213c6' class=''></td><td data-id='r22213c7' class='tn'>59.680</td><td data-id='r22213c8' class='ib'>16.303</td><td data-id='r22213c9' class='in'>4.680</td><td data-id='r22213c10' class='in'>58.759</td><td data-id='r22213c11' class='in'>0:26</td><td data-id='r22213c12' class='in'>4</td><td data-id='r22213c13' class=''></td></tr><tr data-id='r22211' data-pos='11'><td data-id='r22211c1' class='in'></td><td class='rk'><div><p data-id='r22211c2' class=''>11</p></div></td><td class='no'><div data-id='r22211c3' class='notc65535'>11</div></td><td data-id='r22211c4' class='dr'>GRANDPA</td><td data-id='r22211c5' class='tn'>29.644</td><td data-id='r22211c6' class=''></td><td data-id='r22211c7' class='tn'>1:00.432</td><td data-id='r22211c8' class='ib'>23.446</td><td data-id='r22211c9' class='in'>7.143</td><td data-id='r22211c10' class='in'>58.703</td><td data-id='r22211c11' class='in'>0:17</td><td data-id='r22211c12' class='in'>4</td><td data-id='r22211c13' class=''></td></tr><tr data-id='r22218' data-pos='15'><td data-id='r22218c1' class='in'></td><td class='rk'><div><p data-id='r22218c2' class=''>15</p></div></td><td class='no'><div data-id='r22218c3' class='notc65280'>20</div></td><td data-id='r22218c4' class='dr'>PPG SPIRIT</td><td data-id='r22218c5' class='tn'>29.830</td><td data-id='r22218c6' class=''></td><td data-id='r22218c7' class='tn'>59.646</td><td data-id='r22218c8' class='ib'>6 Giri</td><td data-id='r22218c9' class='in'>3 Giri</td><td data-id='r22218c10' class='in'>59.058</td><td data-id='r22218c11' class='in'>0:09</td><td data-id='r22218c12' class='in'>5</td><td data-id='r22218c13' class=''></td></tr><tr data-id='r22212' data-pos='7'><td data-id='r22212c1' class='sr'></td><td class='rk'><div><p data-id='r22212c2' class=''>7</p></div></td><td class='no'><div data-id='r22212c3' class='notc16711680'>1</div></td><td data-id='r22212c4' class='dr'>NINJA</td><td data-id='r22212c5' class='tn'>29.339</td><td data-id='r22212c6' class='tn'>29.983</td><td data-id='r22212c7' class='tn'>59.322</td><td data-id='r22212c8' class='ib'>2 Giri</td><td data-id='r22212c9' class='in'>14.753</td><td data-id='r22212c10' class='in'>58.661</td><td data-id='r22212c11' class='in'>0:15</td><td data-id='r22212c12' class='in'>4</td><td data-id='r22212c13' class=''></td></tr><tr data-id='r22210' data-pos='14'><td data-id='r22210c1' class='si'></td><td class='rk'><div><p data-id='r22210c2' class=''>14</p></div></td><td class='no'><div data-id='r22210c3' class='notc65280'>23</div></td><td data-id='r22210c4' class='dr'>GN KARTING</td><td data-id='r22210c5' class=''></td><td data-id='r22210c6' class=''></td><td data-id='r22210c7' class='tn'>59.765</td><td data-id='r22210c8' class='ib'>3 Giri</td><td data-id='r22210c9' class='in'>5.187</td><td data-id='r22210c10' class='in'>58.962</td><td data-id='r22210c11' class='to'>1:07.</td><td data-id='r22210c12' class='in'>5</td><td data-id='r22210c13' class=''></td></tr><tr data-id='r22331' data-pos='16'><td data-id='r22331c1' class='si'></td><td class='rk'><div><p data-id='r22331c2' class=''></p></div></td><td class='no'><div data-id='r22331c3' class='no4'>32</div></td><td data-id='r22331c4' class='dr'>TEST</td><td data-id='r22331c5' class=''></td><td data-id='r22331c6' class=''></td><td data-id='r22331c7' class=''></td><td data-id='r22331c8' class='ib'></td><td data-id='r22331c9' class='in'></td><td data-id='r22331c10' class='in'></td><td data-id='r22331c11' class='to'></td><td data-id='r22331c12' class='in'></td><td data-id='r22331c13' class=''></td></tr></tbody>"
                //"grid||<tbody><tr data-id='r0' class='head' data-pos='0'><td data-id='c1' data-type='grp' data-pr='6'></td><td data-id='c2' data-type='sta' data-pr='1'></td><td data-id='c3' data-type='rk' data-pr='1'>Cla</td><td data-id='c4' data-type='no' data-pr='1'>Kart</td><td data-id='c5' data-type='dr' data-pr='1' data-width='19' data-min='16'>Pilota</td><td data-id='c6' data-type='s1' data-pr='3' data-width='11' data-min='6'>S1</td><td data-id='c7' data-type='s2' data-pr='3' data-width='11' data-min='6'>S2</td><td data-id='c8' data-type='llp' data-pr='2' data-width='14' data-min='7'>Ultimo T.</td><td data-id='c9' data-type='gap' data-pr='1' data-width='14' data-min='8'>Distacco</td><td data-id='c10' data-type='int' data-pr='5' data-width='9' data-min='7'>Interv.</td><td data-id='c11' data-type='blp' data-pr='6' data-width='11' data-min='7'>M.giro</td><td data-id='c12' data-type='' data-pr='6' data-width='8' data-min='6'>Pena</td></tr><tr data-id='r31404' data-pos='1'><td data-id='r31404c1' class='in'></td><td data-id='r31404c2' class='in'></td><td class='rk'><div><p data-id='r31404c3' class=''></p></div></td><td class='no'><div data-id='r31404c4' class='no4'>40</div></td><td data-id='r31404c5' class='dr'>ALTOMARE</td><td data-id='r31404c6' class=''></td><td data-id='r31404c7' class=''></td><td data-id='r31404c8' class=''></td><td data-id='r31404c9' class='ib'></td><td data-id='r31404c10' class='in'></td><td data-id='r31404c11' class='in'></td><td data-id='r31404c12' class=''></td></tr><tr data-id='r31400' data-pos='2'><td data-id='r31400c1' class='in'></td><td data-id='r31400c2' class='in'></td><td class='rk'><div><p data-id='r31400c3' class=''></p></div></td><td class='no'><div data-id='r31400c4' class='no4'>13</div></td><td data-id='r31400c5' class='dr'>MIRMINA</td><td data-id='r31400c6' class=''></td><td data-id='r31400c7' class=''></td><td data-id='r31400c8' class=''></td><td data-id='r31400c9' class='ib'></td><td data-id='r31400c10' class='in'></td><td data-id='r31400c11' class='in'></td><td data-id='r31400c12' class=''></td></tr><tr data-id='r31403' data-pos='3'><td data-id='r31403c1' class='in'></td><td data-id='r31403c2' class='in'></td><td class='rk'><div><p data-id='r31403c3' class=''></p></div></td><td class='no'><div data-id='r31403c4' class='no4'>25</div></td><td data-id='r31403c5' class='dr'>PRIVITERA</td><td data-id='r31403c6' class=''></td><td data-id='r31403c7' class=''></td><td data-id='r31403c8' class=''></td><td data-id='r31403c9' class='ib'></td><td data-id='r31403c10' class='in'></td><td data-id='r31403c11' class='in'></td><td data-id='r31403c12' class=''></td></tr><tr data-id='r31415' data-pos='4'><td data-id='r31415c1' class='in'></td><td data-id='r31415c2' class='in'></td><td class='rk'><div><p data-id='r31415c3' class=''></p></div></td><td class='no'><div data-id='r31415c4' class='no4'>22</div></td><td data-id='r31415c5' class='dr'>CICERO</td><td data-id='r31415c6' class=''></td><td data-id='r31415c7' class=''></td><td data-id='r31415c8' class=''></td><td data-id='r31415c9' class='ib'></td><td data-id='r31415c10' class='in'></td><td data-id='r31415c11' class='in'></td><td data-id='r31415c12' class=''></td></tr><tr data-id='r31396' data-pos='5'><td data-id='r31396c1' class='in'></td><td data-id='r31396c2' class='in'></td><td class='rk'><div><p data-id='r31396c3' class=''></p></div></td><td class='no'><div data-id='r31396c4' class='no4'>27</div></td><td data-id='r31396c5' class='dr'>BERTOLAMI</td><td data-id='r31396c6' class=''></td><td data-id='r31396c7' class=''></td><td data-id='r31396c8' class=''></td><td data-id='r31396c9' class='ib'></td><td data-id='r31396c10' class='in'></td><td data-id='r31396c11' class='in'></td><td data-id='r31396c12' class=''></td></tr><tr data-id='r31856' data-pos='6'><td data-id='r31856c1' class='in'></td><td data-id='r31856c2' class='in'></td><td class='rk'><div><p data-id='r31856c3' class=''></p></div></td><td class='no'><div data-id='r31856c4' class='no4'>31</div></td><td data-id='r31856c5' class='dr'>PICCOLO</td><td data-id='r31856c6' class=''></td><td data-id='r31856c7' class=''></td><td data-id='r31856c8' class=''></td><td data-id='r31856c9' class='ib'></td><td data-id='r31856c10' class='in'></td><td data-id='r31856c11' class='in'></td><td data-id='r31856c12' class=''></td></tr><tr data-id='r31402' data-pos='7'><td data-id='r31402c1' class='in'></td><td data-id='r31402c2' class='in'></td><td class='rk'><div><p data-id='r31402c3' class=''></p></div></td><td class='no'><div data-id='r31402c4' class='no4'>1</div></td><td data-id='r31402c5' class='dr'>PALUMBO</td><td data-id='r31402c6' class=''></td><td data-id='r31402c7' class=''></td><td data-id='r31402c8' class=''></td><td data-id='r31402c9' class='ib'></td><td data-id='r31402c10' class='in'></td><td data-id='r31402c11' class='in'></td><td data-id='r31402c12' class=''></td></tr><tr data-id='r31412' data-pos='8'><td data-id='r31412c1' class='in'></td><td data-id='r31412c2' class='in'></td><td class='rk'><div><p data-id='r31412c3' class=''></p></div></td><td class='no'><div data-id='r31412c4' class='no4'>36</div></td><td data-id='r31412c5' class='dr'>LA FAUCI</td><td data-id='r31412c6' class=''></td><td data-id='r31412c7' class=''></td><td data-id='r31412c8' class=''></td><td data-id='r31412c9' class='ib'></td><td data-id='r31412c10' class='in'></td><td data-id='r31412c11' class='in'></td><td data-id='r31412c12' class=''></td></tr><tr data-id='r31913' data-pos='9'><td data-id='r31913c1' class='in'></td><td data-id='r31913c2' class='in'></td><td class='rk'><div><p data-id='r31913c3' class=''></p></div></td><td class='no'><div data-id='r31913c4' class='no4'>35</div></td><td data-id='r31913c5' class='dr'>SPARACINO</td><td data-id='r31913c6' class=''></td><td data-id='r31913c7' class=''></td><td data-id='r31913c8' class=''></td><td data-id='r31913c9' class='ib'></td><td data-id='r31913c10' class='in'></td><td data-id='r31913c11' class='in'></td><td data-id='r31913c12' class=''></td></tr><tr data-id='r31397' data-pos='10'><td data-id='r31397c1' class='in'></td><td data-id='r31397c2' class='in'></td><td class='rk'><div><p data-id='r31397c3' class=''></p></div></td><td class='no'><div data-id='r31397c4' class='no4'>30</div></td><td data-id='r31397c5' class='dr'>VELA</td><td data-id='r31397c6' class=''></td><td data-id='r31397c7' class=''></td><td data-id='r31397c8' class=''></td><td data-id='r31397c9' class='ib'></td><td data-id='r31397c10' class='in'></td><td data-id='r31397c11' class='in'></td><td data-id='r31397c12' class=''></td></tr><tr data-id='r31395' data-pos='11'><td data-id='r31395c1' class='in'></td><td data-id='r31395c2' class='in'></td><td class='rk'><div><p data-id='r31395c3' class=''></p></div></td><td class='no'><div data-id='r31395c4' class='no4'>26</div></td><td data-id='r31395c5' class='dr'>CASPO 88</td><td data-id='r31395c6' class=''></td><td data-id='r31395c7' class=''></td><td data-id='r31395c8' class=''></td><td data-id='r31395c9' class='ib'></td><td data-id='r31395c10' class='in'></td><td data-id='r31395c11' class='in'></td><td data-id='r31395c12' class=''></td></tr><tr data-id='r31410' data-pos='12'><td data-id='r31410c1' class='in'></td><td data-id='r31410c2' class='in'></td><td class='rk'><div><p data-id='r31410c3' class=''></p></div></td><td class='no'><div data-id='r31410c4' class='no4'>7</div></td><td data-id='r31410c5' class='dr'>CORVAIA</td><td data-id='r31410c6' class=''></td><td data-id='r31410c7' class=''></td><td data-id='r31410c8' class=''></td><td data-id='r31410c9' class='ib'></td><td data-id='r31410c10' class='in'></td><td data-id='r31410c11' class='in'></td><td data-id='r31410c12' class=''></td></tr><tr data-id='r31416' data-pos='13'><td data-id='r31416c1' class='in'></td><td data-id='r31416c2' class='in'></td><td class='rk'><div><p data-id='r31416c3' class=''></p></div></td><td class='no'><div data-id='r31416c4' class='no4'>11</div></td><td data-id='r31416c5' class='dr'>CIRASA</td><td data-id='r31416c6' class=''></td><td data-id='r31416c7' class=''></td><td data-id='r31416c8' class=''></td><td data-id='r31416c9' class='ib'></td><td data-id='r31416c10' class='in'></td><td data-id='r31416c11' class='in'></td><td data-id='r31416c12' class=''></td></tr><tr data-id='r31857' data-pos='14'><td data-id='r31857c1' class='in'></td><td data-id='r31857c2' class='in'></td><td class='rk'><div><p data-id='r31857c3' class=''></p></div></td><td class='no'><div data-id='r31857c4' class='no4'>37</div></td><td data-id='r31857c5' class='dr'>BAGLIERI</td><td data-id='r31857c6' class=''></td><td data-id='r31857c7' class=''></td><td data-id='r31857c8' class=''></td><td data-id='r31857c9' class='ib'></td><td data-id='r31857c10' class='in'></td><td data-id='r31857c11' class='in'></td><td data-id='r31857c12' class=''></td></tr><tr data-id='r31815' data-pos='15'><td data-id='r31815c1' class='in'></td><td data-id='r31815c2' class='in'></td><td class='rk'><div><p data-id='r31815c3' class=''></p></div></td><td class='no'><div data-id='r31815c4' class='no4'>8</div></td><td data-id='r31815c5' class='dr'>SPINELLA</td><td data-id='r31815c6' class=''></td><td data-id='r31815c7' class=''></td><td data-id='r31815c8' class=''></td><td data-id='r31815c9' class='ib'></td><td data-id='r31815c10' class='in'></td><td data-id='r31815c11' class='in'></td><td data-id='r31815c12' class=''></td></tr><tr data-id='r31411' data-pos='16'><td data-id='r31411c1' class='in'></td><td data-id='r31411c2' class='in'></td><td class='rk'><div><p data-id='r31411c3' class=''></p></div></td><td class='no'><div data-id='r31411c4' class='no4'>20</div></td><td data-id='r31411c5' class='dr'>BOTTARI</td><td data-id='r31411c6' class=''></td><td data-id='r31411c7' class=''></td><td data-id='r31411c8' class=''></td><td data-id='r31411c9' class='ib'></td><td data-id='r31411c10' class='in'></td><td data-id='r31411c11' class='in'></td><td data-id='r31411c12' class=''></td></tr><tr data-id='r31399' data-pos='17'><td data-id='r31399c1' class='in'></td><td data-id='r31399c2' class='in'></td><td class='rk'><div><p data-id='r31399c3' class=''></p></div></td><td class='no'><div data-id='r31399c4' class='no4'>19</div></td><td data-id='r31399c5' class='dr'>GRIMA</td><td data-id='r31399c6' class=''></td><td data-id='r31399c7' class=''></td><td data-id='r31399c8' class=''></td><td data-id='r31399c9' class='ib'></td><td data-id='r31399c10' class='in'></td><td data-id='r31399c11' class='in'></td><td data-id='r31399c12' class=''></td></tr><tr data-id='r31409' data-pos='18'><td data-id='r31409c1' class='in'></td><td data-id='r31409c2' class='in'></td><td class='rk'><div><p data-id='r31409c3' class=''></p></div></td><td class='no'><div data-id='r31409c4' class='no4'>15</div></td><td data-id='r31409c5' class='dr'>MURIANA</td><td data-id='r31409c6' class=''></td><td data-id='r31409c7' class=''></td><td data-id='r31409c8' class=''></td><td data-id='r31409c9' class='ib'></td><td data-id='r31409c10' class='in'></td><td data-id='r31409c11' class='in'></td><td data-id='r31409c12' class=''></td></tr><tr data-id='r31406' data-pos='19'><td data-id='r31406c1' class='in'></td><td data-id='r31406c2' class='in'></td><td class='rk'><div><p data-id='r31406c3' class=''></p></div></td><td class='no'><div data-id='r31406c4' class='no4'>4</div></td><td data-id='r31406c5' class='dr'>MESSINA</td><td data-id='r31406c6' class=''></td><td data-id='r31406c7' class=''></td><td data-id='r31406c8' class=''></td><td data-id='r31406c9' class='ib'></td><td data-id='r31406c10' class='in'></td><td data-id='r31406c11' class='in'></td><td data-id='r31406c12' class=''></td></tr><tr data-id='r31401' data-pos='20'><td data-id='r31401c1' class='in'></td><td data-id='r31401c2' class='in'></td><td class='rk'><div><p data-id='r31401c3' class=''></p></div></td><td class='no'><div data-id='r31401c4' class='no4'>18</div></td><td data-id='r31401c5' class='dr'>CHIARENZA 88</td><td data-id='r31401c6' class=''></td><td data-id='r31401c7' class=''></td><td data-id='r31401c8' class=''></td><td data-id='r31401c9' class='ib'></td><td data-id='r31401c10' class='in'></td><td data-id='r31401c11' class='in'></td><td data-id='r31401c12' class=''></td></tr><tr data-id='r31407' data-pos='21'><td data-id='r31407c1' class='in'></td><td data-id='r31407c2' class='in'></td><td class='rk'><div><p data-id='r31407c3' class=''></p></div></td><td class='no'><div data-id='r31407c4' class='no4'>16</div></td><td data-id='r31407c5' class='dr'>ITALIANO</td><td data-id='r31407c6' class=''></td><td data-id='r31407c7' class=''></td><td data-id='r31407c8' class=''></td><td data-id='r31407c9' class='ib'></td><td data-id='r31407c10' class='in'></td><td data-id='r31407c11' class='in'></td><td data-id='r31407c12' class=''></td></tr></tbody>"
                //'grid||<tbody><tr data-id='r0' class='head' data-pos='0'><td data-id='c1' data-type='grp' data-pr='6'></td><td data-id='c2' data-type='sta' data-pr='1'></td><td data-id='c3' data-type='rk' data-pr='1'>Cla</td><td data-id='c4' data-type='no' data-pr='1'>Kart</td><td data-id='c5' data-type='' data-pr='6' data-width='7'>Club</td><td data-id='c6' data-type='dr' data-pr='1' data-width='34' data-min='16'>Pilota</td><td data-id='c7' data-type='llp' data-pr='2' data-width='24' data-min='7'>Ultimo T.</td><td data-id='c8' data-type='blp' data-pr='1' data-width='24' data-min='7'>M.giro</td><td data-id='c9' data-type='tlp' data-pr='5' data-width='9' data-min='4'>Giri</td></tr><tr data-id='r24' data-pos='2'><td data-id='r24c1' class='gf'></td><td data-id='r24c2' class='in'></td><td class='rk'><div><p data-id='r24c3' class=''>2</p></div></td><td class='no'><div data-id='r24c4' class='no1'>4</div></td><td data-id='r24c5' class='in'></td><td data-id='r24c6' class='dr'>ANTO16</td><td data-id='r24c7' class='ti'>45.890</td><td data-id='r24c8' class='ib'>45.890</td><td data-id='r24c9' class='in'>2</td></tr><tr data-id='r25' data-pos='3'><td data-id='r25c1' class='gl'></td><td data-id='r25c2' class='in'></td><td class='rk'><div><p data-id='r25c3' class=''>3</p></div></td><td class='no'><div data-id='r25c4' class='no1'>7</div></td><td data-id='r25c5' class='in'></td><td data-id='r25c6' class='dr'>VIRGIO</td><td data-id='r25c7' class='ti'>46.327</td><td data-id='r25c8' class='ib'>46.327</td><td data-id='r25c9' class='in'>2</td></tr><tr data-id='r26' data-pos='1'><td data-id='r26c1' class='gs'></td><td data-id='r26c2' class='in'></td><td class='rk'><div><p data-id='r26c3' class=''>1</p></div></td><td class='no'><div data-id='r26c4' class='no1'>6</div></td><td data-id='r26c5' class='in'></td><td data-id='r26c6' class='dr'>BERZI14</td><td data-id='r26c7' class='tb'>44.668</td><td data-id='r26c8' class='ib'>44.668</td><td data-id='r26c9' class='in'>2</td></tr></tbody>' +
                //'grid||<tbody><tr data-id='r0' class='head' data-pos='0'><td data-id='c1' data-type='grp' data-pr='6'></td><td data-id='c2' data-type='sta' data-pr='1'></td><td data-id='c3' data-type='rk' data-pr='1'>Cla</td><td data-id='c4' data-type='no' data-pr='1'>Kart</td><td data-id='c5' data-type='dr' data-pr='1' data-width='19' data-min='16'>Pilota</td><td data-id='c6' data-type='s1' data-pr='3' data-width='11' data-min='6'>S1</td><td data-id='c7' data-type='s2' data-pr='3' data-width='11' data-min='6'>S2</td><td data-id='c8' data-type='llp' data-pr='2' data-width='14' data-min='7'>Ultimo T.</td><td data-id='c9' data-type='gap' data-pr='1' data-width='14' data-min='8'>Distacco</td><td data-id='c10' data-type='int' data-pr='5' data-width='9' data-min='7'>Interv.</td><td data-id='c11' data-type='blp' data-pr='6' data-width='11' data-min='7'>M.giro</td><td data-id='c12' data-type='' data-pr='6' data-width='8' data-min='6'>Pena</td></tr><tr data-id='r1' data-pos='2'><td data-id='r1c1' class='gl'></td><td data-id='r1c2' class='sf'></td><td class='rk'><div><p data-id='r1c3' class=''>2</p></div></td><td class='no'><div data-id='r1c4' class='no4'>4</div></td><td data-id='r1c5' class='dr'>ALEMANNO</td><td data-id='r1c6' class=''></td><td data-id='r1c7' class=''></td><td data-id='r1c8' class='ti'>58.012</td><td data-id='r1c9' class='ib'>0.095</td><td data-id='r1c10' class='in'>0.095</td><td data-id='r1c11' class='in'>58.012</td><td data-id='r1c12' class=''></td></tr><tr data-id='r2' data-pos='1'><td data-id='r2c1' class='gf'></td><td data-id='r2c2' class='sf'></td><td class='rk'><div><p data-id='r2c3' class=''>1</p></div></td><td class='no'><div data-id='r2c4' class='no4'>25</div></td><td data-id='r2c5' class='dr'>PAPPA P.</td><td data-id='r2c6' class=''></td><td data-id='r2c7' class=''></td><td data-id='r2c8' class='tn'>58.139</td><td data-id='r2c9' class='in'>Giro 57</td><td data-id='r2c10' class='in'></td><td data-id='r2c11' class='in'>57.994</td><td data-id='r2c12' class=''></td></tr><tr data-id='r3' data-pos='3'><td data-id='r3c1' class='gs'></td><td data-id='r3c2' class='sf'></td><td class='rk'><div><p data-id='r3c3' class=''>3</p></div></td><td class='no'><div data-id='r3c4' class='no4'>12</div></td><td data-id='r3c5' class='dr'>GIAQUINTA</td><td data-id='r3c6' class=''></td><td data-id='r3c7' class=''></td><td data-id='r3c8' class='tn'>58.291</td><td data-id='r3c9' class='ib'>7.615</td><td data-id='r3c10' class='in'>7.520</td><td data-id='r3c11' class='in'>58.018</td><td data-id='r3c12' class=''></td></tr><tr data-id='r4' data-pos='7'><td data-id='r4c1' class='gm'></td><td data-id='r4c2' class='sf'></td><td class='rk'><div><p data-id='r4c3' class=''>7</p></div></td><td class='no'><div data-id='r4c4' class='no4'>37</div></td><td data-id='r4c5' class='dr'>BELLUARDO</td><td data-id='r4c6' class=''></td><td data-id='r4c7' class=''></td><td data-id='r4c8' class='tn'>58.358</td><td data-id='r4c9' class='ib'>17.889</td><td data-id='r4c10' class='in'>0.190</td><td data-id='r4c11' class='in'>58.105</td><td data-id='r4c12' class=''></td></tr><tr data-id='r5' data-pos='5'><td data-id='r5c1' class='gl'></td><td data-id='r5c2' class='sf'></td><td class='rk'><div><p data-id='r5c3' class=''>5</p></div></td><td class='no'><div data-id='r5c4' class='no4'>1</div></td><td data-id='r5c5' class='dr'>CORVAIA</td><td data-id='r5c6' class=''></td><td data-id='r5c7' class=''></td><td data-id='r5c8' class='tn'>58.391</td><td data-id='r5c9' class='ib'>15.544</td><td data-id='r5c10' class='in'>0.200</td><td data-id='r5c11' class='in'>58.228</td><td data-id='r5c12' class=''></td></tr><tr data-id='r6' data-pos='4'><td data-id='r6c1' class='gf'></td><td data-id='r6c2' class='sf'></td><td class='rk'><div><p data-id='r6c3' class=''>4</p></div></td><td class='no'><div data-id='r6c4' class='no4'>8</div></td><td data-id='r6c5' class='dr'>MONTEMAGNO</td><td data-id='r6c6' class=''></td><td data-id='r6c7' class=''></td><td data-id='r6c8' class='tn'>58.727</td><td data-id='r6c9' class='ib'>15.344</td><td data-id='r6c10' class='in'>7.729</td><td data-id='r6c11' class='in'>57.968</td><td data-id='r6c12' class=''></td></tr><tr data-id='r7' data-pos='6'><td data-id='r7c1' class='gf'></td><td data-id='r7c2' class='sf'></td><td class='rk'><div><p data-id='r7c3' class=''>6</p></div></td><td class='no'><div data-id='r7c4' class='no4'>31</div></td><td data-id='r7c5' class='dr'>ITALIANO</td><td data-id='r7c6' class=''></td><td data-id='r7c7' class=''></td><td data-id='r7c8' class='tn'>58.369</td><td data-id='r7c9' class='ib'>17.699</td><td data-id='r7c10' class='in'>2.155</td><td data-id='r7c11' class='in'>58.220</td><td data-id='r7c12' class=''></td></tr><tr data-id='r8' data-pos='9'><td data-id='r8c1' class='gl'></td><td data-id='r8c2' class='sf'></td><td class='rk'><div><p data-id='r8c3' class=''>9</p></div></td><td class='no'><div data-id='r8c4' class='no4'>38</div></td><td data-id='r8c5' class='dr'>GUZZARDI</td><td data-id='r8c6' class=''></td><td data-id='r8c7' class=''></td><td data-id='r8c8' class='tn'>58.143</td><td data-id='r8c9' class='ib'>18.555</td><td data-id='r8c10' class='in'>0.173</td><td data-id='r8c11' class='in'>58.074</td><td data-id='r8c12' class=''></td></tr><tr data-id='r9' data-pos='8'><td data-id='r9c1' class='gm'></td><td data-id='r9c2' class='sf'></td><td class='rk'><div><p data-id='r9c3' class=''>8</p></div></td><td class='no'><div data-id='r9c4' class='no4'>20</div></td><td data-id='r9c5' class='dr'>SANGIORGIO</td><td data-id='r9c6' class=''></td><td data-id='r9c7' class=''></td><td data-id='r9c8' class='tn'>57.943</td><td data-id='r9c9' class='ib'>18.382</td><td data-id='r9c10' class='in'>0.493</td><td data-id='r9c11' class='in'>57.943</td><td data-id='r9c12' class=''></td></tr><tr data-id='r10' data-pos='10'><td data-id='r10c1' class='gs'></td><td data-id='r10c2' class='sf'></td><td class='rk'><div><p data-id='r10c3' class=''>10</p></div></td><td class='no'><div data-id='r10c4' class='no4'>30</div></td><td data-id='r10c5' class='dr'>CHIARENZA</td><td data-id='r10c6' class=''></td><td data-id='r10c7' class=''></td><td data-id='r10c8' class='tn'>58.525</td><td data-id='r10c9' class='ib'>24.572</td><td data-id='r10c10' class='in'>6.017</td><td data-id='r10c11' class='in'>58.193</td><td data-id='r10c12' class=''></td></tr><tr data-id='r11' data-pos='11'><td data-id='r11c1' class='gs'></td><td data-id='r11c2' class='sf'></td><td class='rk'><div><p data-id='r11c3' class=''>11</p></div></td><td class='no'><div data-id='r11c4' class='no4'>23</div></td><td data-id='r11c5' class='dr'>SPARACINO</td><td data-id='r11c6' class=''></td><td data-id='r11c7' class=''></td><td data-id='r11c8' class='tn'>58.835</td><td data-id='r11c9' class='ib'>33.417</td><td data-id='r11c10' class='in'>8.845</td><td data-id='r11c11' class='in'>58.513</td><td data-id='r11c12' class=''></td></tr><tr data-id='r12' data-pos='12'><td data-id='r12c1' class='gs'></td><td data-id='r12c2' class='sf'></td><td class='rk'><div><p data-id='r12c3' class=''>12</p></div></td><td class='no'><div data-id='r12c4' class='no4'>33</div></td><td data-id='r12c5' class='dr'>CARUSO</td><td data-id='r12c6' class=''></td><td data-id='r12c7' class=''></td><td data-id='r12c8' class='tn'>59.320</td><td data-id='r12c9' class='ib'>56.989</td><td data-id='r12c10' class='in'>23.572</td><td data-id='r12c11' class='in'>58.852</td><td data-id='r12c12' class=''></td></tr><tr data-id='r13' data-pos='15'><td data-id='r13c1' class='gs'></td><td data-id='r13c2' class='sf'></td><td class='rk'><div><p data-id='r13c3' class=''>15</p></div></td><td class='no'><div data-id='r13c4' class='no4'>21</div></td><td data-id='r13c5' class='dr'>SCARDINO</td><td data-id='r13c6' class=''></td><td data-id='r13c7' class=''></td><td data-id='r13c8' class='tn'>1:00.500</td><td data-id='r13c9' class='ib'>27.207</td><td data-id='r13c10' class='in'>16.338</td><td data-id='r13c11' class='in'>59.211</td><td data-id='r13c12' class=''></td></tr><tr data-id='r14' data-pos='14'><td data-id='r14c1' class='gs'></td><td data-id='r14c2' class='sf'></td><td class='rk'><div><p data-id='r14c3' class=''>14</p></div></td><td class='no'><div data-id='r14c4' class='no4'>36</div></td><td data-id='r14c5' class='dr'>NARDO</td><td data-id='r14c6' class=''></td><td data-id='r14c7' class=''></td><td data-id='r14c8' class='tn'>59.810</td><td data-id='r14c9' class='ib'>10.869</td><td data-id='r14c10' class='in'>10.869</td><td data-id='r14c11' class='in'>59.349</td><td data-id='r14c12' class=''></td></tr><tr data-id='r15' data-pos='13'><td data-id='r15c1' class='gs'></td><td data-id='r15c2' class='sf'></td><td class='rk'><div><p data-id='r15c3' class=''>13</p></div></td><td class='no'><div data-id='r15c4' class='no4'>11</div></td><td data-id='r15c5' class='dr'>D'ANGELO</td><td data-id='r15c6' class=''></td><td data-id='r15c7' class=''></td><td data-id='r15c8' class='tn'>1:00.285</td><td data-id='r15c9' class='ib'>1 Giro</td><td data-id='r15c10' class='in'>1 Giro</td><td data-id='r15c11' class='in'>58.531</td><td data-id='r15c12' class=''></td></tr></tbody>' +
                //'grid||<tbody><tr data-id='r0' class='head' data-pos='0'><td data-id='c1' data-type='grp' data-pr='6'></td><td data-id='c2' data-type='sta' data-pr='1'></td><td data-id='c3' data-type='rk' data-pr='1'>Cla</td><td data-id='c4' data-type='no' data-pr='1'>Kart</td><td data-id='c5' data-type='dr' data-pr='1' data-width='19' data-min='16'>Pilota</td><td data-id='c6' data-type='s1' data-pr='3' data-width='11' data-min='6'>S1</td><td data-id='c7' data-type='s2' data-pr='3' data-width='11' data-min='6'>S2</td><td data-id='c8' data-type='llp' data-pr='2' data-width='14' data-min='7'>Ultimo T.</td><td data-id='c9' data-type='gap' data-pr='1' data-width='14' data-min='8'>Distacco</td><td data-id='c10' data-type='int' data-pr='5' data-width='9' data-min='7'>Interv.</td><td data-id='c11' data-type='blp' data-pr='6' data-width='11' data-min='7'>M.giro</td><td data-id='c12' data-type='' data-pr='6' data-width='8' data-min='6'>Pena</td></tr><tr data-id='r1' data-pos='1'><td data-id='r1c1' class='gs'></td><td data-id='r1c2' class='sf'></td><td class='rk'><div><p data-id='r1c3' class=''>1</p></div></td><td class='no'><div data-id='r1c4' class='no1'>31</div></td><td data-id='r1c5' class='dr'>FEDE18</td><td data-id='r1c6' class=''></td><td data-id='r1c7' class=''></td><td data-id='r1c8' class='tn'>59.289</td><td data-id='r1c9' class='in'>Giro 15</td><td data-id='r1c10' class='in'></td><td data-id='r1c11' class='in'>58.844</td><td data-id='r1c12' class=''></td></tr><tr data-id='r2' data-pos='2'><td data-id='r2c1' class='gs'></td><td data-id='r2c2' class='sf'></td><td class='rk'><div><p data-id='r2c3' class=''>2</p></div></td><td class='no'><div data-id='r2c4' class='no1'>28</div></td><td data-id='r2c5' class='dr'>HIGHLANDER 19</td><td data-id='r2c6' class=''></td><td data-id='r2c7' class=''></td><td data-id='r2c8' class='tn'>59.459</td><td data-id='r2c9' class='ib'>4.433</td><td data-id='r2c10' class='in'>4.433</td><td data-id='r2c11' class='in'>59.104</td><td data-id='r2c12' class=''></td></tr><tr data-id='r3' data-pos='5'><td data-id='r3c1' class='gs'></td><td data-id='r3c2' class='sf'></td><td class='rk'><div><p data-id='r3c3' class=''>5</p></div></td><td class='no'><div data-id='r3c4' class='no1'>16</div></td><td data-id='r3c5' class='dr'>ALINO92</td><td data-id='r3c6' class=''>24.000</td><td data-id='r3c7' class=''>23.402</td><td data-id='r3c8' class='tn'>1:02.416</td><td data-id='r3c9' class='ib'>19.887</td><td data-id='r3c10' class='in'>1.056</td><td data-id='r3c11' class='in'>59.877</td><td data-id='r3c12' class=''></td></tr><tr data-id='r4' data-pos='3'><td data-id='r4c1' class='gs'></td><td data-id='r4c2' class='sf'></td><td class='rk'><div><p data-id='r4c3' class=''>3</p></div></td><td class='no'><div data-id='r4c4' class='no1'>40</div></td><td data-id='r4c5' class='dr'>JAK92</td><td data-id='r4c6' class=''></td><td data-id='r4c7' class=''></td><td data-id='r4c8' class='tn'>59.905</td><td data-id='r4c9' class='ib'>16.743</td><td data-id='r4c10' class='in'>12.310</td><td data-id='r4c11' class='in'>59.521</td><td data-id='r4c12' class=''></td></tr><tr data-id='r5' data-pos='6'><td data-id='r5c1' class='gs'></td><td data-id='r5c2' class='sf'></td><td class='rk'><div><p data-id='r5c3' class=''>6</p></div></td><td class='no'><div data-id='r5c4' class='no1'>19</div></td><td data-id='r5c5' class='dr'>DAVID 4</td><td data-id='r5c6' class=''></td><td data-id='r5c7' class=''></td><td data-id='r5c8' class='tn'>1:06.644</td><td data-id='r5c9' class='ib'>24.279</td><td data-id='r5c10' class='in'>4.392</td><td data-id='r5c11' class='in'>59.606</td><td data-id='r5c12' class=''></td></tr><tr data-id='r6' data-pos='4'><td data-id='r6c1' class='gs'></td><td data-id='r6c2' class='sf'></td><td class='rk'><div><p data-id='r6c3' class=''>4</p></div></td><td class='no'><div data-id='r6c4' class='no1'>25</div></td><td data-id='r6c5' class='dr'>MODDA</td><td data-id='r6c6' class=''></td><td data-id='r6c7' class=''></td><td data-id='r6c8' class='tn'>1:00.857</td><td data-id='r6c9' class='ib'>18.831</td><td data-id='r6c10' class='in'>2.088</td><td data-id='r6c11' class='in'>1:00.132</td><td data-id='r6c12' class=''></td></tr><tr data-id='r7' data-pos='7'><td data-id='r7c1' class='gs'></td><td data-id='r7c2' class='sf'></td><td class='rk'><div><p data-id='r7c3' class=''>7</p></div></td><td class='no'><div data-id='r7c4' class='no1'>23</div></td><td data-id='r7c5' class='dr'>VIPER0000</td><td data-id='r7c6' class=''></td><td data-id='r7c7' class=''></td><td data-id='r7c8' class='tn'>1:01.736</td><td data-id='r7c9' class='ib'>55.099</td><td data-id='r7c10' class='in'>30.820</td><td data-id='r7c11' class='in'>1:01.588</td><td data-id='r7c12' class=''></td></tr><tr data-id='r8' data-pos='8'><td data-id='r8c1' class='gs'></td><td data-id='r8c2' class='sf'></td><td class='rk'><div><p data-id='r8c3' class=''>8</p></div></td><td class='no'><div data-id='r8c4' class='no1'>38</div></td><td data-id='r8c5' class='dr'>CARNEX</td><td data-id='r8c6' class=''></td><td data-id='r8c7' class=''></td><td data-id='r8c8' class='tn'>1:03.857</td><td data-id='r8c9' class='ib'>1 Giro</td><td data-id='r8c10' class='in'>1 Giro</td><td data-id='r8c11' class='in'>1:02.461</td><td data-id='r8c12' class=''></td></tr></tbody>' +
                // 'grid||<tbody><tr data-id='r0' class='head' data-pos='0'><td data-id='c1' data-type='grp' data-pr='6'></td><td data-id='c2' data-type='sta' data-pr='1'></td><td data-id='c3' data-type='rk' data-pr='1'>Cla</td><td data-id='c4' data-type='no' data-pr='1'>Kart</td><td data-id='c5' data-type='dr' data-pr='1' data-width='20' data-min='16'>Pilota</td><td data-id='c6' data-type='s1' data-pr='3' data-width='11' data-min='6'>S1</td><td data-id='c7' data-type='s2' data-pr='3' data-width='11' data-min='6'>S2</td><td data-id='c8' data-type='llp' data-pr='2' data-width='14' data-min='7'>Ultimo T.</td><td data-id='c9' data-type='blp' data-pr='1' data-width='14' data-min='7'>M.giro</td><td data-id='c10' data-type='int' data-pr='5' data-width='11' data-min='7'>Interv.</td><td data-id='c11' data-type='gap' data-pr='4' data-width='10' data-min='7'>Distacco</td><td data-id='c12' data-type='tlp' data-pr='5' data-width='5' data-min='4'>Giri</td></tr><tr data-id='r1' data-pos='1'><td data-id='r1c1' class='gs'></td><td data-id='r1c2' class='ss'></td><td class='rk'><div><p data-id='r1c3' class=''>1</p></div></td><td class='no'><div data-id='r1c4' class='no1'>37</div></td><td data-id='r1c5' class='dr'>TEST KART RIF</td><td data-id='r1c6' class='ib'>29.69</td><td data-id='r1c7' class='ib'>29.21</td><td data-id='r1c8' class='tn'>59.022</td><td data-id='r1c9' class='ib'>58.906</td><td data-id='r1c10' class='in'></td><td data-id='r1c11' class='in'></td><td data-id='r1c12' class='in'>51</td></tr></tbody>'
                'msg||';

                parseSocketMessage(testSocket);

                // testSocket = 'r2c6|ti|1:01.953\nr2c7|ib|51.953\nr2c8|in|22.744\nr2c9|in|2\nr2|*|61.95\nr2c4|no|5';

                // //Holykartroma
                // //grp: 'c1'
                // // sta: 'c2'
                // // rk: 'c3'
                // // no: 'c4'
                // // dr: 'c5'
                // // s1: ''
                // // s2: ''
                // // llp: 'c6'
                // // blp: 'c7'
                // // int: ''
                // // gap: 'c8'
                // // tlp: 'c9'
                // // pit: ''

                testSockets = [
                    'dyn1|text|Giro 15/22',
                    'r31395c3||1',
                    'r31415|#|8',
                    'r31856|*|57795|28588',
                    'r31856|#|7',
                    'r31400|*|57901|28709',
                    'r31965c8|tn|1:04.063\nr31965c9|ib|1:00.038\nr31965c10|in|38.217\nr31968c2|sf|\nr31968c8|tn|1:11.618\nr31965|*||\nr31968|*||\n'
                ]
                // testSockets = [
                //     'dyn1|text|Giro 15/15 '+
                //     light|lf| '+'
                //     com||<p><b>20:30</b><span data-flag="chequered"></span>Arrivo</p>
                //     r31969c2|sf|
                //     r31969c8|tn|59.081
                //     r31969c9|in|Giro 15
                //     r31966c2|in|
                //     r31963c2|in|
                //     r31967c2|in|
                //     r31965c2|in|
                //     r31964c2|in|
                //     r31968c2|in|
                //     r31969|*||'
                // ]
                for (var i = 0; i <= testSockets.length; i++) {
                    parseSocketMessage(testSockets[i]);
                }
                // setTimeout(function () {
                //     testSocket = 'r31395c3||1'
                //     parseSocketMessage(testSocket);
                // }, 2000);
            }

            //constructor();

            var clearGrid = function(){
                $scope.console = "";
                $scope.grid = [];
                $ionicScrollDelegate.scrollTop();
            }

            $scope.$on('$ionicView.enter', function (scopes, states) {
                $scope.isS1S2Enable = !(localStorageService.getValue(configurationService.compactViewKey) === 'true')
                $scope.isDriverAutoSelectEnable = (localStorageService.getValue(configurationService.driverAutoSelectKey) === 'true')
                $scope.showSpeed = (localStorageService.getValue(configurationService.showSpeedKey) === 'true')
                clearGrid();
                $scope.checkSpeed();
                checkDriverAutoSelect();
                constructor();
            });

            $scope.$on('$ionicView.afterLeave', function (scopes, states) {
            });
        }])