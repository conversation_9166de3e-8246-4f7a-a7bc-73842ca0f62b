/* StintCard Component Styles - Compact Version */

.stint-card-compact {
  margin: 0;
  padding: 12px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  background-color: var(--ion-color-light);
  transition: all 0.2s ease;
  height: 100%;
  display: flex;
  flex-direction: column;
  cursor: pointer;
}

.stint-card-compact:hover {
  transform: translateY(-3px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.stint-card-compact.active-stint {
  border-left: 4px solid var(--ion-color-primary);
  background-color: var(--ion-color-light-shade);
}

.stint-card-compact.completed-stint {
  border-left: 4px solid var(--ion-color-success);
  background-color: rgba(var(--ion-color-success-rgb), 0.1);
  opacity: 0.9;
}

.stint-card-compact.extra-stint {
  border-left: 4px solid var(--ion-color-warning);
}

.stint-card-header-compact {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.stint-number-compact {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
}

.stint-status-badge-compact {
  font-size: 0.6rem;
  padding: 3px 6px;
}

.extra-stint-chip {
  font-size: 0.6rem;
  height: 20px;
  padding: 0 6px;
}



.stint-card-content-compact {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.stint-time-info-compact {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 0.8rem;
}

.time-item-compact {
  display: flex;
  align-items: center;
  gap: 4px;
}


/* Add or modify styles for extra stints */
.stint-card-compact.extra-stint {
  border-left: 5px solid var(--ion-color-tertiary, #5260ff); /* Use Ionic tertiary color or a fallback purple */
}

/* Optional: Style the chip inside the modal if it's an extra stint */
.stint-details-container .extra-stint .ion-chip {
  --background: var(--ion-color-tertiary, #5260ff);
  --color: var(--ion-color-tertiary-contrast, #fff);
}

.time-separator-compact {
  color: var(--ion-color-medium);
}

.stint-stats-compact {
  display: flex;
  gap: 12px;
  margin-top: 4px;
}

.stat-item-compact {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 0.8rem;
  color: var(--ion-color-medium);
}

.pit-time-compact {
  margin-top: 8px;
  border-radius: 8px;
  overflow: hidden;
  background-color: var(--ion-color-light-shade);
}

.pit-time-content {
  padding: 8px 10px;
}

.pit-time-row {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 0.85rem;
  margin-bottom: 4px;
}

.pit-time-row:last-child {
  margin-bottom: 0;
}

.pit-icon {
  font-size: 1rem;
  color: var(--ion-color-medium);
}


/* Compact StintDetailsModal styles */
.stint-details-container {
  padding: 12px;
}

/* Header with status and time */
.stint-header-compact {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 16px;
}

.status-chip {
  align-self: flex-start;
  margin: 0;
}

.stint-time-info {
  display: flex;
  align-items: center;
  gap: 16px;
}

.time-info-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
  color: var(--ion-color-dark);
}

.time-info-item ion-icon {
  font-size: 16px;
}

/* Main info grid */
.stint-info-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
  margin-bottom: 20px;
}

.info-grid-item {
  background: var(--ion-color-light);
  border-radius: 8px;
  padding: 12px;
  display: flex;
  flex-direction: column;
}

.info-grid-label {
  font-size: 12px;
  color: var(--ion-color-medium);
  margin-bottom: 4px;
}

.info-grid-value {
  font-size: 18px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 4px;
}

.info-grid-diff {
  font-size: 13px;
  margin-top: 4px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.info-grid-diff ion-icon {
  font-size: 14px;
}

.color-success {
  color: var(--ion-color-success);
}

.color-danger {
  color: var(--ion-color-danger);
}

.color-medium {
  color: var(--ion-color-medium);
}

/* Edit form */
.edit-form-compact {
  margin-top: 16px;
}

.edit-section {
  margin-bottom: 16px;
}

.edit-section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.edit-section-header ion-label {
  font-weight: 500;
}

.time-input-group-compact {
  display: flex;
  align-items: flex-end;
  justify-content: center;
  gap: 4px;
  margin-bottom: 8px;
}

.time-input-field {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.time-input-field ion-label {
  font-size: 12px;
  color: var(--ion-color-medium);
  margin-bottom: 4px;
}

.time-input {
  --padding-start: 8px;
  --padding-end: 8px;
  --padding-top: 8px;
  --padding-bottom: 8px;
  --background: var(--ion-color-light);
  border-radius: 8px;
  text-align: center;
  font-weight: 600;
  width: 70px;
}

.time-separator {
  display: flex;
  align-items: flex-end;
  padding-bottom: 12px;
  font-weight: bold;
  font-size: 18px;
}

.time-hint {
  text-align: center;
  font-size: 12px;
  margin-top: 8px;
}

/* Dark mode adjustments */
@media (prefers-color-scheme: dark) {
  .time-input {
    --background: var(--ion-color-dark);
  }
}

/* Add styles for bridge/transition stints */
.stint-card-compact.bridge-stint {
  border-left: 4px solid var(--ion-color-warning);
  /* Remove the background color to match non-completed stints */
  background-color: var(--ion-color-light);
}

/* Optional: Style the chip inside the modal if it's a bridge stint */
.stint-details-container .bridge-stint .ion-chip {
  --background: var(--ion-color-warning, #ffce00);
  --color: var(--ion-color-warning-contrast, #000);
}

/* Add a visual indicator for stint duration */
.stint-duration-indicator {
  height: 4px;
  background-color: var(--ion-color-primary);
  margin-top: 8px;
  border-radius: 2px;
  overflow: hidden;
}

/* Different colors based on stint duration */
.stint-duration-indicator.duration-regular {
  background-color: var(--ion-color-primary);
}

.stint-duration-indicator.duration-bridge {
  background-color: var(--ion-color-warning);
}

.stint-duration-indicator.duration-extra {
  background-color: var(--ion-color-tertiary);
}

/* Responsive adjustments */
@media (max-width: 576px) {
  .stint-card {
    min-height: 130px;
  }
  .stint-times-section {
    grid-template-columns: 1fr;
    gap: 8px;
  }
}

/* Add these styles for the comparison view */
.stint-comparison-view {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.comparison-container {
  padding: 8px 0;
}

.comparison-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
}

.comparison-label {
  font-weight: 500;
  color: var(--ion-color-medium);
}

.comparison-value {
  font-weight: 600;
  font-size: 16px;
}

.comparison-diff {
  display: flex;
  align-items: center;
  gap: 4px;
}

.comparison-diff ion-icon {
  font-size: 18px;
}

.color-success {
  color: var(--ion-color-success);
}

.color-danger {
  color: var(--ion-color-danger);
}

.color-medium {
  color: var(--ion-color-medium);
}

.comparison-divider {
  height: 1px;
  background-color: var(--ion-color-light-shade);
  margin: 8px 0;
}

.comparison-progress-container {
  margin-top: 12px;
}

.comparison-progress-label {
  font-size: 12px;
  color: var(--ion-color-medium);
  margin-bottom: 4px;
}

.comparison-progress-bar {
  margin: 8px 0;
}

.comparison-progress-value {
  text-align: right;
  font-size: 12px;
  font-weight: 600;
}

/* Dark mode adjustments */
@media (prefers-color-scheme: dark) {
  .comparison-divider {
    background-color: var(--ion-color-dark-tint);
  }
}

/* StintDetailsModal styles */
.stint-modal {
  --width: 90%;
  --height: auto;
  --max-height: 80%;
  --border-radius: 16px;
  --box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

/* Time Range Banner */
.time-range-banner {
  background: linear-gradient(to right, var(--ion-color-primary), var(--ion-color-primary-shade));
  color: white;
  padding: 16px;
  margin-bottom: 16px;
}

.time-range-content {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.time-range-label {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
  opacity: 0.9;
}

.time-range-label ion-icon {
  font-size: 16px;
}

.time-range-value {
  font-size: 20px;
  font-weight: 600;
  letter-spacing: 0.5px;
}

/* Metrics Grid */
.stint-metrics-grid {
  padding: 0 12px;
}

.metric-card {
  background: var(--ion-color-light);
  border-radius: 12px;
  padding: 16px;
  height: 100%;
  display: flex;
  flex-direction: column;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.metric-label {
  font-size: 13px;
  color: var(--ion-color-medium);
  margin-bottom: 6px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.metric-value {
  font-size: 22px;
  font-weight: 600;
  margin-bottom: 4px;
}

.metric-diff {
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 4px;
  margin-top: 4px;
}

.metric-diff ion-icon {
  font-size: 16px;
}

/* Edit View */
.stint-edit-view {
  padding: 12px;
}

.edit-card {
  margin: 0;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.edit-header {
  margin-bottom: 16px;
}

.edit-header h2 {
  font-size: 18px;
  font-weight: 600;
  margin: 0;
  color: var(--ion-color-dark);
}

.edit-section {
  margin-bottom: 24px;
}

.edit-section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.edit-section-header h3 {
  font-size: 16px;
  font-weight: 500;
  margin: 0;
  color: var(--ion-color-dark);
}

.planned-value {
  font-size: 13px;
  color: var(--ion-color-medium);
}

.time-input-container {
  margin-bottom: 8px;
}

.time-input-group {
  display: flex;
  align-items: flex-end;
  gap: 8px;
}

.time-input-field {
  flex: 1;
}

.time-separator {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  font-weight: 600;
  color: var(--ion-color-medium);
  margin-bottom: 16px;
}

.time-input {
  --padding-start: 12px;
  --padding-end: 12px;
  --border-radius: 8px;
  --background: var(--ion-color-light);
  font-size: 18px;
  font-weight: 500;
}

/* Action Buttons */
.edit-actions {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-top: 24px;
}

.save-button {
  --border-radius: 8px;
  margin: 0;
}

.cancel-button {
  --border-radius: 8px;
  margin: 0;
}

/* Color utilities */
.color-success {
  color: var(--ion-color-success);
}

.color-danger {
  color: var(--ion-color-danger);
}

.color-medium {
  color: var(--ion-color-medium);
}


