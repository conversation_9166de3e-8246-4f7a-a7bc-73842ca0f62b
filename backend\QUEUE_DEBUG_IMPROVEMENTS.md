# Queue Debug Improvements

## Problem Analysis

The queue was still overflowing with the log showing:
```
[1] 🔍 OTR FIELD DETECTED: competitorId=17788, data.type=to, data.value=14.
[1] ⏱️ Pit current duration from otr: 17788 -> 14. (type: to)
[1] ⏳ Message queued (101 in queue) - Processing: grid=false, message=true
[1] 🚨 Queue overflow detected! 101 messages queued. This indicates a processing issue.
[1] 🔍 Processing flags: grid=false, message=true
```

**Issue**: `isProcessingMessage` was stuck at `true`, preventing queue processing.

## Debug Improvements Added

### ✅ **1. Enhanced Message Processing Logging**
```typescript
// Before
this.isProcessingMessage = true;
try {
  await this.handleUpdateMessage(message.data);
} finally {
  this.isProcessingMessage = false;
}

// After
this.isProcessingMessage = true;
try {
  console.log(`🔄 Processing update message (${Object.keys(message.data).length} fields)`);
  await this.handleUpdateMessage(message.data);
  console.log(`✅ Update message processed successfully`);
} finally {
  this.isProcessingMessage = false;
  console.log(`🔓 isProcessingMessage reset to false`);
}
```

### ✅ **2. Message Processing Timeout**
```typescript
// Add 5-second timeout to prevent hanging
const timeoutPromise = new Promise((_, reject) => {
  setTimeout(() => reject(new Error('Message processing timeout')), 5000);
});

await Promise.race([
  this.handleUpdateMessage(message.data),
  timeoutPromise
]);
```

### ✅ **3. Pit Duration Method Debugging**
```typescript
private async handlePitCurrentDurationUpdate(websocketId: string, durationValue: string): Promise<void> {
  if (!this.currentSession) {
    console.log(`⚠️ handlePitCurrentDurationUpdate: No current session`);
    return;
  }

  console.log(`🔄 handlePitCurrentDurationUpdate: Starting for ${websocketId} with value ${durationValue}`);
  
  try {
    // ... existing logic ...
  } catch (error) {
    console.error('Error handling pit duration update:', error);
  }
  
  console.log(`✅ handlePitCurrentDurationUpdate: Completed for ${websocketId}`);
}
```

### ✅ **4. Emergency Queue Recovery**
```typescript
// Enhanced safety check with recovery mechanism
if (this.messageQueue.length > 100) {
  console.error(`🚨 Queue overflow detected! ${this.messageQueue.length} messages queued.`);
  console.error(`🔍 Processing flags: grid=${this.isProcessingGrid}, message=${this.isProcessingMessage}`);
  
  // Force reset flags to recover
  console.error(`🔧 Force resetting processing flags to recover from stuck state`);
  this.isProcessingGrid = false;
  this.isProcessingMessage = false;
  
  // Try to process the queue immediately
  console.error(`🔧 Attempting to process queued messages after reset`);
  setImmediate(() => {
    this.processQueuedMessages().catch(error => {
      console.error('Error in emergency queue processing:', error);
    });
  });
}
```

## Expected Debug Output

### **Normal Processing**
```
🔄 Processing update message (3 fields)
🔄 handlePitCurrentDurationUpdate: Starting for 17788 with value 14.
✅ handlePitCurrentDurationUpdate: Completed for 17788
✅ Update message processed successfully
🔓 isProcessingMessage reset to false
📤 Processing queued message (99 remaining)
📤 Processing queued message (98 remaining)
...
```

### **If Timeout Occurs**
```
🔄 Processing update message (3 fields)
🔄 handlePitCurrentDurationUpdate: Starting for 17788 with value 14.
❌ Error in handleUpdateMessage: Error: Message processing timeout
🔓 isProcessingMessage reset to false
```

### **If Emergency Recovery Triggers**
```
🚨 Queue overflow detected! 101 messages queued.
🔍 Processing flags: grid=false, message=true
🔧 Force resetting processing flags to recover from stuck state
🔧 Attempting to process queued messages after reset
📤 Processing queued message (100 remaining)
📤 Processing queued message (99 remaining)
...
```

## Diagnostic Questions

With these improvements, we can now identify:

1. **Is the message processing starting?**
   - Look for: `🔄 Processing update message`

2. **Is the pit duration method completing?**
   - Look for: `✅ handlePitCurrentDurationUpdate: Completed`

3. **Is the processing flag being reset?**
   - Look for: `🔓 isProcessingMessage reset to false`

4. **Is there a timeout issue?**
   - Look for: `Error: Message processing timeout`

5. **Is emergency recovery working?**
   - Look for: `🔧 Force resetting processing flags`

## Potential Issues to Watch For

### **Database Connection Issues**
- If pit duration updates are hanging on database operations
- MongoDB connection timeouts or locks

### **Async/Await Issues**
- Promises not being properly awaited
- Unhandled promise rejections

### **Memory Issues**
- Large objects causing garbage collection delays
- Memory leaks in database operations

### **Race Conditions**
- Multiple async operations interfering with each other
- Database transaction conflicts

## Next Steps

1. **Run the log replay** with these debug improvements
2. **Monitor the console output** for the specific debug messages
3. **Identify where the processing gets stuck** based on the last debug message
4. **Use the timeout and recovery mechanisms** to prevent complete hangs

The enhanced logging will help pinpoint exactly where the message processing is getting stuck and whether the recovery mechanisms are working properly.
