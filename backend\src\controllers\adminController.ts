// backend/src/controllers/adminController.ts
import { Request, Response } from "express";
import mongoose, { ClientSession, UpdateWriteOpResult } from "mongoose"; // <-- Import UpdateWriteOpResult
import { AssignmentLog } from "../models/AssignmentLog";
import { Kart, <PERSON>Kart } from "../models/Kart";
import { Team /*, ITeam */ } from "../models/Team"; // <-- Removed ITeam import
import { Row } from "../models/Row";
import {
  sendErrorResponse,
  safelyEndSession,
  abortTransactionAndEndSession,
  // isValidObjectId, // <-- Removed isValidObjectId import
} from "../utils/controllerUtils";
import { sendWebSocketMessage } from "../websocket/websocket";

// --- Constants for WebSocket events ---
const ROW_UPDATE_EVENT = "rowsUpdated";
const KART_UPDATE_EVENT = "kartsUpdated";
const TEAM_UPDATE_EVENT = "teamsUpdated";
const ASSIGNMENT_LOG_EVENT = "assignmentHistoryCleared";

// Default speed value to reset to (e.g., 0 for 'Super Fast', 4 for 'Unknown')
const DEFAULT_RESET_SPEED = 4;
const DEFAULT_KART_STATUS = 'available'; // Default status
const DEFAULT_KART_SPEED = 4; // Default speed for new karts (Unknown)


/**
 * @description Unassign ALL karts currently assigned to ANY team.
 * @route POST /api/admin/karts/unassign-all-from-teams
 */
export const unassignAllKartsFromTeams = async (req: Request, res: Response): Promise<void> => {
  const operation = "unassignAllKartsFromTeams";
  let session: ClientSession | null = null;
  let unassignedKartCount = 0;
  let updatedTeamCount = 0;

  try {
      session = await mongoose.startSession();
      session.startTransaction();
      console.log(`[${operation}] Transaction started.`);

      // 1. Find all karts currently assigned to a team
      const assignedKarts = await Kart.find({ currentTeamId: { $ne: null } }).session(session);
      unassignedKartCount = assignedKarts.length;
      console.log(`[${operation}] Found ${unassignedKartCount} karts assigned to teams.`);

      if (unassignedKartCount === 0) {
          await abortTransactionAndEndSession(session, operation); // Abort, nothing to do
          res.status(200).json({ message: "No karts are currently assigned to teams." });
          // *** Add explicit return ***
          return;
      }

      const kartIdsToUpdate = assignedKarts.map(k => k._id);
      // FIX: Ensure teamIdsToUpdate only contains unique ObjectIds
      const uniqueTeamIds = [...new Set(assignedKarts.map(k => k.currentTeamId?.toString()).filter(id => id))];
      const teamIdsToUpdate = uniqueTeamIds.map(id => new mongoose.Types.ObjectId(id));


      // 2. Update Karts: Set currentTeamId to null, status to 'available'
      console.log(`[${operation}] Updating ${kartIdsToUpdate.length} karts...`);
      const kartUpdateResult = await Kart.updateMany(
          { _id: { $in: kartIdsToUpdate } },
          { $set: { currentTeamId: null, status: 'available' } },
          { session }
      );
      console.log(`[${operation}] Kart update result: matched=${kartUpdateResult.matchedCount}, modified=${kartUpdateResult.modifiedCount}`);

      // 3. Update Teams: Set currentKartId to null for affected teams
      console.log(`[${operation}] Updating ${teamIdsToUpdate.length} teams...`);
      const teamUpdateResult = await Team.updateMany(
          { _id: { $in: teamIdsToUpdate } },
          // Note: We don't add to pastKarts here as it's a bulk admin action
          { $set: { currentKartId: null } },
          { session }
      );
      updatedTeamCount = teamUpdateResult.modifiedCount;
      console.log(`[${operation}] Team update result: matched=${teamUpdateResult.matchedCount}, modified=${teamUpdateResult.modifiedCount}`);

      // 4. Commit Transaction
      await session.commitTransaction();
      console.log(`[${operation}] Transaction committed.`);

      // 5. Send WebSocket Notifications (after commit)
      sendWebSocketMessage({ event: KART_UPDATE_EVENT, payload: { type: "refreshAll" } });
      sendWebSocketMessage({ event: TEAM_UPDATE_EVENT, payload: { type: "refreshAll" } });
      console.log(`[${operation}] WebSocket messages sent.`);

      res.status(200).json({
          message: `Successfully unassigned ${unassignedKartCount} karts from ${updatedTeamCount} teams.`,
          unassignedKartCount: unassignedKartCount,
          updatedTeamCount: updatedTeamCount,
      });

  } catch (error: unknown) {
      await abortTransactionAndEndSession(session, operation, error);
      sendErrorResponse(res, "Error unassigning all karts from teams", 500, error);
  } finally {
      await safelyEndSession(session);
  }
};


/**
 * @description Clears assignment history and reassigns karts to teams based on matching numbers.
 * @route POST /api/admin/reset-assignments
 */
export const resetAssignments = async (req: Request, res: Response) => {
  let session: ClientSession | null = null;
  const operation = "resetAssignments";

  try {
    session = await mongoose.startSession();
    session.startTransaction();
    console.log(`[${operation}] Transaction started.`);

    // 1. Clear Assignment History
    console.log(`[${operation}] Clearing AssignmentLog collection...`);
    const deleteLogResult = await AssignmentLog.deleteMany({}, { session });
    console.log(
      `[${operation}] Cleared ${deleteLogResult.deletedCount} assignment logs.`
    );

    // 2. Clear ALL existing assignments on Karts, Teams, and Rows first
    console.log(
      `[${operation}] Clearing existing assignments on Karts, Teams, and Rows...`
    );
    await Promise.all([
      Kart.updateMany(
        {},
        {
          $set: {
            currentTeamId: null,
            currentRowId: null,
            status: "available",
          },
        },
        { session }
      ),
      Team.updateMany(
        {},
        { $set: { currentKartId: null, pastKarts: [] } },
        { session }
      ),
      Row.updateMany(
        {},
        { $set: { currentKartId: null, pastKarts: [] } },
        { session }
      ),
    ]);
    console.log(`[${operation}] Existing assignments cleared.`);

    // 5. Commit Transaction
    await session.commitTransaction();
    console.log(`[${operation}] Transaction committed.`);

    // 6. Send WebSocket Notifications
    console.log(`[${operation}] Sending WebSocket updates...`);
    sendWebSocketMessage({ event: ASSIGNMENT_LOG_EVENT, payload: { type: "clear" } });
    sendWebSocketMessage({ event: KART_UPDATE_EVENT, payload: { type: "refreshAll" } });
    sendWebSocketMessage({ event: TEAM_UPDATE_EVENT, payload: { type: "refreshAll" } });
    sendWebSocketMessage({ event: ROW_UPDATE_EVENT, payload: { type: "refreshAll" } });
    console.log(`[${operation}] WebSocket updates sent.`);

    res.status(200).json({
      // Updated message to reflect only clearing
      message: `Assignment history cleared. All kart, team, and row assignments have been reset. Karts set to 'available'.`,
      logsDeletedCount: deleteLogResult.deletedCount,
    });
  } catch (error: unknown) {
    await abortTransactionAndEndSession(session, operation, error);
    sendErrorResponse(res, "Error resetting assignments", 500, error);
  } finally {
    await safelyEndSession(session);
  }
};


/**
 * @description Delete ALL Teams and ALL Karts. Use with extreme caution.
 * @route DELETE /api/admin/teams-and-karts/all
 */
export const deleteAllTeamsAndKarts = async (req: Request, res: Response) => {
  const operation = "deleteAllTeamsAndKarts";
  console.log(`[${operation}] Received request to delete ALL teams and karts`);
  // Consider adding an extra confirmation step/query parameter in a real application

  try {
    // It's safer to perform these in separate operations or a transaction if needed,
    // but for a full wipe, separate might be acceptable.
    const deleteTeamsResult = await Team.deleteMany({});
    console.log(`[${operation}] Deleted ${deleteTeamsResult.deletedCount} teams.`);

    const deleteKartsResult = await Kart.deleteMany({});
    console.log(`[${operation}] Deleted ${deleteKartsResult.deletedCount} karts.`);

    // Optionally, clear related data in other collections (Rows, Logs, etc.) if desired
    // await Row.updateMany({}, { $set: { currentKartId: null, pastKarts: [] } });
    // await AssignmentLog.deleteMany({});
    // console.log(`[${operation}] Cleared related data in Rows and AssignmentLogs.`);

    // Notify clients via WebSocket AFTER successful deletion
    if (deleteTeamsResult.acknowledged && deleteKartsResult.acknowledged) {
      sendWebSocketMessage({ event: TEAM_UPDATE_EVENT, payload: { type: "refreshAll" } });
      sendWebSocketMessage({ event: KART_UPDATE_EVENT, payload: { type: "refreshAll" } });
      // sendWebSocketMessage({ event: ROW_UPDATE_EVENT, payload: { type: "refreshAll" } });
      // sendWebSocketMessage({ event: ASSIGNMENT_LOG_EVENT, payload: { type: "clear" } });
      console.log(`[${operation}] WebSocket messages sent for refreshAll.`);
    } else {
      console.warn(`[${operation}] DeleteMany operation(s) may not have been fully acknowledged by DB.`);
      // Still attempt to send WS message
      sendWebSocketMessage({ event: TEAM_UPDATE_EVENT, payload: { type: "refreshAll" } });
      sendWebSocketMessage({ event: KART_UPDATE_EVENT, payload: { type: "refreshAll" } });
    }

    res.status(200).json({
      message: `Successfully deleted ${deleteTeamsResult.deletedCount} teams and ${deleteKartsResult.deletedCount} karts.`,
      deletedTeams: deleteTeamsResult.deletedCount,
      deletedKarts: deleteKartsResult.deletedCount,
    });
  } catch (error: unknown) {
    console.error(`[${operation}] Error deleting all teams and karts:`, error);
    sendErrorResponse(res, "Error deleting all teams and karts", 500, error);
  }
};


/**
 * @description Reset the speed of ALL karts to a default value.
 * @route POST /api/admin/karts/reset-speeds
 */
export const resetKartSpeeds = async (req: Request, res: Response) => {
    const operation = "resetKartSpeeds";
    const resetValue = req.body.speed ?? DEFAULT_RESET_SPEED; // Allow overriding default via request body if needed

    // Validate the reset value if provided in the body
    if (typeof resetValue !== 'number' || !Number.isInteger(resetValue) || resetValue < 0) {
        return sendErrorResponse(res, `Invalid speed value provided: ${resetValue}. Must be a non-negative integer.`, 400);
    }

    console.log(`[${operation}] Received request to reset all kart speeds to ${resetValue}.`);

    try {
        const updateResult = await Kart.updateMany(
            {}, // Empty filter matches all karts
            { $set: { speed: resetValue } } // Set the speed field
        );

        console.log(`[${operation}] Updated speed for ${updateResult.modifiedCount} karts (matched ${updateResult.matchedCount}).`);

        // Notify clients via WebSocket AFTER successful update
        if (updateResult.acknowledged) {
            sendWebSocketMessage({ event: KART_UPDATE_EVENT, payload: { type: "refreshAll" } });
            console.log(`[${operation}] WebSocket message sent for kart refreshAll.`);
        } else {
            console.warn(`[${operation}] UpdateMany operation may not have been fully acknowledged by DB.`);
            // Still attempt to send WS message
            sendWebSocketMessage({ event: KART_UPDATE_EVENT, payload: { type: "refreshAll" } });
        }

        res.status(200).json({
            message: `Successfully reset speed to ${resetValue} for ${updateResult.modifiedCount} karts.`,
            matchedCount: updateResult.matchedCount,
            modifiedCount: updateResult.modifiedCount,
            resetValue: resetValue,
        });

    } catch (error: unknown) {
        console.error(`[${operation}] Error resetting kart speeds:`, error);
        sendErrorResponse(res, "Error resetting kart speeds", 500, error);
    }
};

/**
 * @description Batch create karts within a specified number range.
 * @route POST /api/admin/karts/batch-create
 */
export const batchCreateKarts = async (req: Request, res: Response) => {
    const operation = "batchCreateKarts";
    const { startNumber, endNumber } = req.body;

    // --- Input Validation ---
    const start = parseInt(startNumber, 10);
    const end = parseInt(endNumber, 10);

    if (isNaN(start) || isNaN(end) || start <= 0 || end <= 0 || start > end) {
        return sendErrorResponse(res, "Invalid range: 'startNumber' and 'endNumber' must be positive integers, and startNumber must be less than or equal to endNumber.", 400);
    }
    if (end - start + 1 > 100) { // Add a reasonable limit to prevent abuse
         return sendErrorResponse(res, "Invalid range: Cannot create more than 100 karts at once.", 400);
    }

    console.log(`[${operation}] Received request to create karts from #${start} to #${end}.`);

    let session: ClientSession | null = null;
    const createdKarts: IKart[] = [];
    const skippedNumbers: number[] = [];
    const errors: { number: number; message: string }[] = [];

    try {
        session = await mongoose.startSession();
        session.startTransaction();
        console.log(`[${operation}] Transaction started.`);

        // --- Check for existing karts in the range ---
        const existingKarts = await Kart.find({ number: { $gte: start, $lte: end } }, 'number', { session }).lean();
        const existingNumbers = new Set(existingKarts.map(k => k.number));
        console.log(`[${operation}] Found ${existingNumbers.size} existing karts in range ${start}-${end}.`);

        // --- Create Karts ---
        for (let num = start; num <= end; num++) {
            if (existingNumbers.has(num)) {
                console.log(`[${operation}] Skipping Kart #${num} as it already exists.`);
                skippedNumbers.push(num);
                continue; // Skip if number already exists
            }

            try {
                // Create kart document within the transaction
                const newKartData = {
                    number: num,
                    speed: DEFAULT_KART_SPEED,
                    status: DEFAULT_KART_STATUS,
                    // Add other default fields if necessary
                };
                // Use Kart.create which returns an array
                const created = await Kart.create([newKartData], { session });

                // FIX: Check if created[0] exists before pushing
                if (created && created.length > 0 && created[0]) {
                    createdKarts.push(created[0]);
                    console.log(`[${operation}] Prepared Kart #${num} for creation.`);
                } else {
                    // This case should be rare with Kart.create but handle defensively
                    console.warn(`[${operation}] Kart.create did not return the expected document for number ${num}.`);
                    errors.push({ number: num, message: 'Creation result was unexpected.' });
                }

            } catch (error: unknown) { // FIX: Use unknown instead of any
                 // Handle potential errors during individual creation (though duplicates are pre-checked)
                 console.error(`[${operation}] Error preparing Kart #${num}:`, error);
                 const errorMessage = error instanceof Error ? error.message : 'Unknown error during creation';
                 errors.push({ number: num, message: errorMessage });
                 // Decide if you want to abort the whole transaction on any single error
                 // For now, we'll collect errors and continue
            }
        }

        if (errors.length > 0) {
            // If any errors occurred during the loop, abort the transaction
            throw new Error(`Errors occurred during kart creation process. Aborting transaction.`);
        }

        // --- Commit Transaction ---
        await session.commitTransaction();
        console.log(`[${operation}] Transaction committed. ${createdKarts.length} karts created.`);

        // --- Send WebSocket Notification (after commit) ---
        if (createdKarts.length > 0) {
            sendWebSocketMessage({ event: KART_UPDATE_EVENT, payload: { type: "refreshAll" } }); // Or send specific created karts
            console.log(`[${operation}] WebSocket message sent for kart refreshAll.`);
        }

        res.status(201).json({
            message: `Batch kart creation complete. Created: ${createdKarts.length}, Skipped (already exist): ${skippedNumbers.length}.`,
            createdCount: createdKarts.length,
            skippedCount: skippedNumbers.length,
            skippedNumbers: skippedNumbers,
            // Optionally return created kart details (careful with large batches)
            // createdKarts: createdKarts.map(k => ({ _id: k._id, number: k.number })),
        });

    } catch (error: unknown) {
        // Abort transaction on any error during the process or commit
        await abortTransactionAndEndSession(session, operation, error);
        const errorMessage = error instanceof Error ? error.message : "Error during batch kart creation";
        sendErrorResponse(res, errorMessage, 500, { errors }); // Include specific errors if any
    } finally {
        // Ensure session is always ended
        await safelyEndSession(session);
    }
};
