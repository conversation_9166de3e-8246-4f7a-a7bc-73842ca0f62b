import { Schema, model, Types } from "mongoose";
import { Document } from 'mongoose'; // Import Document
import { IKart } from "./Kart";

// Interface for the entries within the karts array
export interface IPitLaneEntry {
    kartId?: Types.ObjectId | IKart | null; // Can be populated
    enteredAt?: Date | null;
    // exitedAt removed as it tracks current state
}

// Interface for the Pitlane document itself, extending Document
export interface IPitLane extends Document {
    // _id: string; // Changed
    _id: Types.ObjectId; // Use ObjectId type
    karts: IPitLaneEntry[]; // Array of karts currently in the pitlane
}

// Schema remains the same
const pitLaneSchema = new Schema<IPitLane>({
  karts: [
    {
      kartId: {
          type: Schema.Types.ObjectId,
          ref: "Kart",
          required: true
      },
      enteredAt: {
          type: Date,
          default: Date.now
      },
      _id: false
    },
  ],
}, {
    // timestamps: true // Optional
});

export const Pitlane = model<IPitLane>("Pitlane", pitLaneSchema);
