/* Pit Lane Modal Styles */
.pit-lane-modal .modal-wrapper {
  --background: var(--ion-background-color);
}

.kart-list {
  padding: 10px;
}

.kart-item {
  --background: var(--ion-color-light);
  --background-hover: var(--ion-color-light-shade);
  border-radius: 5px;
  margin-bottom: 5px;
  transition:
    transform 0.2s ease,
    box-shadow 0.2s ease;
}

.kart-item:last-child {
  margin-bottom: 0;
}

.kart-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

ion-segment {
  --background: var(--ion-color-light);
}

ion-segment-button {
  --indicator-color: var(--ion-color-primary);
}

.pit-lane-kart-card-header,
.pit-lane-team-card-header {
  padding: 12px 10px; /* Adjust padding as needed */
  text-align: center; /* Center content */
  /* Ensure text color contrasts with backgrounds */
  color: var(--card-text-color, #ffffff); /* Default to white text */
}

/* Specific title style */
.pit-lane-card-title {
  font-size: 1.4em; /* Make number larger */
  font-weight: bold;
  margin-bottom: 2px;
  line-height: 1.1;
}

/* Specific subtitle style */
.pit-lane-card-subtitle {
  font-size: 0.9em;
  opacity: 0.9; /* Slightly less prominent */
  line-height: 1.2;
  /* Ensure subtitle color contrasts */
  color: var(--card-subtitle-color, var(--card-text-color, #ffffff));
}

/* Optional: Note for assigned kart on team card */
.pit-lane-card-kart-note {
  display: block;
  font-size: 0.75em;
  margin-top: 4px;
  opacity: 0.8;
  color: var(--card-subtitle-color, var(--card-text-color, #ffffff));
}

/* --- Contrast adjustments for specific speed backgrounds --- */
/* Example: If 'average' (warning/yellow) background is light */
.speed-bg-warning .pit-lane-kart-card-header,
.speed-bg-warning .pit-lane-team-card-header {
  color: var(--card-text-color-dark, #222222); /* Use dark text */
}
.speed-bg-warning .pit-lane-card-subtitle,
.speed-bg-warning .pit-lane-card-kart-note {
  color: var(--card-subtitle-color-dark, var(--card-text-color-dark, #222222));
}

/* Example: If 'unknown' (medium/grey) background is light */
.speed-bg-medium .pit-lane-kart-card-header,
.speed-bg-medium .pit-lane-team-card-header {
  color: var(--card-text-color-dark, #222222); /* Use dark text */
}
.speed-bg-medium .pit-lane-card-subtitle,
.speed-bg-medium .pit-lane-card-kart-note {
  color: var(--card-subtitle-color-dark, var(--card-text-color-dark, #222222));
}
