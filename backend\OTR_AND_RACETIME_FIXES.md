# OTR Pit Duration and Race Time Fixes

## Issues Fixed

### ✅ **Issue 1: pitCurrentDuration Not Updated from otr Field**

**Problem**: The `otr` field was not properly updating `pitCurrentDuration` for active pit stops.

**Root Cause**: The `otr` field handling was checking for `data.type === 'to' || data.type === 'in'` for time on track, but pit duration updates were being routed incorrectly.

**Solution**: Updated the `otr` field logic to properly detect pit duration updates:

```typescript
case 'otr': // On track time column
  if (competitorId && data.value) {
    if (this.config.enableLogging) {
      console.log(`🔍 OTR FIELD DETECTED: competitorId=${competitorId}, data.type=${data.type}, data.value=${data.value}`);
    }
    
    if (data.type === 'to') {
      // Time on track update
      await this.handleTimeOnTrackUpdate(competitorId, data.value);
    } else {
      // All other data types for otr field should be pit duration updates
      // This includes when competitor is in pit and otr shows current pit duration
      await this.handlePitDurationUpdate(competitorId, data.value);
    }
  }
  break;
```

**Expected Behavior**:
```
r17788c14|otr|05.  → pitCurrentDuration: 5 seconds
r17788c14|otr|10.  → pitCurrentDuration: 10 seconds
r17788c14|otr|00.  → pitCurrentDuration: 0 seconds (pit exit)
```

### ✅ **Issue 2: raceTimeAtPitIn Always Says 0**

**Problem**: The `getCurrentRaceTime()` method was returning 0 because of incorrect logic in race time calculation.

**Root Cause**: The condition `if (this.raceDurationMs === 0 || this.raceTimeLeftMs === 0)` was wrong. When `raceTimeLeftMs` is 0, it means the race is over, not that there's no data.

**Solution**: Fixed the race time calculation logic:

```typescript
/**
 * Get current race time in seconds from dyn1 data
 * Race time = total race duration - time left
 */
private getCurrentRaceTime(): number {
  if (this.raceDurationMs === 0) {
    // No dyn1 data available yet - fallback to session time
    if (!this.currentSession) return 0;
    const sessionStartTime = this.currentSession.createdAt || new Date();
    const currentTime = new Date();
    const raceTimeMs = currentTime.getTime() - sessionStartTime.getTime();
    
    if (this.config.enableLogging) {
      console.log(`⏱️ Using session time fallback: ${Math.floor(raceTimeMs / 1000)}s (no dyn1 data)`);
    }
    
    return Math.floor(raceTimeMs / 1000);
  }
  
  // Calculate race time from dyn1 data
  // Race time = total race duration - time left
  const raceTimeMs = this.raceDurationMs - this.raceTimeLeftMs;
  const raceTimeSeconds = Math.floor(raceTimeMs / 1000);
  
  if (this.config.enableLogging) {
    console.log(`⏱️ Race time from dyn1: ${raceTimeSeconds}s (duration: ${this.raceDurationMs}ms, left: ${this.raceTimeLeftMs}ms)`);
  }
  
  return raceTimeSeconds;
}
```

**Expected Behavior**:
```
dyn1|text|01:30:00  → raceDurationMs: 5400000 (90 minutes)
dyn1|text|01:29:45  → raceTimeLeftMs: 5385000 (89.75 minutes)
getCurrentRaceTime() → 15 seconds (90 min - 89.75 min = 15 seconds)
```

## Enhanced Logging

### **OTR Field Detection**
```
🔍 OTR FIELD DETECTED: competitorId=17788, data.type=otr, data.value=05.
⏱️ Pit duration update from otr: 17788 -> 05. (type: otr)
⏱️ Updated pit current duration from otr: VANHAT KOIRAT - 5s
```

### **Race Time Calculation**
```
⏱️ Race duration set: 01:30:00 (5400000ms)
⏱️ Race time left: 01:29:45 (5385000ms)
⏱️ Race time from dyn1: 15s (duration: 5400000ms, left: 5385000ms)
🏁 Pit entry recorded: VANHAT KOIRAT - Race time: 15s
```

## Expected Database Results

### **Pit Stop with Correct Duration and Race Time**
```javascript
{
  _id: ObjectId("..."),
  sessionId: ObjectId("..."),
  competitorId: ObjectId("17788"),
  kartId: ObjectId("..."),
  pitInTime: "2024-11-09T15:30:45.000Z",
  pitOutTime: "2024-11-09T15:31:15.000Z",
  pitDuration: 30, // Total pit duration
  pitCurrentDuration: 0, // ✅ Updated from otr field during pit
  pitTotalDuration: 30, // Calculated at pit out
  raceTimeAtPitIn: 15, // ✅ Correct race time from dyn1 (not 0)
  raceTimeAtPitOut: 45, // ✅ Correct race time from dyn1
  reason: "Regular",
  isActive: false
}
```

### **Team Status Updates**
```javascript
{
  _id: ObjectId("..."),
  name: "VANHAT KOIRAT",
  status: "in_pit", // ✅ Updated when otr shows pit duration
  isActive: true
}
```

## Message Flow Example

### **Complete Pit Stop Sequence**
```
1. dyn1|text|01:30:00           → Set race duration: 90 minutes
2. dyn1|text|01:29:45           → Race time: 15 seconds elapsed
3. r17788c15|in|1               → Pit entry (1 second in pit)
   → Creates pit stop with raceTimeAtPitIn: 15
4. r17788c14|otr|05.            → Update pitCurrentDuration: 5
5. r17788c14|otr|10.            → Update pitCurrentDuration: 10
6. r17788c14|otr|15.            → Update pitCurrentDuration: 15
7. dyn1|text|01:29:15           → Race time: 45 seconds elapsed
8. r17788c15|in|0               → Pit exit
   → Updates pit stop with raceTimeAtPitOut: 45, pitDuration: 30
```

### **Database Result**
```javascript
{
  pitInTime: "2024-11-09T15:30:45.000Z",
  pitOutTime: "2024-11-09T15:31:15.000Z",
  pitCurrentDuration: 0, // Reset on exit
  pitDuration: 30, // Calculated from pit in/out times
  raceTimeAtPitIn: 15, // ✅ From dyn1 calculation
  raceTimeAtPitOut: 45, // ✅ From dyn1 calculation
  pitTotalDuration: 30 // Calculated at pit out
}
```

## Test Script

Created `backend/scripts/test-otr-and-racetime-fix.ts` to verify both fixes:

```bash
cd backend
npx ts-node scripts/test-otr-and-racetime-fix.ts
```

**Test Sequence**:
1. Creates session with competitors
2. Sends `dyn1` messages to set race time
3. Creates pit entry with numeric status
4. Sends multiple `otr` updates to test pit duration
5. Sends pit exit
6. Verifies database results

**Expected Output**:
```
✅ pitCurrentDuration updated from otr: 15s
✅ raceTimeAtPitIn calculated correctly: 15s
✅ OTR and Race Time fixes working correctly!
```

## Key Changes Made

### ✅ **OTR Field Routing**
**Before**: `data.type === 'to' || data.type === 'in'` → time on track
**After**: `data.type === 'to'` → time on track, all others → pit duration

### ✅ **Race Time Logic**
**Before**: `if (raceDurationMs === 0 || raceTimeLeftMs === 0)` → fallback
**After**: `if (raceDurationMs === 0)` → fallback (only when no dyn1 data)

### ✅ **Enhanced Logging**
- OTR field detection with data type and value
- Race time calculation with detailed breakdown
- Pit duration updates with source field

## Benefits

### ✅ **Accurate Pit Duration Tracking**
- Real-time updates from `otr` field
- Proper pit current duration display
- Automatic team status updates

### ✅ **Correct Race Time Context**
- Pit stops show actual race time when they occurred
- Proper race time calculation from `dyn1` data
- Fallback to session time when needed

### ✅ **Better Debugging**
- Comprehensive logging for troubleshooting
- Clear indication of data sources
- Easy verification of fixes

Both issues are now fixed and the system properly tracks pit duration from the `otr` field and calculates race time from `dyn1` messages!
