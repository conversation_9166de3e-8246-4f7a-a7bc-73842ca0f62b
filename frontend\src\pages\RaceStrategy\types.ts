// d:/Desktop/ManulilacRaceplanner/frontend/src/pages/RaceStrategy/types.ts
export interface StintInfo {
  _id?: string; // Add _id as it's needed for updates
  raceStrategyId?: string; // Add raceStrategyId if needed
  stintNumber: number;
  startTime: number;
  endTime: number;
  duration: number; // This is the planned stint duration
  laps: number;
  pitEndTime: number | null;
  isPitWindowValid: boolean;
  isExtraStint?: boolean;
  startLap?: number;
  endLap?: number;
  isUserModified?: boolean;
  actualPitEntryTime?: number | null;
  actualPitDuration?: number | null;
  actualStintDuration?: number | null;
  // Add these new properties for tracking planned vs actual
  plannedStintDuration?: number; // Explicit planned stint duration (same as duration)
  plannedPitDuration?: number; // Explicit planned pit duration
  // Calculated differences
  stintDurationDiff?: number; // Difference between actual and planned stint duration
  pitDurationDiff?: number; // Difference between actual and planned pit duration
  // Other properties
  pitDuration?: number;
  actualAvgLapTime?: number | null;
  bestLapTime?: number | null;
  isCompleted?: boolean;
}

export interface StrategyResult {
  totalRaceTimeSeconds: number;
  totalPitTimeSeconds: number;
  totalDrivingTimeSeconds: number;
  idealStintTimeSeconds: number;
  stints: StintInfo[];
  progress: number;
}



