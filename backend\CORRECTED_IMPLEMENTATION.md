# Corrected Implementation Based on 24h Serres Format

## Overview

Updated the implementation based on your clarifications about the actual data format from 24h serres.txt:

1. **Pit current duration** comes from `r id|c14|to|ss` (or `mm:ss`) format
2. **Internal race timer** is a countdown starting from first `dyn1` and syncs on each new `dyn1`
3. **data-type="pit"** is total pit number (not pit status)
4. **Pit entry race time** uses the countdown timer

## Key Changes Made

### ✅ **1. Corrected OTR Field Handling**

**Before**: `otr` field with `data.type='to'` was treated as time on track
**After**: `otr` field with `data.type='to'` is pit current duration

```typescript
case 'otr': // On track time column
  if (competitorId && data.value) {
    if (data.type === 'to') {
      // Pit current duration from 24h serres format: r id|c14|to|ss (or mm:ss)
      if (this.config.enableLogging) {
        console.log(`⏱️ Pit current duration from otr: ${competitorId} -> ${data.value} (type: ${data.type})`);
      }
      await this.handlePitCurrentDurationUpdate(competitorId, data.value);
    } else {
      // Other otr types - time on track
      await this.handleTimeOnTrackUpdate(competitorId, data.value);
    }
  }
  break;
```

### ✅ **2. Corrected Pit Field Handling**

**Before**: `pit` field was treated as pit status (IN/OUT)
**After**: `pit` field is total pit number (counter)

```typescript
case 'pit': // Pit column - data-type="pit" is total pit number
  if (competitorId && data.value) {
    if (this.config.enableLogging) {
      console.log(`🔍 PIT FIELD DETECTED: competitorId=${competitorId}, data.type=${data.type}, data.value=${data.value}`);
    }
    
    // data-type="pit" represents total pit number, not pit status
    // This is just a counter of total pit stops, not entry/exit events
    if (this.config.enableLogging) {
      console.log(`🏁 Pit count update: ${competitorId} -> ${data.value} (total pit stops)`);
    }
    await this.handlePitCountUpdate(competitorId, data.value);
  }
  break;
```

### ✅ **3. Enhanced Pit Current Duration Parser**

**Updated to handle 24h serres format**:

```typescript
/**
 * Parse pit current duration from 24h serres format
 * Format: r id|c14|to|ss (or mm:ss)
 * Examples: "05" = 5 seconds, "1:30" = 90 seconds
 */
private parsePitCurrentDuration(durationValue: string): number | null {
  if (!durationValue) return null;

  try {
    const cleanValue = durationValue.trim();
    
    // Format: mm:ss
    if (cleanValue.includes(':')) {
      const parts = cleanValue.split(':');
      if (parts.length === 2 && parts[0] && parts[1]) {
        const minutes = parseInt(parts[0]) || 0;
        const seconds = parseInt(parts[1]) || 0;
        return (minutes * 60) + seconds;
      }
    }
    
    // Format: ss (just seconds)
    const seconds = parseInt(cleanValue);
    return isNaN(seconds) ? null : seconds;
  } catch (error) {
    return null;
  }
}
```

### ✅ **4. Race Timer Implementation**

**Countdown timer that syncs with dyn1 messages**:

```typescript
/**
 * Handle race time updates from dyn1 messages
 * dyn1 contains milliseconds left in the race
 * Implements a countdown timer that updates between dyn1 messages
 */
private async handleRaceTimeUpdate(timeValue: string): Promise<void> {
  // Parse dyn1 time value
  let timeLeftMs = parseInt(timeValue) || 0;

  // Update race time tracking
  this.raceTimeLeftMs = timeLeftMs;
  this.lastDyn1Timestamp = new Date();
  
  // Set race duration from first dyn1 message and start timer
  if (this.raceDurationMs === 0 && timeLeftMs > 0) {
    this.raceDurationMs = timeLeftMs;
    this.startRaceTimer(); // ✅ Start countdown timer
  }
}

/**
 * Get current race time in seconds from dyn1 timer
 * Race time = total race duration - current time left (updated by timer)
 */
private getCurrentRaceTime(): number {
  if (this.raceDurationMs === 0) {
    // Fallback to session time if no dyn1 data
    return Math.floor(sessionTimeMs / 1000);
  }
  
  // Calculate current race time from timer
  const now = new Date();
  const elapsedSinceLastDyn1 = now.getTime() - this.lastDyn1Timestamp.getTime();
  const currentTimeLeftMs = Math.max(0, this.raceTimeLeftMs - elapsedSinceLastDyn1);
  
  // Race time = total duration - current time left
  const raceTimeMs = this.raceDurationMs - currentTimeLeftMs;
  return Math.floor(raceTimeMs / 1000);
}
```

## Expected Message Flow

### **24h Serres Format Messages**
```
1. dyn1|text|5400000           → Set race duration: 90 minutes, start timer
2. dyn1|text|5385000           → Sync timer: 15 seconds elapsed
3. r17788c15|in|3              → Pit count update: 3 total pit stops
4. r17788c14|to|05             → Pit current duration: 5 seconds
5. r17788c14|to|1:30           → Pit current duration: 90 seconds
6. r17788c14|to|00             → Pit current duration: 0 seconds (pit exit)
```

### **Database Results**
```javascript
// Pit stop with correct data
{
  pitInTime: "2024-11-09T15:30:45.000Z",
  raceTimeAtPitIn: 15, // ✅ From countdown timer (not 0)
  pitCurrentDuration: 90, // ✅ From r id|c14|to|1:30
  raceTimeAtPitOut: 105, // ✅ From countdown timer
  pitDuration: 90 // ✅ Calculated correctly
}

// Team status
{
  name: "VANHAT KOIRAT",
  status: "in_pit", // ✅ Updated when pitCurrentDuration > 0
  isActive: true
}
```

## Logging Output

### **OTR Pit Duration Updates**
```
🔍 OTR FIELD DETECTED: competitorId=17788, data.type=to, data.value=05
⏱️ Pit current duration from otr: 17788 -> 05 (type: to)
⏱️ Updated pit current duration from otr: VANHAT KOIRAT - 5s
🏁 Updated team status: VANHAT KOIRAT -> in_pit
```

### **Pit Count Updates**
```
🔍 PIT FIELD DETECTED: competitorId=17788, data.type=in, data.value=3
🏁 Pit count update: 17788 -> 3 (total pit stops)
```

### **Race Timer Updates**
```
⏱️ Race duration set: 01:30:00 (5400000ms)
⏱️ dyn1 update - Race time left: 01:29:45 (5385000ms)
⏱️ Race time from timer: 15s (duration: 5400000ms, left: 5385000ms, elapsed since dyn1: 0ms)
```

## Key Differences from Previous Implementation

### ✅ **OTR Field**
- **Before**: `data.type='to'` → time on track
- **After**: `data.type='to'` → pit current duration ✅

### ✅ **Pit Field**
- **Before**: `data.type='in'` → pit status (IN/OUT)
- **After**: `data.type='in'` → pit count (total number) ✅

### ✅ **Duration Format**
- **Before**: Expected `"05."` format
- **After**: Handles `"05"` and `"1:30"` formats ✅

### ✅ **Race Time**
- **Before**: Static calculation from session start
- **After**: Dynamic countdown timer synced with dyn1 ✅

## Benefits

### ✅ **Accurate Data Mapping**
- Correctly maps 24h serres format to database fields
- Handles both seconds and mm:ss duration formats
- Proper pit count vs pit status distinction

### ✅ **Real-time Updates**
- Countdown timer provides accurate race time
- Pit current duration updates in real-time
- Team status reflects current pit state

### ✅ **Robust Parsing**
- Handles various time formats (ss, mm:ss)
- Proper error handling for invalid formats
- Comprehensive logging for debugging

The implementation now correctly handles the actual 24h serres data format and provides accurate race timing and pit tracking!
