/**
 * Time formatting utilities for displaying time values consistently
 */
import { formatSecondsToTimeString, formatDuration } from './timeUtils';
/**
 * Displays seconds as HH:MM format (e.g., "02:30" for 2 hours 30 minutes)
 */
export const displaySecondsAsHHMM = (seconds: number): string => {
  return formatSecondsToTimeString(seconds, 'hh:mm');
};

/**
 * Displays seconds as MM:SS format (e.g., "05:30" for 5 minutes 30 seconds)
 */
export const displaySecondsAsMMSS = (seconds: number): string => {
  return formatSecondsToTimeString(seconds, 'mm:ss');
};

/**
 * Displays seconds as MM:SS format (e.g., "05:30" for 5 minutes 30 seconds)
 */
export const displaySecondsAsMMSSFFF = (seconds: number): string => {
  return formatSecondsToTimeString(seconds, 'mm:ss.fff');
};

/**
 * Displays seconds as HH:MM:SS format (e.g., "01:05:30" for 1 hour 5 minutes 30 seconds)
 */
export const displaySecondsAsHHMMSS = (seconds: number): string => {
  return formatSecondsToTimeString(seconds, 'hh:mm:ss');
};

/**
 * Displays seconds as a human-readable duration (e.g., "2h 30m" or "45m 20s")
 */
export const displaySecondsAsDuration = (seconds: number): string => {
  return formatDuration(seconds);
};

/**
 * Formats a Date object as a time string (HH:MM:SS)
 */
export const formatTimeFromDate = (date: Date): string => {
  return date.toTimeString().substring(0, 8);
};

/**
 * Formats a timestamp (milliseconds since epoch) as a time string (HH:MM:SS)
 */
export const formatTimeFromTimestamp = (timestamp: number): string => {
  return formatTimeFromDate(new Date(timestamp));
};

/**
 * Formats a race time (seconds elapsed) as a time string with optional sign
 * @param seconds Seconds elapsed in race
 * @param showPositiveSign Whether to show + for positive values
 * @returns Formatted time string (e.g., "+01:30" or "-00:45")
 */
export const formatRaceTime = (seconds: number, showPositiveSign = true): string => {
  const isNegative = seconds < 0;
  const absSeconds = Math.abs(seconds);
  const timeString = formatSecondsToTimeString(absSeconds, 'mm:ss');
  
  if (isNegative) {
    return `-${timeString}`;
  } else if (showPositiveSign && seconds > 0) {
    return `+${timeString}`;
  } else {
    return timeString;
  }
};

/**
 * Formats a lap time in seconds as a time string (MM:SS.sss)
 */
export const formatLapTime = (seconds: number): string => {
  if (seconds <= 0) return '--:--';
  
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = Math.floor(seconds % 60);
  const milliseconds = Math.floor((seconds % 1) * 1000);
  
  return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}.${milliseconds.toString().padStart(3, '0')}`;
};


