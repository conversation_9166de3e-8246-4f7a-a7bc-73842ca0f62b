/* eslint-disable @typescript-eslint/no-unused-vars */
// d:\Downloads\Nuova cartella\my-app\frontend\src\pages\PitRows\PitRows.tsx
import React, { useState, useEffect, useCallback } from 'react';
import {
  IonPage,
  IonHeader,
  IonToolbar,
  IonTitle,
  IonContent,
  IonButton,
  IonIcon,
  IonButtons,
  IonLoading,
  IonToast,
  IonBackButton,
  IonMenuButton,
} from '@ionic/react';
import { settingsOutline } from 'ionicons/icons';
import apiService from '../../services/apiService';

import axios from 'axios'; // Import axios to use isAxiosError

import KartRowSettings from './components/KartRowSettings';
import RowCard from './components/RowCard';
import PitLaneModal from './components/PitLaneModal';
import NextPits from './components/NextPits';
import AssignmentHistory from './components/AssignmentHistory';

// --- Interfaces ---
import { Row, Team, Kart, AssignmentLog } from '../../types';
import useWebSocket from '../../hooks/webSocketHandler'; // <-- Import the updated WebSocket hook
import { useDataContext } from '../../context/DataContext';

// --- Mock Data Interface ---
interface table_nextPits {
  id: number;
  team: string;
  kart: number;
  avg: string;
  best: string;
  pitTime: string;
}

// --- API Base URL ---
const API_BASE_URL = import.meta.env.VITE_API_URL;

// --- WebSocket Event Names (Match Backend) ---
const ROW_UPDATE_EVENT = "rowsUpdated";
const KART_UPDATE_EVENT = "kartsUpdated";
const TEAM_UPDATE_EVENT = "teamsUpdated";
const ASSIGNMENT_LOG_EVENT = "assignmentHistoryCleared"; // Or other history events

// --- Component ---
const PitRows: React.FC = () => {
  // --- Data Context ---
  const {
    useApexDatabase,
    selectedSession,
    selectedTeam,
    availableTeams,
    isLoadingTeams,
    refreshTeams,
    error: contextError
  } = useDataContext();

  const [showSettings, setShowSettings] = useState(false);
  const [selectedRow, setSelectedRow] = useState<Row | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);

  // --- State ---
  const [rows, setRows] = useState<Row[]>([]);
  const [teams, setTeams] = useState<Team[]>([]);
  const [karts, setKarts] = useState<Kart[]>([]);
  const [assignmentHistory, setAssignmentHistory] = useState<AssignmentLog[]>([]);

  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [isMutating, setIsMutating] = useState<boolean>(false);
  const [isClearingHistory, setIsClearingHistory] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [toastMessage, setToastMessage] = useState<string | null>(null);

  // --- Mock Data ---
  const [next_Pits, setNextPits] = useState<table_nextPits[]>([]);

  // --- Data Fetching ---
  // Helper to extract error messages safely
  const getErrorMessage = (error: unknown, defaultMessage: string = "An unknown error occurred"): string => {
    if (axios.isAxiosError(error)) {
      // Safely access response data message if it exists
      return error.response?.data?.message || error.message || defaultMessage;
    }
    if (error instanceof Error) {
      return error.message;
    }
    // Fallback for other unknown types
    if (typeof error === 'string') return error;
    return defaultMessage;
  };

  // Memoize the fetchData function to prevent recreation on each render
  const fetchData = useCallback(async (showMainLoader = true) => {
    if (showMainLoader) setIsLoading(true);
    setError(null);

    console.log('[PitRows] Fetching data...', useApexDatabase ? 'MIXED MODE (rows + apex data)' : 'REGULAR MODE');
    try {
      // Use smart API for teams and karts (auto-redirects to apex when enabled)
      // Always fetch regular rows for pit row functionality
      const [rowsData, teamsData, kartsData, historyData] = await Promise.all([
        apiService.rows.getAll('currentKartId', useApexDatabase, selectedSession?._id), // Smart API for rows too
        apiService.teams.getAll('currentKartId', useApexDatabase, selectedSession?._id), // Smart API
        apiService.karts.getAll('currentTeamId,currentRowId', useApexDatabase, selectedSession?._id), // Smart API
        useApexDatabase ? Promise.resolve([]) : apiService.assignmentHistory.getAll('kartId,teamId,rowId,previousTeamId,previousRowId') // No history in apex mode
      ]);

      setRows(rowsData);
      setTeams(teamsData || []);
      setKarts(kartsData || []);
      setAssignmentHistory(historyData || []);
      console.log('[PitRows] Data fetch successful - rows:', rowsData.length, 'teams:', teamsData?.length || 0, 'karts:', kartsData?.length || 0);
    } catch (err) {
      const message = getErrorMessage(err, "An unknown error occurred");
      console.error("Error fetching data:", message, err);
      setError(`Failed to load data: ${message}`);
    }

    if (showMainLoader) setIsLoading(false);
  }, [useApexDatabase, selectedSession]); // Dependencies updated

  // --- WebSocket Integration ---
  // Define the handler function that will react to messages
  const handleWebSocketMessage = useCallback((message: { event: string; payload?: unknown }) => {
    console.log('[PitRows] Handling WebSocket message:', message); // Keep payload in log for debugging
    const { event, payload } = message; // Keep payload variable for the check below

    // Helper to check payload type for clear event
    const isClearPayload = (p: any): p is { type: 'clear' } =>
      p && typeof p === 'object' && p.type === 'clear';
      
    // Decide whether to refetch based on the event type
    switch (event) {
      case ROW_UPDATE_EVENT:
      case KART_UPDATE_EVENT:
      case TEAM_UPDATE_EVENT: // Assuming team changes might affect row/kart display indirectly
        console.log(`[PitRows] Refetching data due to ${event} event...`);
        fetchData(false); // Refetch data without showing the main loading spinner
        break;

      case ASSIGNMENT_LOG_EVENT:
        if (isClearPayload(payload)) { // Use the type guard
          console.log('[PitRows] Assignment history cleared via WebSocket, clearing local state.');
          setAssignmentHistory([]); // Clear local history state
        } else {
          // If individual logs are pushed, refetching all data covers this too.
          console.log(`[PitRows] Refetching data due to ${event} event (payload: ${JSON.stringify(payload)})...`);
          fetchData(false);
        }
        break;

      // Add cases for other events if needed

      default:
        console.log(`[PitRows] Ignoring WebSocket event: ${event}`);
        break;
    }
  }, [fetchData]); // Dependency: fetchData function

  // --- Initialize WebSocket Connection ---
  // Pass the handler function to the hook
  useWebSocket(handleWebSocketMessage);

  // --- Initial Data Load ---
  useEffect(() => {
    // Add a small delay to prevent UI jank
    const timer = setTimeout(() => {
      fetchData();
    }, 100);
    
    return () => clearTimeout(timer);
  }, [fetchData]); // Run only once on mount

  // --- Handlers ---
  // ... (handleTeamSwapSelect, handlePitKartSelect, handleCardClick, etc. remain the same) ...
  const handleTeamSwapSelect = async (selectedTeam: Team | null) => {
    setIsModalOpen(false); // Close modal immediately

    if (!selectedRow) {
      console.error("Swap cancelled: No target row selected.");
      setToastMessage("Swap failed: Target row not selected.");
      setSelectedRow(null);
      return;
    }
    if (!selectedTeam) {
      console.log("Swap cancelled: No team selected.");
      setSelectedRow(null);
      return;
    }



    console.log(`Attempting swap: Team ${selectedTeam.number} (${selectedTeam._id}) <-> Row ${selectedRow.rowNumber} (${selectedRow._id})`);
    setIsMutating(true);
    setError(null);
    setToastMessage(null);

    try {
      // Use smart swap that works with both regular and apex teams
      const response = await apiService.rows.smartSwapTeamKart(
        selectedRow._id,
        selectedTeam._id,
        useApexDatabase ? selectedSession?._id : undefined
      );
      setToastMessage(response.message || 'Swap successful!');
      // Data will be refetched via WebSocket, no need for manual fetchData(false) here
    } catch (err: unknown) {
      console.error("Swap failed:", err);
      const errorMessage = getErrorMessage(err, 'Swap failed.');
      setError(errorMessage);
      setToastMessage(`Error: ${errorMessage}`);
    } finally {
      setIsMutating(false);
      setSelectedRow(null); // Clear selected row after attempt
    }
  };

  const handlePitKartSelect = async (kart: Kart | null) => { // Expects Kart | null
    // Close modal immediately regardless of selection
    setIsModalOpen(false);

    // Ensure a row and a kart are actually selected
    if (!selectedRow) {
      console.error("Assign cancelled: No target row selected.");
      setSelectedRow(null); // Clear selection just in case
      return;
    }
    if (!kart) {
      console.log("Assign cancelled: No kart selected from modal.");
      setSelectedRow(null);
      return;
    }



    // Prevent assigning a kart to a row that already has a kart (optional check)
    if (selectedRow.currentKartId) {
      console.warn(`Assign cancelled: Row ${selectedRow.rowNumber} already has Kart #${(selectedRow.currentKartId as Kart)?.number} assigned.`);
      setToastMessage(`Row ${selectedRow.rowNumber} is already occupied.`);
      setSelectedRow(null);
      return;
    }

    // Prevent assigning a kart that is already assigned elsewhere unexpectedly
    // Check against the full karts list state
    const kartInState = karts.find(k => k._id === kart._id);
    if (kartInState && (kartInState.currentRowId || kartInState.currentTeamId)) {
      console.warn(`Assign cancelled: Kart #${kart.number} is already assigned (Row: ${kartInState.currentRowId?._id}, Team: ${kartInState.currentTeamId?._id}). Status: ${kartInState.status}`);
      setToastMessage(`Kart #${kart.number} seems already assigned. Refreshing data.`);
      setSelectedRow(null);
      fetchData(false); // Refresh data as state might be stale
      return;
    }


    console.log(`Attempting to assign Kart #${kart.number} (${kart._id}) to Row ${selectedRow.rowNumber} (${selectedRow._id})`);
    setIsMutating(true); // Start loading indicator for mutation
    setError(null);
    setToastMessage(null);

    try {
      const response = await apiService.rows.assignKart(selectedRow._id, kart._id);
      setToastMessage(response.message || `Kart #${kart.number} assigned to Row ${selectedRow.rowNumber}.`);
      // Data will be refetched via WebSocket

    } catch (err: unknown) {
      console.error("Error assigning kart to row:", err);
      const errorMessage = getErrorMessage(err, "Failed to assign kart.");
      setError(errorMessage); // Set error state
      setToastMessage(`Error: ${errorMessage}`); // Show error toast
    } finally {
      setIsMutating(false); // Stop loading indicator
      setSelectedRow(null); // Clear the selected row state
    }
  };

  const handleCardClick = (row: Row) => {
    console.log('Row card clicked:', row);
    setSelectedRow(row);
    setIsModalOpen(true);
  };

  // --- Row Management Handlers ---
  const handleCreateRow = async (newRowData: { color: string }) => {
    console.log('Attempting to create row with data:', newRowData);
    setIsMutating(true);
    setError(null);
    setToastMessage(null);
    try {
      const response = await apiService.rows.create(newRowData);
      console.log('Row created:', response);
      setToastMessage(`Row ${response.rowNumber} created successfully!`);
      // Data will be refetched via WebSocket
    } catch (err: unknown) {
      console.error("Failed to create row:", err);
      const errorMessage = getErrorMessage(err, 'Failed to create row.');
      setError(errorMessage);
      setToastMessage(`Error: ${errorMessage}`);
    } finally {
      setIsMutating(false);
    }
  };

  const handleDeleteRow = async (rowNumber: number) => {
    const rowToDelete = rows.find(r => r.rowNumber === rowNumber);
    if (!rowToDelete) {
      const msg = `Error: Row number ${rowNumber} not found for deletion.`;
      console.error(msg);
      setToastMessage(msg);
      return;
    }

    if (!window.confirm(`Are you sure you want to delete Row ${rowNumber}? This cannot be undone.`)) {
      return;
    }
    console.log(`Attempting to delete row number: ${rowNumber} (ID: ${rowToDelete._id})`);
    setIsMutating(true);
    setError(null);
    setToastMessage(null);
    try {
      const response = await apiService.rows.delete(rowToDelete._id);
      console.log('Row deleted:', response);
      setToastMessage(response.message || 'Row deleted successfully!');
      // Data will be refetched via WebSocket
    } catch (err: unknown) {
      console.error("Failed to delete row:", err);
      const errorMessage = getErrorMessage(err, 'Failed to delete row.');
      setError(errorMessage);
      setToastMessage(`Error: ${errorMessage}`);
    } finally {
      setIsMutating(false);
    }
  };
  
  const handleUpdateRowColor = async (rowNumber: number, newColor: string) => {
    const rowToUpdate = rows.find(r => r.rowNumber === rowNumber);
    if (!rowToUpdate) {
      const msg = `Error: Row number ${rowNumber} not found for color update.`;
      console.error(msg);
      setToastMessage(msg);
      return;
    }
    console.log(`Attempting to update color for row ${rowNumber} (ID: ${rowToUpdate._id}) to ${newColor}`);
    setIsMutating(true);
    setError(null);
    setToastMessage(null);
    try {
      const response = await apiService.rows.update(rowToUpdate.rowNumber, { color: newColor }); // Pass rowNumber instead of _id
      setToastMessage(`Row ${response.rowNumber} color updated successfully!`);
      // Data will be refetched via WebSocket
    } catch (err: unknown) {
      console.error(`Failed to update color for row ${rowNumber}:`, err);
      const errorMessage = getErrorMessage(err, 'Failed to update color.');
      setError(errorMessage);
      setToastMessage(`Error: ${errorMessage}`);
    } finally {
      setIsMutating(false);
    }
  };

  // --- Handler for Moving Kart to Maintenance ---
  const handleMoveToMaintenance = async (kartId: string) => {
    console.log(`Attempting to move Kart ${kartId} to Maintenance`);
    setIsMutating(true);
    setError(null);
    setToastMessage(null);
    try {
      // API call to update kart status
      const response = await apiService.admin.moveKartToMaintenance(kartId); // Use admin API
      setToastMessage(response.message || `Kart moved to maintenance.`);
      // Data will refetch via WebSocket
    } catch (err: unknown) {
      console.error(`Failed to move kart ${kartId} to maintenance:`, err);
      const errorMessage = getErrorMessage(err, 'Failed to update kart status.');
      setError(errorMessage);
      setToastMessage(`Error: ${errorMessage}`);
    } finally {
      setIsMutating(false);
      setSelectedRow(null); // Clear selection
    }
  };

  // --- Handler for Emptying a Row (Unassigning Kart) ---
  const handleEmptyRow = async (kartId: string) => {
    // Ensure a row is actually selected before proceeding
    if (!selectedRow) {
        console.error("Cannot empty row: No row selected.");
        setToastMessage("Error: No row selected to empty.");
        return;
    }
    console.log(`Attempting to empty row by unassigning Kart ${kartId}`);
    setIsMutating(true);
    setError(null);
    setToastMessage(null);
    try {
      const response = await apiService.rows.unassignKart(selectedRow._id); // Use selectedRow._id
      setToastMessage(response.message || `Kart unassigned from row.`);
      // Data will refetch via WebSocket
    } catch (err: unknown) {
      console.error(`Failed to unassign kart ${kartId} from row:`, err);
      const errorMessage = getErrorMessage(err, 'Failed to unassign kart.');
      setError(errorMessage);
      setToastMessage(`Error: ${errorMessage}`);
    } finally {
      setIsMutating(false);
      setSelectedRow(null); // Clear selection
    }
  };

  // --- Handler for Clearing Assignment History ---
  const handleClearHistory = async () => {
    console.log('Attempting to clear assignment history...');
    setIsClearingHistory(true);
    setError(null);
    setToastMessage(null);
    try {
      // Using reset-assignments as it clears history and resets state
      const response = await apiService.admin.resetAssignments();
      console.log('Assignments reset:', response);
      setToastMessage(response.message || 'Assignments reset successfully!');
      // Data will be refetched via WebSocket due to multiple updates
      // setAssignmentHistory([]); // No need to manually clear, WS will trigger refetch
    } catch (err: unknown) {
      console.error("Failed to clear assignment history:", err);
      const errorMessage = getErrorMessage(err, 'Failed to clear history.');
      setError(errorMessage);
      setToastMessage(`Error: ${errorMessage}`);
    } finally {
      setIsClearingHistory(false);
    }
  };


  // --- Render ---
  return (
    <IonPage>
      <IonHeader>
        <IonToolbar>
          <IonButtons slot="start">
            <IonMenuButton></IonMenuButton>
          </IonButtons>
          <IonTitle>Pit Rows</IonTitle>
          <IonButtons slot="end">
            <IonButton
              onClick={() => setShowSettings(true)}
              disabled={isLoading || isMutating || isClearingHistory}
              title="Pit Row Settings"
            >
              <IonIcon slot="icon-only" icon={settingsOutline} />
            </IonButton>
          </IonButtons>
        </IonToolbar>
      </IonHeader>

      <IonContent className="ion-padding">

        <IonLoading
          isOpen={isLoading || isMutating || isClearingHistory}
          message={isLoading ? 'Loading Data...' : (isClearingHistory ? 'Clearing History...' : 'Updating...')}
        />
        <IonToast
          isOpen={!!toastMessage}
          message={toastMessage || ""}
          onDidDismiss={() => setToastMessage(null)}
          duration={3000}
          color={toastMessage?.toLowerCase().includes('error') ? 'danger' : 'success'}
        />
        {error && !isLoading && !isMutating && !isClearingHistory && (
          <div style={{ textAlign: 'center', color: 'var(--ion-color-danger)', margin: '1rem' }}>
            <p>{error}</p>
            <IonButton onClick={() => fetchData()}>Retry</IonButton> {/* fetchData() will use default true for showMainLoader */}
          </div>
        )}
        {!isLoading && (
          <>
            {/* Pass rows with populated kart info */}
            <RowCard
              rows={rows}
              handleCardClick={handleCardClick} // Keep pit row functionality in mixed mode
            />
            {/*<NextPits data={next_Pits} /> *}
            {/* Pass history with populated info */}
            <AssignmentHistory
              historyData={assignmentHistory}
              onClearHistory={handleClearHistory}
              isClearingHistory={isClearingHistory}
            />

            <KartRowSettings
              isOpen={showSettings}
              onClose={() => setShowSettings(false)}
              rows={rows}
              onCreateRow={handleCreateRow}
              onDeleteRow={handleDeleteRow}
              onUpdateRowColor={handleUpdateRowColor}
              isMutating={isMutating || isClearingHistory}
            />
            <PitLaneModal
              isOpen={isModalOpen}
              onClose={() => {
                setIsModalOpen(false);
                setSelectedRow(null);
              }}
              teams={teams} // Pass teams with populated kart info
              // Filter the full karts list based on status
              pitLaneKarts={karts.filter(k => k.status === 'in_pit_row')}
              maintenanceKarts={karts.filter(k => k.status === 'maintenance')}
              availableKarts={karts.filter(k => k.status === 'available')}
              onTeamSelect={handleTeamSwapSelect}
              onPitKartSelect={handlePitKartSelect}
              // Pass new props
              isTargetRowOccupied={!!selectedRow?.currentKartId}
              targetKartId={typeof selectedRow?.currentKartId === 'object' ? selectedRow.currentKartId?._id : null}
              onMoveToMaintenance={handleMoveToMaintenance}
              onEmptyRow={handleEmptyRow}
              targetRowNumber={selectedRow?.rowNumber}
            />
          </>
        )}
      </IonContent>
    </IonPage>
  );
};

export default PitRows;
