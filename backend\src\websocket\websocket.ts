import WebSocket, { WebSocketServer, ServerOptions } from 'ws';
import http from 'http'; // Import http
import url from 'url'; // Import url
import LogReplayService from '../services/logReplayService';
import { getRealtimeReceiver, initializeRealtimeReceiver } from '../services/apexRealtimeReceiver';

let wssInstance: WebSocketServer | null = null;
let replayService: LogReplayService | null = null;

// Allowed origins for WebSocket connections
const allowedWsOrigins = [
    'https://raceplanner-frontend.onrender.com', // Your deployed frontend
    'http://localhost:3000' // Your local frontend dev server
];

// Function to set up the WebSocket server on an existing HTTP server
export const setupWebSocketServer = (server: http.Server) => {
    const options: ServerOptions = {
        server, // Attach to the existing HTTP server
        // path: "/ws", // Optional: Uncomment if you want ws on /ws path instead of root '/'
        verifyClient: (info, cb) => { // Optional but recommended: Origin check
            const origin = info.origin;
            console.log(`[WebSocket] Verifying client origin: ${origin}`);
            if (origin && allowedWsOrigins.includes(origin)) {
                cb(true); // Allow connection
            } else if (!origin) {
                 // Allow connections with no origin header (e.g., direct connections, older clients, some tools)
                 // Consider if this is acceptable for your security requirements.
                 console.log("[WebSocket] Allowing connection with no origin header.");
                 cb(true);
            }
            else {
                console.log(`[WebSocket] Blocking connection from origin: ${origin}`);
                cb(false, 403, 'Forbidden origin'); // Reject connection
            }
        }
     };

    wssInstance = new WebSocketServer(options);

    wssInstance.on('connection', (ws, req) => {
        // const clientIp = req.socket.remoteAddress; // Get client IP if needed
        console.log(`🔗 Client connected`); // Removed IP for privacy/simplicity

        ws.on('close', (code, reason) => {
            console.log(`❌ Client disconnected. Code: ${code}, Reason: ${reason.toString()}`);
        });

        ws.on('error', (error) => {
            console.error('[WebSocket] Error on client connection:', error);
        });

        // Handle incoming messages from client for replay control
        ws.on('message', (message) => {
            try {
                const data = JSON.parse(message.toString());
                handleReplayCommand(data, ws);
            } catch (error) {
                console.error('Error parsing client message:', error);
                ws.send(JSON.stringify({ error: 'Invalid message format' }));
            }
        });
    });

    wssInstance.on('error', (error) => {
        console.error('[WebSocket] Server Error:', error);
    });

    console.log("🚀 WebSocket Server attached to HTTP server");

    return wssInstance; // Return instance if needed elsewhere, though unlikely
};

// Function to send messages to all connected and open clients
export const sendWebSocketMessage = (message: object) => {
    if (!wssInstance) {
        console.error("[WebSocket] Attempted to send message, but server instance is not available.");
        return;
    }
    // Avoid logging potentially large message objects by default in production
    // console.log(`[WebSocket] Broadcasting to ${wssInstance.clients.size} clients. Message:`, message);
    const messageString = JSON.stringify(message);

    wssInstance.clients.forEach((client) => {
        if (client.readyState === WebSocket.OPEN) {
            client.send(messageString, (err) => {
                if (err) {
                    console.error("[WebSocket] Error sending message to a client:", err);
                }
            });
        }
    });
};

// Initialize replay service
export const initializeReplayService = () => {
    if (replayService) {
        return replayService;
    }

    replayService = new LogReplayService({
        speed: 1.0,
        autoStart: false,
        loopMode: false,
        skipTimestamps: true,
        realTimeMode: true
    });

    // Set up event listeners for replay service
    replayService.on('message', (data) => {
        // Send the enhanced message data to all connected clients
        sendWebSocketMessage({
            type: 'apex_data',
            content: data.content,
            originalMessage: data.originalMessage,
            parsedMessage: data.parsedMessage,
            lineNumber: data.lineNumber,
            timestamp: data.timestamp || new Date().toISOString()
        });

        // Process the message with the realtime receiver
        const receiver = getRealtimeReceiver();
        if (receiver) {
            // Ensure receiver is active
            if (!receiver.isReceiving()) {
                receiver.start();
            }
            receiver.processMessage(data.content).catch(error => {
                console.error('Error processing message in realtime receiver:', error);
            });
        } else {
            console.warn('No realtime receiver available to process message');
        }
    });

    replayService.on('replayStarted', (status) => {
        broadcastReplayStatus('started', status);
    });

    replayService.on('replayPaused', (status) => {
        broadcastReplayStatus('paused', status);
    });

    replayService.on('replayResumed', (status) => {
        broadcastReplayStatus('resumed', status);
    });

    replayService.on('replayStopped', (status) => {
        broadcastReplayStatus('stopped', status);
    });

    replayService.on('replayCompleted', (status) => {
        broadcastReplayStatus('completed', status);
    });

    replayService.on('replayProgress', (status) => {
        // Send progress updates every 10 lines to avoid spam
        if (status.currentLine % 10 === 0) {
            broadcastReplayStatus('progress', status);
        }
    });

    replayService.on('error', (error) => {
        sendWebSocketMessage({
            type: 'replay_error',
            error: error.error.message,
            line: error.line,
            lineNumber: error.lineNumber
        });
    });

    console.log('🎬 Log Replay Service initialized');
    return replayService;
};

// Handle replay commands from clients
const handleReplayCommand = (data: any, ws: WebSocket) => {
    if (!replayService) {
        ws.send(JSON.stringify({ error: 'Replay service not initialized' }));
        return;
    }

    try {
        switch (data.command) {
            case 'load_log':
                if (data.logContent) {
                    replayService.loadLog(data.logContent);
                    ws.send(JSON.stringify({
                        type: 'log_loaded',
                        totalLines: replayService.getTotalLines()
                    }));
                } else {
                    ws.send(JSON.stringify({ error: 'No log content provided' }));
                }
                break;

            case 'start':
                replayService.start();
                break;

            case 'pause':
                replayService.pause();
                break;

            case 'resume':
                replayService.resume();
                break;

            case 'stop':
                replayService.stop();
                break;

            case 'reset':
                replayService.reset();
                break;

            case 'set_speed':
                if (typeof data.speed === 'number' && data.speed > 0) {
                    replayService.setSpeed(data.speed);
                    ws.send(JSON.stringify({
                        type: 'speed_changed',
                        speed: data.speed
                    }));
                } else {
                    ws.send(JSON.stringify({ error: 'Invalid speed value' }));
                }
                break;

            case 'seek':
                if (typeof data.lineNumber === 'number') {
                    replayService.seekToLine(data.lineNumber);
                } else {
                    ws.send(JSON.stringify({ error: 'Invalid line number' }));
                }
                break;

            case 'get_status':
                ws.send(JSON.stringify({
                    type: 'replay_status',
                    status: replayService.getStatus()
                }));
                break;

            default:
                ws.send(JSON.stringify({ error: `Unknown command: ${data.command}` }));
        }
    } catch (error) {
        console.error('Error handling replay command:', error);
        ws.send(JSON.stringify({
            error: error instanceof Error ? error.message : 'Unknown error'
        }));
    }
};

// Broadcast replay status to all clients
const broadcastReplayStatus = (event: string, status: any) => {
    sendWebSocketMessage({
        type: 'replay_status',
        event,
        status
    });
};

// Get replay service instance
export const getReplayService = (): LogReplayService | null => {
    return replayService;
};

// Initialize realtime receiver
export const initializeApexReceiver = () => {
    const receiver = initializeRealtimeReceiver({
        enableLogging: true,
        autoCreateSession: true,
        broadcastUpdates: true
    });

    console.log('🎯 Apex Realtime Receiver initialized');
    return receiver;
};
