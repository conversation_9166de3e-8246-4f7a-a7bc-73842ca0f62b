#!/usr/bin/env node

/**
 * <PERSON>ript to clean up old indexes that conflict with the optimized schema
 * This removes indexes that reference fields that no longer exist
 */

const mongoose = require('mongoose');

async function cleanupOldIndexes() {
  console.log('🧹 Cleaning up old indexes for optimized schema...');
  
  try {
    // Connect to database
    const mongoUri = process.env.MONGODB_URI || 'mongodb://localhost:27017/race-planner';
    await mongoose.connect(mongoUri);
    console.log('✅ Connected to database');
    
    const db = mongoose.connection.db;
    
    // Clean up apex_teams collection indexes
    console.log('\n📝 Cleaning up apex_teams indexes...');
    try {
      const teamIndexes = await db.collection('apex_teams').indexes();
      console.log('Current team indexes:', teamIndexes.map(idx => idx.name));
      
      // Drop the problematic teamId index
      const teamIdIndexes = teamIndexes.filter(idx => 
        idx.key && (idx.key.teamId !== undefined || JSON.stringify(idx.key).includes('teamId'))
      );
      
      for (const index of teamIdIndexes) {
        console.log(`🗑️ Dropping team index: ${index.name}`);
        await db.collection('apex_teams').dropIndex(index.name);
      }
      
      // Also try to drop by key pattern
      try {
        await db.collection('apex_teams').dropIndex({ sessionId: 1, teamId: 1 });
        console.log('🗑️ Dropped sessionId_1_teamId_1 index');
      } catch (e) {
        console.log('ℹ️ sessionId_1_teamId_1 index not found (already dropped)');
      }
      
    } catch (error) {
      console.log('⚠️ Error cleaning team indexes:', error.message);
    }
    
    // Clean up apex_karts collection indexes
    console.log('\n📝 Cleaning up apex_karts indexes...');
    try {
      const kartIndexes = await db.collection('apex_karts').indexes();
      console.log('Current kart indexes:', kartIndexes.map(idx => idx.name));
      
      // Drop indexes that reference old teamId field
      const kartTeamIdIndexes = kartIndexes.filter(idx => 
        idx.key && (idx.key.teamId !== undefined || JSON.stringify(idx.key).includes('teamId'))
      );
      
      for (const index of kartTeamIdIndexes) {
        console.log(`🗑️ Dropping kart index: ${index.name}`);
        await db.collection('apex_karts').dropIndex(index.name);
      }
      
    } catch (error) {
      console.log('⚠️ Error cleaning kart indexes:', error.message);
    }
    
    // Clean up apex_laps collection indexes
    console.log('\n📝 Cleaning up apex_laps indexes...');
    try {
      const lapIndexes = await db.collection('apex_laps').indexes();
      console.log('Current lap indexes:', lapIndexes.map(idx => idx.name));
      
      // Drop indexes that reference old teamId field
      const lapTeamIdIndexes = lapIndexes.filter(idx => 
        idx.key && (idx.key.teamId !== undefined || JSON.stringify(idx.key).includes('teamId'))
      );
      
      for (const index of lapTeamIdIndexes) {
        console.log(`🗑️ Dropping lap index: ${index.name}`);
        await db.collection('apex_laps').dropIndex(index.name);
      }
      
    } catch (error) {
      console.log('⚠️ Error cleaning lap indexes:', error.message);
    }
    
    // Clean up apex_pitstops collection indexes
    console.log('\n📝 Cleaning up apex_pitstops indexes...');
    try {
      const pitStopIndexes = await db.collection('apex_pitstops').indexes();
      console.log('Current pit stop indexes:', pitStopIndexes.map(idx => idx.name));
      
      // Drop indexes that reference old teamId field
      const pitStopTeamIdIndexes = pitStopIndexes.filter(idx => 
        idx.key && (idx.key.teamId !== undefined || JSON.stringify(idx.key).includes('teamId'))
      );
      
      for (const index of pitStopTeamIdIndexes) {
        console.log(`🗑️ Dropping pit stop index: ${index.name}`);
        await db.collection('apex_pitstops').dropIndex(index.name);
      }
      
    } catch (error) {
      console.log('⚠️ Error cleaning pit stop indexes:', error.message);
    }
    
    console.log('\n🎉 Index cleanup completed!');
    console.log('ℹ️ New optimized indexes will be created when the server starts');
    
  } catch (error) {
    console.error('❌ Index cleanup failed:', error);
  } finally {
    await mongoose.disconnect();
    console.log('👋 Disconnected from database');
  }
}

// Run the cleanup
if (require.main === module) {
  cleanupOldIndexes().catch(console.error);
}

module.exports = { cleanupOldIndexes };
