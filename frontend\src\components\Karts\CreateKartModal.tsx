// src/components/Karts/CreateKartModal.tsx
import React, { useState } from 'react';
import {
    IonModal,
    IonHeader,
    IonToolbar,
    IonTitle,
    IonButtons,
    IonButton,
    IonIcon,
    IonContent,
    IonList,
    IonItem,
    IonLabel,
    IonInput,
} from '@ionic/react';
import { close } from 'ionicons/icons';

interface CreateKartModalProps {
    isOpen: boolean;
    onDidDismiss: () => void;
    onCreateKart: (kartNumber: number) => Promise<boolean>; // Returns true on success
    isMutating: boolean;
}

export const CreateKartModal: React.FC<CreateKartModalProps> = ({ isOpen, onDidDismiss, onCreateKart, isMutating }) => {
    const [newKartNumber, setNewKartNumber] = useState<string>('');

    const handleInternalCreate = async () => {
        const kartNum = parseInt(newKartNumber, 10);
        if (isNaN(kartNum) || kartNum <= 0) {
            // Rely on parent toast for validation message
            return;
        }
        const success = await onCreateKart(kartNum);
        if (success) {
            setNewKartNumber(''); // Reset on success
            onDidDismiss();
        }
    };

    const handleDismiss = () => {
        setNewKartNumber(''); // Reset on dismiss
        onDidDismiss();
    };

    return (
        <IonModal isOpen={isOpen} onDidDismiss={handleDismiss}>
            <IonHeader>
                <IonToolbar>
                    <IonTitle>Create New Kart</IonTitle>
                    <IonButtons slot="end">
                        <IonButton onClick={handleDismiss} disabled={isMutating} fill="clear">
                            <IonIcon slot="icon-only" icon={close} />
                        </IonButton>
                    </IonButtons>
                </IonToolbar>
            </IonHeader>
            <IonContent className="ion-padding">
                <IonList lines="none">
                    <IonItem>
                        <IonLabel position="stacked">Kart Number *</IonLabel>
                        <IonInput type="number" value={newKartNumber} onIonChange={e => setNewKartNumber(e.detail.value!)} placeholder="Enter unique number (e.g., 1, 23)" required min="1" disabled={isMutating} clearInput />
                    </IonItem>
                </IonList>
                <IonButton expand="block" onClick={handleInternalCreate} disabled={isMutating || !newKartNumber || parseInt(newKartNumber, 10) <= 0} style={{ marginTop: '1rem' }}>
                    {isMutating ? 'Creating...' : 'Create Kart'}
                </IonButton>
            </IonContent>
        </IonModal>
    );
};