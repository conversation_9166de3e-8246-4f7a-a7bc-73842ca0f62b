import { Schema, model, Types } from "mongoose";
import { Document } from 'mongoose'; // Import Document

// Interface extending Document
export interface IDriver extends Document {
  // _id: string; // Changed
  _id: Types.ObjectId; // Use ObjectId type
  name: string;
  // teamId?: Types.ObjectId | null; // Optional: See comment above
  createdAt: Date;
  updatedAt: Date;
}

// Schema remains the same
const driverSchema = new Schema<IDriver>({
  name: {
    type: String,
    required: [true, "Driver name is required"],
    trim: true,
  },
}, {
  timestamps: true
});

export const Driver = model<IDriver>("Driver", driverSchema);
