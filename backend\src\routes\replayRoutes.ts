import express, { Request, Response } from 'express';
import multer from 'multer';
import fs from 'fs';
import path from 'path';
import { initializeReplayService, getReplayService } from '../websocket/websocket';

const router = express.Router();

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadDir = path.join(__dirname, '../../uploads');
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    cb(null, uploadDir);
  },
  filename: (req, file, cb) => {
    const timestamp = Date.now();
    const originalName = file.originalname.replace(/[^a-zA-Z0-9.-]/g, '_');
    cb(null, `${timestamp}_${originalName}`);
  }
});

const upload = multer({ 
  storage,
  limits: {
    fileSize: 50 * 1024 * 1024 // 50MB limit
  },
  fileFilter: (req, file, cb) => {
    // Accept text files and log files
    if (file.mimetype === 'text/plain' || 
        file.originalname.endsWith('.txt') || 
        file.originalname.endsWith('.log')) {
      cb(null, true);
    } else {
      cb(new Error('Only text files (.txt, .log) are allowed'));
    }
  }
});

// Initialize replay service
router.use((req, res, next) => {
  initializeReplayService();
  next();
});

// Upload log file from desktop
router.post('/upload-log', upload.single('logFile'), async (req: Request, res: Response) => {
  try {
    if (!req.file) {
      res.status(400).json({ error: 'No file uploaded' });
      return;
      return;
    }

    const filePath = req.file.path;
    const logContent = fs.readFileSync(filePath, 'utf8');
    
    // Clean up uploaded file after reading
    fs.unlinkSync(filePath);

    const replayService = getReplayService();
    if (!replayService) {
      res.status(500).json({ error: 'Replay service not available' });
      return;
    }

    // Load the log content into replay service
    replayService.loadLog(logContent);

    res.json({
      success: true,
      message: 'Log file uploaded and loaded successfully',
      filename: req.file.originalname,
      totalLines: replayService.getTotalLines(),
      status: replayService.getStatus()
    });

  } catch (error) {
    console.error('Error uploading log file:', error);
    res.status(500).json({ 
      error: error instanceof Error ? error.message : 'Failed to upload log file' 
    });
  }
});

// Load log content directly
router.post('/load-log', async (req: Request, res: Response) => {
  try {
    const { logContent } = req.body;

    if (!logContent || typeof logContent !== 'string') {
      res.status(400).json({ error: 'Log content is required' });
      return;
    }

    const replayService = getReplayService();
    if (!replayService) {
      res.status(500).json({ error: 'Replay service not available' });
      return;
    }

    replayService.loadLog(logContent);

    res.json({
      success: true,
      message: 'Log content loaded successfully',
      totalLines: replayService.getTotalLines(),
      status: replayService.getStatus()
    });

  } catch (error) {
    console.error('Error loading log content:', error);
    res.status(500).json({ 
      error: error instanceof Error ? error.message : 'Failed to load log content' 
    });
  }
});

// Start replay
router.post('/start', async (req: Request, res: Response) => {
  try {
    const replayService = getReplayService();
    if (!replayService) {
      res.status(500).json({ error: 'Replay service not available' });
      return;
    }

    replayService.start();

    res.json({
      success: true,
      message: 'Replay started',
      status: replayService.getStatus()
    });

  } catch (error) {
    console.error('Error starting replay:', error);
    res.status(500).json({ 
      error: error instanceof Error ? error.message : 'Failed to start replay' 
    });
  }
});

// Pause replay
router.post('/pause', async (req: Request, res: Response) => {
  try {
    const replayService = getReplayService();
    if (!replayService) {
      res.status(500).json({ error: 'Replay service not available' });
      return;
    }

    replayService.pause();

    res.json({
      success: true,
      message: 'Replay paused',
      status: replayService.getStatus()
    });

  } catch (error) {
    console.error('Error pausing replay:', error);
    res.status(500).json({ 
      error: error instanceof Error ? error.message : 'Failed to pause replay' 
    });
  }
});

// Resume replay
router.post('/resume', async (req: Request, res: Response) => {
  try {
    const replayService = getReplayService();
    if (!replayService) {
      res.status(500).json({ error: 'Replay service not available' });
      return;
    }

    replayService.resume();

    res.json({
      success: true,
      message: 'Replay resumed',
      status: replayService.getStatus()
    });

  } catch (error) {
    console.error('Error resuming replay:', error);
    res.status(500).json({ 
      error: error instanceof Error ? error.message : 'Failed to resume replay' 
    });
  }
});

// Stop replay
router.post('/stop', async (req: Request, res: Response) => {
  try {
    const replayService = getReplayService();
    if (!replayService) {
      res.status(500).json({ error: 'Replay service not available' });
      return;
    }

    replayService.stop();

    res.json({
      success: true,
      message: 'Replay stopped',
      status: replayService.getStatus()
    });

  } catch (error) {
    console.error('Error stopping replay:', error);
    res.status(500).json({ 
      error: error instanceof Error ? error.message : 'Failed to stop replay' 
    });
  }
});

// Reset replay
router.post('/reset', async (req: Request, res: Response) => {
  try {
    const replayService = getReplayService();
    if (!replayService) {
      res.status(500).json({ error: 'Replay service not available' });
      return;
    }

    replayService.reset();

    res.json({
      success: true,
      message: 'Replay reset',
      status: replayService.getStatus()
    });

  } catch (error) {
    console.error('Error resetting replay:', error);
    res.status(500).json({ 
      error: error instanceof Error ? error.message : 'Failed to reset replay' 
    });
  }
});

// Set replay speed
router.post('/speed', async (req: Request, res: Response) => {
  try {
    const { speed } = req.body;

    if (typeof speed !== 'number' || speed <= 0) {
      res.status(400).json({ error: 'Valid speed value is required' });
      return;
    }

    const replayService = getReplayService();
    if (!replayService) {
      res.status(500).json({ error: 'Replay service not available' });
      return;
    }

    replayService.setSpeed(speed);

    res.json({
      success: true,
      message: `Replay speed set to ${speed}x`,
      speed,
      status: replayService.getStatus()
    });

  } catch (error) {
    console.error('Error setting replay speed:', error);
    res.status(500).json({ 
      error: error instanceof Error ? error.message : 'Failed to set replay speed' 
    });
  }
});

// Seek to specific line
router.post('/seek', async (req: Request, res: Response) => {
  try {
    const { lineNumber } = req.body;

    if (typeof lineNumber !== 'number' || lineNumber < 0) {
      res.status(400).json({ error: 'Valid line number is required' });
      return;
    }

    const replayService = getReplayService();
    if (!replayService) {
      res.status(500).json({ error: 'Replay service not available' });
      return;
    }

    replayService.seekToLine(lineNumber);

    res.json({
      success: true,
      message: `Seeked to line ${lineNumber}`,
      lineNumber,
      status: replayService.getStatus()
    });

  } catch (error) {
    console.error('Error seeking replay:', error);
    res.status(500).json({ 
      error: error instanceof Error ? error.message : 'Failed to seek replay' 
    });
  }
});

// Get replay status
router.get('/status', async (req: Request, res: Response) => {
  try {
    const replayService = getReplayService();
    if (!replayService) {
      res.status(500).json({ error: 'Replay service not available' });
      return;
    }

    res.json({
      success: true,
      status: replayService.getStatus(),
      config: replayService.getConfig()
    });

  } catch (error) {
    console.error('Error getting replay status:', error);
    res.status(500).json({ 
      error: error instanceof Error ? error.message : 'Failed to get replay status' 
    });
  }
});

// Update replay configuration
router.post('/config', async (req: Request, res: Response) => {
  try {
    const { config } = req.body;

    if (!config || typeof config !== 'object') {
      res.status(400).json({ error: 'Valid configuration object is required' });
      return;
    }

    const replayService = getReplayService();
    if (!replayService) {
      res.status(500).json({ error: 'Replay service not available' });
      return;
    }

    replayService.updateConfig(config);

    res.json({
      success: true,
      message: 'Configuration updated',
      config: replayService.getConfig()
    });

  } catch (error) {
    console.error('Error updating replay config:', error);
    res.status(500).json({ 
      error: error instanceof Error ? error.message : 'Failed to update configuration' 
    });
  }
});

export default router;
