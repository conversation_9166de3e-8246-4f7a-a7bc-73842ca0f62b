import React from 'react';
import { IonIcon, IonText, IonButton } from '@ionic/react';
import { closeCircleOutline } from 'ionicons/icons';

interface ErrorDisplayProps {
  error: string;
  onRetry: () => void;
}

const ErrorDisplay: React.FC<ErrorDisplayProps> = ({ error, onRetry }) => {
  return (
    <div className="ion-padding ion-text-center error-message-container">
      <IonIcon icon={closeCircleOutline} size="large" color="danger" />
      <IonText color="danger">
        <h5>Error Loading Strategy</h5>
        <p>{error || 'An unknown error occurred.'}</p>
      </IonText>
      <IonButton onClick={onRetry} fill="outline">Retry</IonButton>
    </div>
  );
};

export default ErrorDisplay;