import React, { useState, useEffect } from 'react';
import { IonText } from '@ionic/react';
import { displaySecondsAsHHMMSS } from '../utils/timeFormatters';

interface CountdownTimerProps {
  targetTimeSeconds: number;
  currentTimeSeconds: number;
  onComplete?: () => void;
  color?: string;
  className?: string;
}

/**
 * Component for displaying a countdown timer
 */
const CountdownTimer: React.FC<CountdownTimerProps> = ({
  targetTimeSeconds,
  currentTimeSeconds,
  onComplete,
  color = 'primary',
  className = ''
}) => {
  const [remainingSeconds, setRemainingSeconds] = useState<number>(
    Math.max(0, targetTimeSeconds - currentTimeSeconds)
  );
  
  useEffect(() => {
    // Update remaining time
    const remaining = Math.max(0, targetTimeSeconds - currentTimeSeconds);
    setRemainingSeconds(remaining);
    
    // Call onComplete if countdown reaches zero
    if (remaining === 0 && onComplete) {
      onComplete();
    }
  }, [targetTimeSeconds, currentTimeSeconds, onComplete]);
  
  // Determine color based on remaining time
  const getDisplayColor = (): string => {
    if (remainingSeconds === 0) return 'danger';
    if (remainingSeconds < 60) return 'warning'; // Less than 1 minute
    return color;
  };
  
  return (
    <IonText color={getDisplayColor()} className={className}>
      {displaySecondsAsHHMMSS(remainingSeconds)}
    </IonText>
  );
};

export default CountdownTimer;