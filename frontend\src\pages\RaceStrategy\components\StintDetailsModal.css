.stint-header {
  padding: 0;
  box-shadow: none;
}

.time-display-hint {
  color: var(--ion-color-medium);
  font-size: 14px;
  margin: 4px 0 12px;
}

.time-inputs-container {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.time-inputs {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
}

.time-separator {
  font-size: 20px;
  font-weight: bold;
  margin: 0 8px;
}

.time-input {
  text-align: center;
  --padding-start: 0;
  --padding-end: 0;
  max-width: 60px;
  border: 1px solid var(--ion-color-medium);
  border-radius: 8px;
  margin: 0 4px;
  font-size: 18px;
  font-weight: 500;
}

.time-input-ms {
  max-width: 70px;
}

.time-input-labels {
  display: flex;
  justify-content: space-around;
  width: 100%;
  font-size: 12px;
  color: var(--ion-color-medium);
  margin-top: 4px;
  padding: 0 8px;
}

.save-button {
  margin-bottom: 8px;
  width: 100%;
}

.cancel-button {
  width: 100%;
}

.edit-card {
  background: var(--ion-card-background);
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.edit-card h2 {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--ion-text-color);
}

.planned-value {
  color: var(--ion-color-medium);
  font-size: 14px;
  margin: 0 0 16px 0;
}

.time-input-group {
  display: flex;
  align-items: flex-end;
  justify-content: center;
}

.time-input-container {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.time-input {
  --background: var(--ion-background-color);
  --color: var(--ion-text-color);
  --padding-start: 8px;
  --padding-end: 8px;
  --padding-top: 8px;
  --padding-bottom: 8px;
  border: 1px solid var(--ion-color-step-300);
  border-radius: 8px;
  text-align: center;
  font-size: 18px;
  width: 60px;
  margin: 0;
}

.ms-input {
  width: 70px;
}

.time-input-container label {
  font-size: 12px;
  color: var(--ion-color-medium);
  margin-top: 4px;
}

.separator {
  font-size: 24px;
  font-weight: 500;
  margin: 0 4px;
  align-self: center;
  padding-bottom: 12px;
}

.button-group {
  margin-top: 24px;
}

.button-group ion-button {
  margin-bottom: 8px;
}

.stint-header-toolbar {
  --background: transparent;
  --border-color: transparent;
  padding: 16px 16px 0;
}

.stint-title {
  font-size: 24px;
  font-weight: 700;
  color: #333;
}

.stint-badge {
  margin-left: 8px;
  font-size: 12px;
  font-weight: 600;
  padding: 4px 8px;
  border-radius: 4px;
}

.stint-main {
  padding: 0 16px 16px;
}

.stint-card {
  margin: 16px 0;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.stint-card-header {
  padding: 16px;
  background-color: #fff;
  border-bottom: 1px solid #eee;
}

.stint-card-title {
  font-size: 16px;
  font-weight: 600;
  color: #555;
  margin: 0;
}

.stint-card-content {
  padding: 16px;
  background-color: #fff;
}

/* Time display section */
.time-display {
  display: flex;
  justify-content: space-between;
  margin-bottom: 24px;
  padding: 16px;
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.time-display-item {
  text-align: center;
}

.time-display-label {
  font-size: 14px;
  color: #888;
  margin-bottom: 8px;
}

.time-display-value {
  font-size: 20px;
  font-weight: 700;
  color: #333;
}

/* Stats display */
.stats-display {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 12px;
  margin-bottom: 24px;
}

.stat-item {
  padding: 16px;
  background-color: #fff;
  border-radius: 12px;
  text-align: center;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.stat-label {
  font-size: 12px;
  color: #888;
  margin-bottom: 8px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.stat-value {
  font-size: 18px;
  font-weight: 700;
  color: #333;
}

/* Custom styles for the edit alert */
.edit-stint-alert .alert-wrapper {
  min-width: 320px;
}

.edit-stint-alert .alert-message {
  max-height: none;
  overflow: visible;
}

/* Time input container */
.time-input-container {
  padding: 8px 0;
}

.time-input-label {
  font-size: 14px;
  font-weight: 600;
  color: #555;
  margin-bottom: 4px;
}

.time-input-hint {
  font-size: 12px;
  color: #888;
  margin-bottom: 8px;
}

.time-input-fields {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16px;
}

.time-input-field {
  width: 40px;
  height: 40px;
  border: 1px solid #ddd;
  border-radius: 8px;
  text-align: center;
  font-size: 16px;
  font-weight: 600;
  padding: 0;
  margin: 0 2px;
}

.time-input-field-ms {
  width: 50px; /* Wider for milliseconds */
}

.time-input-separator {
  font-size: 18px;
  font-weight: 600;
  margin: 0 4px;
}

/* Dark mode */
@media (prefers-color-scheme: dark) {
  .stint-modal-content {
    --background: #121212;
  }

  .stint-title {
    color: #f8f9fa;
  }

  .time-display,
  .stat-item,
  .stint-card,
  .stint-card-header,
  .stint-card-content {
    background-color: #1e1e1e;
  }

  .stint-card-header {
    border-bottom-color: #333;
  }

  .stint-card-title {
    color: #ccc;
  }

  .time-display-value,
  .stat-value {
    color: #f8f9fa;
  }

  .time-display-label,
  .stat-label {
    color: #aaa;
  }

  .time-input-label {
    color: #ddd;
  }

  .time-input-hint {
    color: #aaa;
  }

  .time-input-field {
    border-color: #444;
    background-color: #2a2a2a;
    color: #f8f9fa;
  }
}

/* Edit Stint Modal */
.edit-stint-modal {
  --border-radius: 16px;
}

/* Time input fields styling */
.time-input-fields {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 16px 0 8px;
}

.time-input-field {
  width: 48px;
  height: 48px;
  border: 1px solid #ddd;
  border-radius: 8px;
  text-align: center;
  font-size: 18px;
  font-weight: 600;
  padding: 0;
  margin: 0 2px;
}

.time-input-field-ms {
  width: 60px; /* Wider for milliseconds */
}

.time-input-separator {
  font-size: 20px;
  font-weight: 600;
  margin: 0 4px;
}

.time-input-labels {
  display: flex;
  justify-content: space-around;
  font-size: 12px;
  color: #888;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 8px;
}

.time-input-hint {
  font-size: 14px;
  color: #888;
  text-align: center;
}

/* Action buttons */
.save-button {
  margin-bottom: 8px;
}

.cancel-button {
  margin-top: 0;
}

/* Dark mode adjustments */
@media (prefers-color-scheme: dark) {
  .edit-stint-modal {
    --background: #121212;
  }

  .time-input-field {
    border-color: #444;
    background-color: #2a2a2a;
    color: #f8f9fa;
  }

  .time-input-labels span {
    color: #aaa;
  }

  .time-input-hint {
    color: #aaa;
  }
}
