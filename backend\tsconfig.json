{
  "compilerOptions": {
    "target": "es2022", // Target environment (Node.js version)
    "lib": ["es2022"], // Standard libraries for the target
    "module": "CommonJS",  // Use modern Node.js module system (ESM)
    "moduleResolution": "Node", // How modules are resolved
    "outDir": "./build", // Output directory for compiled JS files
    "rootDir": "./src", // Specify root directory of source files (optional but good practice)

    "strict": true, // Enable all strict type-checking options
    "esModuleInterop": true, // Allows default imports from CommonJS modules
    "skipLibCheck": true, // Skip type checking of declaration files
    "forceConsistentCasingInFileNames": true, // Disallow inconsistently-cased references
    "allowJs": true, // Allow JavaScript files to be compiled
    "resolveJsonModule": true, // Allow importing JSON files
    "isolatedModules": true, // Ensure files can be safely transpiled without relying on other imports
    "moduleDetection": "force", // Treat files as modules even without import/export
    "noUncheckedIndexedAccess": true, // Add 'undefined' to index signatures
    "sourceMap": true, // Generate source map files for debugging

    // --- Crucial for Node.js Backend ---
     "types": ["node",  "express", "ws"] // Explicitly include Node.js type definitions
    // -----------------------------------
  },
  "include": ["src/**/*"], // Compile files inside the 'src' directory relative to this tsconfig
  "exclude": ["node_modules", "**/*.spec.ts", "**/*.test.ts"], // Exclude dependencies and test files
  
}
