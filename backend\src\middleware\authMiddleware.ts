import { Request, Response, NextFunction } from "express";
import jwt from "jsonwebtoken";
import { User } from "../models/User";
import { sendErrorResponse } from "../utils/controllerUtils";

// Interface for decoded token
interface DecodedToken {
  id: string;
  iat: number;
  exp: number;
}

// Protect routes - verify token and attach user to request
export const protect = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    let token;

    // Check for token in headers
    if (
      req.headers.authorization &&
      req.headers.authorization.startsWith("Bearer")
    ) {
      // Get token from header
      token = req.headers.authorization.split(" ")[1];
    }

    // Check if token exists
    if (!token) {
      return sendErrorResponse(
        res,
        "Not authorized to access this route - no token provided",
        401
      );
    }

    try {
      // Verify token
      const decoded = jwt.verify(
        token,
        process.env.JWT_SECRET || "your-secret-key"
      ) as DecodedToken;

      // Check if user exists in database
      const user = await User.findById(decoded.id);
      if (!user) {
        return sendErrorResponse(res, "User not found", 401);
      }

      // Attach user to request
      (req as any).user = { id: decoded.id };
      
      next();
    } catch (error: any) {
      if (error.name === 'TokenExpiredError') {
        return sendErrorResponse(res, "Token expired", 401);
      } else if (error.name === 'JsonWebTokenError') {
        return sendErrorResponse(res, "Invalid token", 401);
      } else {
        return sendErrorResponse(res, "Authentication error", 401);
      }
    }
  } catch (error) {
    console.error("Auth middleware error:", error);
    sendErrorResponse(res, "Authentication error", 500, error);
  }
};
