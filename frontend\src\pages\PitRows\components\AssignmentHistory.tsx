// d:\Downloads\Nuova cartella\my-app\frontend\src\pages\PitRows\components\AssignmentHistory.tsx
import React, { useState } from "react";
import {
  IonCard,
  IonCardHeader,
  IonCardTitle,
  IonCardContent,
  IonButton,
  IonIcon,
  IonModal,
  IonHeader,
  IonToolbar,
  IonTitle,
  IonButtons,
  IonContent,
  IonGrid,
  IonRow,
  IonCol,
  IonText,
  useIonAlert, // Import useIonAlert for confirmation
  IonSpinner, // Import Spinner for loading state
} from "@ionic/react";
import { format } from 'date-fns';
import { close, scan, trashOutline } from 'ionicons/icons';

//import "./common.css";
//import "./NextPits.css"; // Assuming similar grid styles
//import "./AssignmentHistory.css"; // Add a dedicated CSS file if needed

import { AssignmentLog } from "../../../types";

// --- Updated Props Interface ---
interface AssignmentHistoryProps {
  historyData: AssignmentLog[];
  onClearHistory: () => Promise<void>; // Handler for clearing history
  isClearingHistory: boolean; // Loading state for the clear operation
  // onRemoveHistoryEntry?: (logId: string) => void; // Keep if needed, but removal is discouraged
}

// --- Helper function to format event details ---
const formatEventDescription = (log: AssignmentLog): React.ReactNode => { // Return ReactNode to include elements
    const kartNum = log.kartId?.number ?? '?';
    const teamName = log.teamId?.name ?? '?';
    const teamNum = log.teamId?.number; // Keep number for context if needed
    const rowNum = log.rowId?.rowNumber;
    const rowColor = log.rowId?.color ?? 'grey'; // Default color if missing
    const prevTeamName = log.previousTeamId?.name ?? '?';
    const prevTeamNum = log.previousTeamId?.number;
    const prevRowNum = log.previousRowId?.rowNumber;

    // Small inline style for color swatch
    const colorSwatchStyle: React.CSSProperties = {
        display: 'inline-block',
        width: '12px',
        height: '12px',
        backgroundColor: rowColor,
        marginRight: '5px',
        verticalAlign: 'middle',
        border: '1px solid #ccc',
        borderRadius: '2px',
    };

    switch (log.eventType) {
        case 'ASSIGNED_TO_ROW':
            // Show Kart -> Row Color (Row Number)
            return (
                <>
                    Kart #{kartNum} {'--->'} <span style={colorSwatchStyle}></span> Row {rowNum}
                    <span style={{ opacity: 0.7, marginLeft: '5px' }}>(from: {prevTeamName})</span>
                </>
            );
        case 'ASSIGNED_TO_TEAM':
             // Show Kart -> Team Name (#Number)
            return (
                <>
                    Kart #{kartNum} {'--->'} Team: {teamName} {teamNum ? `(#${teamNum})` : ''}
                    <span style={{ opacity: 0.7, marginLeft: '5px' }}>(from Row {prevRowNum ?? 'N/A'})</span>
                </>
            );
        case 'UNASSIGNED_FROM_ROW':
             // Show Kart left Row Color (Row Number)
            return (
                 <>
                    Kart #{kartNum} left <span style={colorSwatchStyle}></span> Row {rowNum ?? 'N/A'}
                 </>
            );
        case 'UNASSIGNED_FROM_TEAM':
             // Show Kart left Team Name (#Number)
             return `Kart #${kartNum} left Team: ${teamName} ${teamNum ? `(#${teamNum})` : ''}`;
        default:
            return `Event: ${log.eventType} for Kart #${kartNum}`;
    }
};

// --- Reusable Row Component for the Grid ---
// No changes needed here unless you want to remove the delete button per log entry
const HistoryLogRow: React.FC<{ item: AssignmentLog; /* onRemoveClick: (logId: string) => void */ }> = ({ item /*, onRemoveClick */ }) => (
    <IonRow className="data-row assignment-history-row"> {/* Added specific class */}
        {/* Timestamp */}
        <IonCol size="3" size-md="2">
            <IonText color="medium">{format(new Date(item.timestamp), 'HH:mm:ss')}</IonText>
        </IonCol>
        {/* Event Description */}
        <IonCol size="9" size-md="10"> {/* Adjusted size */}
            <IonText color="dark">{formatEventDescription(item)}</IonText>
        </IonCol>
        {/* Actions Column Removed - Deleting single entries is discouraged */}
        {/* <IonCol size="1" size-md="1" style={{ textAlign: 'right' }}>
             <IonButton fill="clear" color="danger" size="small" onClick={() => onRemoveClick(item._id)}>
                <IonIcon slot="icon-only" icon={trashOutline} />
            </IonButton>
        </IonCol> */}
    </IonRow>
);

// --- Modal for Full History ---
// No changes needed here unless you want to remove the delete button per log entry
const FullHistoryModal: React.FC<{ isOpen: boolean; onClose: () => void; data: AssignmentLog[]; /* onRemoveClick: (logId: string) => void */ }> = ({ isOpen, onClose, data /*, onRemoveClick */ }) => (
    <IonModal isOpen={isOpen} onDidDismiss={onClose}>
        <IonHeader>
            <IonToolbar>
                <IonTitle>Full Assignment History</IonTitle>
                <IonButtons slot="end">
                    <IonButton onClick={onClose}>
                        <IonIcon icon={close} />
                    </IonButton>
                </IonButtons>
            </IonToolbar>
        </IonHeader>
        <IonContent>
            <IonGrid className="assignment-history-table">
                {/* Header Row */}
                <IonRow className="header-row">
                    <IonCol size="3" size-md="2">Time</IonCol>
                    <IonCol size="9" size-md="10">Event</IonCol> {/* Adjusted size */}
                    {/* <IonCol size="1" size-md="1"></IonCol> */} {/* Actions Header Removed */}
                </IonRow>
                {/* Data Rows */}
                {data.map((item) => (
                    <HistoryLogRow key={item._id} item={item} /* onRemoveClick={onRemoveClick} */ />
                ))}
                {data.length === 0 && (
                    <IonRow>
                        <IonCol style={{ textAlign: 'center', marginTop: '1rem' }}>
                            <IonText color="medium">No history found.</IonText>
                        </IonCol>
                    </IonRow>
                )}
            </IonGrid>
        </IonContent>
    </IonModal>
);

// --- Main Component ---
const AssignmentHistory: React.FC<AssignmentHistoryProps> = ({
    historyData = [],
    onClearHistory, // Receive handler
    isClearingHistory, // Receive loading state
 }) => {
    const [showFullTable, setShowFullTable] = useState(false);
    const [presentAlert] = useIonAlert(); // Hook for confirmation dialog
    const displayLimit = 5;

    // --- Handler for the Clear History Button ---
    const handleClearClick = () => {
        presentAlert({
            header: 'Clear History?',
            message: 'This will permanently delete all assignment history logs. Are you sure?',
            buttons: [
                { text: 'Cancel', role: 'cancel' },
                {
                    text: 'Clear All',
                    role: 'destructive',
                    handler: () => {
                        onClearHistory(); // Call the passed-in handler
                    },
                },
            ],
        });
    };

    // Placeholder handler for individual row removal (discouraged)
    // const handleRemoveLogRow = (logId: string) => { ... };

    return (
        <IonCard className="list-card">
            <IonCardHeader className="next-pits-header">
                <div className="next-pits-title-container">
                    <IonCardTitle>Assignment History</IonCardTitle>
                    {/* Container for buttons */}
                    <div className="header-buttons">
                         {/* Clear History Button */}
                         <IonButton
                            fill="clear"
                            color="danger"
                            size="small"
                            onClick={handleClearClick}
                            disabled={isClearingHistory || historyData.length === 0} // Disable if clearing or no data
                            title="Clear All History"
                        >
                            {isClearingHistory ? <IonSpinner name="dots" color="danger" /> : <IonIcon slot="icon-only" icon={trashOutline} />}
                        </IonButton>
                        {/* Expand Button */}
                        {historyData.length > displayLimit && (
                            <IonButton fill="clear" size="small" onClick={() => setShowFullTable(true)} disabled={isClearingHistory}>
                                <IonIcon slot="icon-only" icon={scan}/>
                            </IonButton>
                        )}
                    </div>
                </div>
            </IonCardHeader>

            <IonCardContent>
                <IonGrid className="ion-no-padding assignment-history-table">
                    {historyData.length > 0 && (
                        <IonRow className="header-row" style={{ fontSize: '0.8em', color: 'var(--ion-color-medium)'}}>
                            <IonCol size="3" size-md="2">Time</IonCol>
                            <IonCol size="9" size-md="10">Event</IonCol> {/* Adjusted size */}
                            {/* <IonCol size="1" size-md="1"></IonCol> */} {/* Actions Header Removed */}
                        </IonRow>
                    )}
                    {historyData.slice(0, displayLimit).map((item) => (
                        <HistoryLogRow key={item._id} item={item} /* onRemoveClick={handleRemoveLogRow} */ />
                    ))}
                    {historyData.length === 0 && (
                         <IonRow>
                            <IonCol style={{ textAlign: 'center', marginTop: '0.5rem', marginBottom: '0.5rem' }}>
                                <IonText color="medium">No recent history.</IonText>
                            </IonCol>
                        </IonRow>
                    )}
                </IonGrid>
            </IonCardContent>

            {historyData.length > 0 && (
                <FullHistoryModal
                    isOpen={showFullTable}
                    onClose={() => setShowFullTable(false)}
                    data={historyData}
                    /* onRemoveClick={handleRemoveLogRow} */
                />
            )}
        </IonCard>
    );
};

// Add some basic CSS for the header buttons if needed
/*
// AssignmentHistory.css
.header-buttons {
    display: flex;
    align-items: center;
}
*/

export default AssignmentHistory;
