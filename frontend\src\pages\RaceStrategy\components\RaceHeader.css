/* RaceHeader Component Styles - Compact Version */

.race-header-container {
  background-color: var(--ion-color-light);
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  margin-bottom: 12px;
  cursor: pointer;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.race-header-container:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
}

.race-header-main {
  padding: 10px 12px;
  background: linear-gradient(to right, rgba(var(--ion-color-primary-rgb), 0.05), rgba(var(--ion-color-primary-rgb), 0.15));
}

.race-title-section {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
}

.race-title {
  font-size: 1.2rem;
  font-weight: 700;
  color: var(--ion-color-dark);
  margin: 0;
}

.race-type {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 4px;
}

.race-type-chip {
  margin: 0;
  font-weight: 500;
  --background: var(--ion-color-primary);
  --color: var(--ion-color-primary-contrast);
  height: 24px;
  font-size: 0.7rem;
}

.race-status-badge {
  padding: 4px 8px;
  border-radius: 8px;
  font-size: 0.7rem;
}

.race-time-section {
  margin-top: 6px;
}

.race-time-display {
  display: flex;
  justify-content: space-between;
  margin-bottom: 6px;
}

.race-time-current, .race-time-remaining {
  display: flex;
  align-items: center;
  gap: 4px;
}

.race-time-current ion-icon, .race-time-remaining ion-icon {
  font-size: 0.9rem;
  color: var(--ion-color-primary);
}

.time-label {
  font-size: 0.7rem;
  color: var(--ion-color-medium);
}

.time-value {
  font-size: 0.9rem;
  font-weight: 600;
  color: var(--ion-color-dark);
}

.race-progress-bar {
  height: 4px;
  border-radius: 2px;
  overflow: hidden;
  --buffer-background: rgba(var(--ion-color-medium-rgb), 0.2);
}

.race-stats-section {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  padding: 8px;
  background-color: var(--ion-color-light);
  border-top: 1px solid rgba(var(--ion-color-medium-rgb), 0.1);
}

.race-stat {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 4px;
  text-align: center;
}

.race-stat ion-icon {
  font-size: 1rem;
  color: var(--ion-color-primary);
  margin-bottom: 2px;
}

.stat-label {
  font-size: 0.6rem;
  color: var(--ion-color-medium);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  margin-bottom: 1px;
}

.stat-value {
  font-size: 0.8rem;
  font-weight: 600;
  color: var(--ion-color-dark);
}

/* Race Rules Section */
.race-rules-section {
  padding: 8px;
  background-color: rgba(var(--ion-color-light-rgb), 0.7);
  border-top: 1px solid rgba(var(--ion-color-medium-rgb), 0.1);
}

.rules-grid {
  padding: 0;
  margin: 0;
}

.rule-item {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 4px 0;
}

.rule-item ion-icon {
  font-size: 0.9rem;
  color: var(--ion-color-primary);
}

.rule-content {
  display: flex;
  flex-direction: column;
}

.rule-label {
  font-size: 0.65rem;
  color: var(--ion-color-medium);
}

.rule-value {
  font-size: 0.75rem;
  font-weight: 600;
  color: var(--ion-color-dark);
}

/* Compact header styles */
.race-header-compact {
  background-color: var(--ion-color-light);
  border-radius: 8px;
  padding: 12px;
  margin: 12px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
  cursor: pointer;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.race-header-compact:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

.race-header-top {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.race-title {
  display: flex;
  align-items: center;
  gap: 8px;
}

.race-title h2 {
  margin: 0;
  font-size: 1.2rem;
  font-weight: 600;
}

.race-status-badge {
  font-size: 0.6rem;
  padding: 3px 6px;
  height: 18px;
}

.race-type {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 0.9rem;
  color: var(--ion-color-medium);
}

.race-time-display {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 10px 0;
  font-size: 1rem;
}

.race-time-current {
  display: flex;
  align-items: center;
  gap: 6px;
  font-weight: 600;
  font-size: 1.2rem;
}

.race-time-remaining {
  display: flex;
  align-items: center;
  gap: 6px;
  color: var(--ion-color-medium);
}

.race-progress-bar {
  height: 6px;
  border-radius: 3px;
  margin: 8px 0 12px 0;
}

.race-stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
  gap: 10px;
  margin-top: 10px;
}

.race-stat {
  text-align: center;
  background-color: rgba(var(--ion-color-primary-rgb), 0.05);
  border-radius: 6px;
  padding: 8px;
}

.stat-label {
  font-size: 0.7rem;
  color: var(--ion-color-medium);
  margin-bottom: 4px;
}

.stat-value {
  font-size: 0.9rem;
  font-weight: 600;
}

/* Responsive adjustments */
@media (max-width: 576px) {
  .race-title-section {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .race-type {
    flex-direction: row;
    align-items: center;
    margin-top: 6px;
    gap: 6px;
  }
  
  .race-time-display {
    flex-direction: column;
    gap: 4px;
  }
  
  .race-stats-section {
    grid-template-columns: repeat(2, 1fr);
    gap: 4px;
  }
  
  .compact-header-top {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .compact-race-title {
    margin-bottom: 8px;
  }
  
  .compact-race-type {
    align-self: flex-start;
  }
  
  .compact-stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}




