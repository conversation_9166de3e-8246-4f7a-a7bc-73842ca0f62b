// backend/src/routes/adminRoutes.ts
import express from "express";
import {
    resetAssignments,
    deleteAllTeamsAndKarts,
    resetKartSpeeds,
    batchCreateKarts,
    unassignAllKartsFromTeams
} from "../controllers/adminController";
import { protect } from "../middleware/authMiddleware"; // Import middleware

const router = express.Router();

// Protect all admin routes
router.use(protect);

// Admin routes (all protected)
router.post("/reset-assignments", resetAssignments);
router.delete("/teams-and-karts/all", deleteAllTeamsAndKarts);
router.post("/karts/reset-speeds", resetKartSpeeds);
router.post("/karts/batch-create", batchCreateKarts);
router.post("/karts/unassign-all-from-teams", unassignAllKartsFromTeams);

export default router;
