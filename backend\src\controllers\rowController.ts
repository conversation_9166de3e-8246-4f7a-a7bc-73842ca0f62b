import { Request, Response } from "express";
import mongoose, { ClientSession } from "mongoose";
import { Row, IRow } from "../models/Row";
import { Team, ITeam } from "../models/Team";
import { Kart, IKart } from "../models/Kart";
import { sendWebSocketMessage } from "../websocket/websocket";

// *** NEW: Import AssignmentLog model ***
import { AssignmentLog, IAssignmentLog } from "../models/AssignmentLog";
import { ApexTeam, ApexKart } from "../models/ApexModels";

// --- Constants for WebSocket events ---
const ROW_UPDATE_EVENT = "rowsUpdated";
const KART_UPDATE_EVENT = "kartsUpdated";
const TEAM_UPDATE_EVENT = "teamsUpdated";

// *** NEW: Constant for Assignment Log event (optional) ***
// const ASSIGNMENT_LOG_EVENT = "assignmentLogged";

// Helper function to populate kart data (both regular and apex karts)
async function populateRowKarts(rows: any[], useApexDatabase = false, sessionId?: string) {
  const populatedRows = [];

  for (const row of rows) {
    const rowObj = row.toObject ? row.toObject() : row;

    if (rowObj.currentKartId) {
      let kartData = null;

      if (useApexDatabase && sessionId) {
        // Try to find apex kart first
        kartData = await ApexKart.findOne({
          _id: rowObj.currentKartId,
          sessionId: new mongoose.Types.ObjectId(sessionId)
        }).lean();
      }

      // If not found in apex or not using apex, try regular kart
      if (!kartData) {
        kartData = await Kart.findById(rowObj.currentKartId).lean();
      }

      rowObj.currentKartId = kartData;
    }

    populatedRows.push(rowObj);
  }

  return populatedRows;
}

import {
  isValidObjectId, // Keep for potential use elsewhere, though less needed for rowNumber lookups
  sendErrorResponse,
  safelyEndSession,
  abortTransactionAndEndSession,
} from "../utils/controllerUtils";

// --- Row Controllers ---

/**
 * @description Create a new row with the next sequential rowNumber
 * @route POST /api/rows
 */
export const createRow = async (req: Request, res: Response) => {
  const { color } = req.body; // Only color is needed from body now
  if (!color) {
    return sendErrorResponse(res, "Row color is required.", 400);
  }

  try {
    // Find the row with the highest rowNumber
    const highestRow = await Row.findOne().sort({ rowNumber: -1 });
    const nextRowNumber = highestRow ? highestRow.rowNumber + 1 : 1; // Start from 1 if no rows exist

    const newRow = new Row({
      rowNumber: nextRowNumber, // Assign calculated sequential number
      color,
      currentKartId: null,
      pastKarts: [],
    });

    const savedRow = await newRow.save();
    // Send WebSocket message
    sendWebSocketMessage({
      event: ROW_UPDATE_EVENT,
      payload: { type: "create", row: savedRow },
    });
    res.status(201).json(savedRow);
  } catch (error: unknown) {
    // Handle potential duplicate key error on rowNumber (though less likely with sequential logic if no manual inserts)
    if (
      error &&
      typeof error === "object" &&
      "code" in error &&
      error.code === 11000
    ) {
      // This might indicate a race condition or manual insertion conflict
      return sendErrorResponse(
        res,
        `Row number conflict occurred. Please try again.`,
        409,
        error
      );
    }
    sendErrorResponse(res, "Error creating row", 500, error);
  }
};

// Get all rows with smart population for apex/regular karts
export const getRows = async (req: Request, res: Response) => {
  try {
    const { useApexDatabase, sessionId } = req.query;

    // Get rows without population first
    const rows = await Row.find()
      .sort({ rowNumber: 1 })
      .lean()
      .exec();

    // Use smart population to handle both apex and regular karts
    const populatedRows = await populateRowKarts(
      rows,
      useApexDatabase === 'true',
      sessionId as string
    );

    res.status(200).json(populatedRows);
  } catch (error: unknown) {
    sendErrorResponse(res, "Error fetching rows", 500, error);
  }
};

/**
 * @description Get a single row by its rowNumber
 * @route GET /api/rows/:rowNumber
 */
export const getRowByNumber = async (req: Request, res: Response) => {
  const rowNumberParam = req.params.rowNumber;
  // FIX 1: Provide default empty string if rowNumberParam is undefined
  const rowNumber = parseInt(rowNumberParam || "", 10);

  if (isNaN(rowNumber) || rowNumber <= 0) {
    return sendErrorResponse(
      res,
      "Valid positive Row Number is required.",
      400
    );
  }

  try {
    const row = await Row.findOne({ rowNumber: rowNumber })
      .populate<{ currentKartId: Pick<IKart, "number" | "speed" | "_id"> | null }>({
        path: "currentKartId",
        select: "number speed _id",
      })
      .lean()
      .exec();

    if (!row) {
      return sendErrorResponse(
        res,
        `Row with number ${rowNumber} not found`,
        404
      );
    }
    res.status(200).json(row);
  } catch (error: unknown) {
    sendErrorResponse(res, "Error fetching row", 500, error);
  }
};

/**
 * @description Update a row by its rowNumber
 * @route PUT /api/rows/:rowNumber
 */
export const updateRow = async (req: Request, res: Response) => {
  const rowNumberParam = req.params.rowNumber;
  // FIX 2: Provide default empty string if rowNumberParam is undefined
  const rowNumber = parseInt(rowNumberParam || "", 10);

  if (isNaN(rowNumber) || rowNumber <= 0) {
    return sendErrorResponse(
      res,
      "Valid positive Row Number is required.",
      400
    );
  }

  // Only allow updating specific fields like 'color'
  const { color } = req.body;
  const updateData: Partial<Pick<IRow, "color">> = {}; // Only allow color update via this route for simplicity
  if (color !== undefined) updateData.color = color;
  // If you need to update rowNumber, it's complex due to uniqueness and sequence, handle with care in a separate endpoint/logic

  if (Object.keys(updateData).length === 0) {
    return sendErrorResponse(
      res,
      "No valid fields provided for update (only 'color' is allowed).",
      400
    );
  }

  try {
    // Find by rowNumber and update
    const updatedRow = await Row.findOneAndUpdate(
      { rowNumber: rowNumber }, // Find condition
      updateData,
      { new: true, runValidators: true }
    )
      .populate<{ currentKartId: Pick<IKart, "number" | "_id"> | null }>({
        path: "currentKartId",
        select: "number _id",
      })
      .lean();

    if (!updatedRow) {
      return sendErrorResponse(
        res,
        `Row with number ${rowNumber} not found`,
        404
      );
    }

    // Send WebSocket message
    sendWebSocketMessage({
      event: ROW_UPDATE_EVENT,
      payload: { type: "update", row: updatedRow },
    });
    res.status(200).json(updatedRow);
  } catch (error: unknown) {
    // Handle validation errors (e.g., invalid color format if schema enforces it)
    if (
      error &&
      typeof error === "object" &&
      "name" in error &&
      error.name === "ValidationError"
    ) {
      return sendErrorResponse(
        res,
        `Validation Error: ${(error as Error).message}`,
        400,
        error
      );
    }
    sendErrorResponse(res, "Error updating row", 500, error);
  }
};

/**
 * @description Delete a row by its rowNumber, automatically unassigning any kart present.
 * @route DELETE /api/rows/:rowNumber
 */
export const deleteRow = async (req: Request, res: Response) => {
  let session: ClientSession | null = null;
  const operation = "deleteRowByNumber";
  const rowNumberParam = req.params.rowNumber;
  const rowNumber = parseInt(rowNumberParam || "", 10);

  if (isNaN(rowNumber) || rowNumber <= 0) {
    return sendErrorResponse(
      res,
      "Valid positive Row Number is required.",
      400
    );
  }

  try {
    session = await mongoose.startSession();
    session.startTransaction();
    console.log(
      `[${operation}] Transaction started for row number ${rowNumber}.`
    );

    const rowToDelete = await Row.findOne({ rowNumber: rowNumber }).session(
      session
    );
    if (!rowToDelete) {
      await abortTransactionAndEndSession(session, `${operation}NotFound`);
      return sendErrorResponse(
        res,
        `Row with number ${rowNumber} not found`,
        404
      );
    }
    console.log(
      `[${operation}] Found Row ${rowToDelete.rowNumber} (ID: ${rowToDelete._id}).`
    );

    let kartIdToUpdate: mongoose.Types.ObjectId | null = null; // Use ObjectId type
    let updatedKartData: IKart | null = null;

    if (rowToDelete.currentKartId) {
      kartIdToUpdate = rowToDelete.currentKartId; // Already ObjectId | null
      console.log(
        `[${operation}] Row has Kart ${kartIdToUpdate} assigned. Unassigning kart...`
      );

      const kartUpdateResult = await Kart.findByIdAndUpdate(
        kartIdToUpdate,
        { $set: { currentRowId: null, status: "available" } },
        { new: true, session }
      );

      if (!kartUpdateResult) {
        console.warn(
          `[${operation}] Kart ${kartIdToUpdate} was assigned to row ${rowNumber} but not found during update. Proceeding with row deletion.`
        );
        kartIdToUpdate = null;
      } else {
        updatedKartData = kartUpdateResult;
        console.log(
          `[${operation}] Kart ${kartIdToUpdate} unassigned and status set to 'available'.`
        );

        // *** NEW: Log the unassignment event ***
        const logEntry = new AssignmentLog({
          eventType: "UNASSIGNED_FROM_ROW",
          kartId: kartIdToUpdate,
          rowId: rowToDelete._id, // The row it left
          // previousRowId: rowToDelete._id, // Can also use previousRowId
          timestamp: new Date(), // Explicit timestamp
        });
        await logEntry.save({ session });
        console.log(
          `[${operation}] Logged UNASSIGNED_FROM_ROW for Kart ${kartIdToUpdate}.`
        );
      }
    } else {
      console.log(`[${operation}] Row ${rowNumber} has no kart assigned.`);
    }

    const deleteResult = await Row.findOneAndDelete({
      rowNumber: rowNumber,
    }).session(session);
    if (!deleteResult) {
      throw new Error(`Failed to delete Row ${rowNumber} after finding it.`);
    }
    console.log(`[${operation}] Row ${rowNumber} deleted.`);

    await session.commitTransaction();
    console.log(`[${operation}] Transaction committed.`);

    // --- Post-Commit Actions ---
    sendWebSocketMessage({
      event: ROW_UPDATE_EVENT,
      payload: { type: "delete", rowId: rowToDelete._id.toString() },
    });
    if (updatedKartData) {
      const populatedKart = await Kart.findById(updatedKartData._id)
        .populate<{
          currentTeamId: Pick<ITeam, "name" | "number" | "_id"> | null;
        }>({ path: "currentTeamId", select: "name number _id" })
        .populate<{
          currentRowId: Pick<IRow, "rowNumber" | "color" | "_id"> | null;
        }>({ path: "currentRowId", select: "rowNumber color _id" })
        .lean();
      if (populatedKart) {
        sendWebSocketMessage({
          event: KART_UPDATE_EVENT,
          payload: { type: "update", kart: populatedKart },
        });
      } else {
        console.warn(
          `[${operation}] Could not re-fetch populated kart ${updatedKartData._id} for WebSocket message.`
        );
      }
      // Optional: Send WS message for the new log entry
      // sendWebSocketMessage({ event: ASSIGNMENT_LOG_EVENT, payload: { type: "create", log: logEntry } });
    }

    res.status(200).json({
      message: `Row ${rowNumber} deleted successfully${updatedKartData ? ` and Kart ${updatedKartData.number} was unassigned` : ""}.`,
    });
  } catch (error: unknown) {
    await abortTransactionAndEndSession(session, operation, error);
    sendErrorResponse(res, `Error deleting row ${rowNumber}`, 500, error);
  } finally {
    await safelyEndSession(session);
  }
};

// --- Swap Kart between a Row and a Team ---
// This function uses IDs, which is fine as it's a specific operation between known entities.
// No changes needed here unless you also want to swap using rowNumber.
export const swapKartWithTeam = async (req: Request, res: Response): Promise<void>  => {
  let session: ClientSession | null = null;
  const operation = "swapKartWithTeam";
  try {
    session = await mongoose.startSession();
    session.startTransaction();
    console.log(`[${operation}] Transaction started.`);

    const { rowId, teamId } = req.params;

    // 1. Validate IDs
    if (
      !rowId ||
      !teamId ||
      !isValidObjectId(rowId) ||
      !isValidObjectId(teamId)
    ) {
      await abortTransactionAndEndSession(session, `${operation}Validation`);
      return sendErrorResponse(
        res,
        "Valid Row and Team IDs required for swap.",
        400
      );
    }
    console.log(`[${operation}] IDs validated: Row=${rowId}, Team=${teamId}`);

    // 2. Fetch Row and Team
    const row = await Row.findById(rowId).session(session);
    if (!row) {
      throw new Error(`Row with ID ${rowId} not found.`);
    }
    const team = await Team.findById(teamId).session(session);
    if (!team) {
      throw new Error(`Team with ID ${teamId} not found.`);
    }
    console.log(
      `[${operation}] Row ${row.rowNumber} and Team ${team.number} found.`
    );

    const rowKartId = row.currentKartId;
    const teamKartId = team.currentKartId;
    console.log(
      `[${operation}] Row Kart ID: ${rowKartId}, Team Kart ID: ${teamKartId}`
    );

    // 3. Handle Edge Cases
    if (rowKartId?.toString() === teamKartId?.toString()) {
      console.log(
        `[${operation}] Info: Row ${row.rowNumber} and Team ${team.number} already have the same kart or both are empty. No swap needed.`
      );
      await abortTransactionAndEndSession(session, operation);
      sendErrorResponse(res, "Row and Team already have the same kart.", 200);
      return;
    }

    // --- Swap Logic ---
    let teamKart: IKart | null = null;
    let rowKart: IKart | null = null;
    const kartsToSave: IKart[] = [];
    const updatedKartIds: string[] = [];
    // *** NEW: Array to hold log entries ***
    const logEntriesToSave: IAssignmentLog[] = []; // Use 'any' for now, or import IAssignmentLog

    // 4. Prepare Kart Updates & Log Entries
    const now = new Date(); // Consistent timestamp for related logs

    if (teamKartId) {
      teamKart = await Kart.findById(teamKartId).session(session);
      if (!teamKart) {
        throw new Error(`Kart ${teamKartId} (from team) not found.`);
      }
      if (teamKart.currentRowId && teamKart.currentRowId.toString() !== rowId) {
        throw new Error(
          `Kart ${teamKart.number} (from team) is unexpectedly assigned to another row (${teamKart.currentRowId}). Cannot swap.`
        );
      }

      // *** NEW: Log kart moving TO row FROM team ***
      logEntriesToSave.push(
        new AssignmentLog({
          eventType: "ASSIGNED_TO_ROW",
          kartId: teamKart._id,
          rowId: row._id, // Destination row
          previousTeamId: team._id, // Source team
          timestamp: now,
        })
      );

      teamKart.currentTeamId = null;
      teamKart.currentRowId = row._id;
      teamKart.status = "in_pit_row";
      kartsToSave.push(teamKart);
      updatedKartIds.push(teamKartId.toString());
      console.log(
        `[${operation}] Preparing Team Kart ${teamKart.number}: Set teamId=null, rowId=${row._id}, status=in_pit_row`
      );
    }

    if (rowKartId) {
      rowKart = await Kart.findById(rowKartId).session(session);
      if (!rowKart) {
        throw new Error(`Kart ${rowKartId} (from row) not found.`);
      }
      if (
        rowKart.currentTeamId &&
        rowKart.currentTeamId.toString() !== teamId
      ) {
        throw new Error(
          `Kart ${rowKart.number} (from row) is unexpectedly assigned to another team (${rowKart.currentTeamId}). Cannot swap.`
        );
      }

      // *** NEW: Log kart moving TO team FROM row ***
      logEntriesToSave.push(
        new AssignmentLog({
          eventType: "ASSIGNED_TO_TEAM",
          kartId: rowKart._id,
          teamId: team._id, // Destination team
          previousRowId: row._id, // Source row
          timestamp: now,
        })
      );

      rowKart.currentTeamId = team._id;
      rowKart.currentRowId = null;
      rowKart.status = "on_track";
      kartsToSave.push(rowKart);
      updatedKartIds.push(rowKartId.toString());
      console.log(
        `[${operation}] Preparing Row Kart ${rowKart.number}: Set teamId=${team._id}, rowId=null, status=on_track`
      );
    }

    // 5. Update Row and Team references
    row.currentKartId = teamKart?._id ?? null;
    team.currentKartId = rowKart?._id ?? undefined;
    console.log(
      `[${operation}] Updated Row ${row.rowNumber} currentKartId to ${row.currentKartId}`
    );
    console.log(
      `[${operation}] Updated Team ${team.number} currentKartId to ${team.currentKartId}`
    );

    // 6. Save all changes (Karts, Row, Team, AND Logs)
    await Promise.all([
      row.save({ session }),
      team.save({ session }),
      ...kartsToSave.map((k) => k.save({ session })),
      // *** NEW: Save log entries ***
      ...logEntriesToSave.map((log) => log.save({ session })),
    ]);
    console.log(
      `[${operation}] Row, Team, ${kartsToSave.length} Kart(s), and ${logEntriesToSave.length} Log(s) saved.`
    );

    // 7. Commit Transaction
    await session.commitTransaction();
    console.log(`[${operation}] Transaction committed.`);

    // --- Post-Commit Actions ---
    // 8. Fetch updated data
    const [finalRow, finalTeam, ...finalKarts] = await Promise.all([
      Row.findById(rowId)
        .populate<{
          currentKartId: Pick<IKart, "number" | "_id"> | null;
        }>({ path: "currentKartId", select: "number _id" })
        .lean(),
      Team.findById(teamId)
        .populate<{
          currentKartId: Pick<IKart, "number" | "_id"> | null;
        }>({ path: "currentKartId", select: "number _id" })
        .lean(),
      ...updatedKartIds.map((id) =>
        Kart.findById(id)
          .populate<{
            currentTeamId: Pick<ITeam, "name" | "number" | "_id"> | null;
            currentRowId: Pick<IRow, "rowNumber" | "color" | "_id"> | null;
          }>([
            { path: "currentTeamId", select: "name number _id" },
            { path: "currentRowId", select: "rowNumber color _id" },
          ])
          .lean()
      ),
    ]);
    console.log(`[${operation}] Fetched updated data post-commit.`);

    // 9. Send WebSocket updates (No change needed here unless adding log events)
    if (finalRow)
      sendWebSocketMessage({
        event: ROW_UPDATE_EVENT,
        payload: { type: "update", row: finalRow },
      });
    if (finalTeam)
      sendWebSocketMessage({
        event: TEAM_UPDATE_EVENT,
        payload: { type: "update", team: finalTeam },
      });
    finalKarts.forEach((k) => {
      if (k)
        sendWebSocketMessage({
          event: KART_UPDATE_EVENT,
          payload: { type: "update", kart: k },
        });
    });
    // Optional: Send WS message for the new log entries
    // logEntriesToSave.forEach(log => sendWebSocketMessage({ event: ASSIGNMENT_LOG_EVENT, payload: { type: "create", log: log } }));
    console.log(`[${operation}] WebSocket messages sent.`);

    // 10. Send Success Response
    res.status(200).json({
      message: `Successfully swapped karts between Row ${row.rowNumber} and Team ${team.number}.`,
      row: finalRow,
      team: finalTeam,
    });
  } catch (error: unknown) {
    await abortTransactionAndEndSession(session, operation, error);
    const statusCode =
      error instanceof Error &&
      (error.message.includes("not found") ||
        error.message.includes("unexpectedly assigned"))
        ? 404
        : error instanceof Error &&
            error.message.includes("already have the same kart")
          ? 200
          : 500;
    sendErrorResponse(
      res,
      `Error during kart swap: ${error instanceof Error ? error.message : "Unknown error"}`,
      statusCode,
      error
    );
  } finally {
    await safelyEndSession(session);
  }
};

export const assignKartToRow = async (req: Request, res: Response): Promise<void>  => {
  let session: ClientSession | null = null;
  const operation = "assignKartToRow";
  const { rowId, kartId } = req.params;

  // 1. Validate IDs
  if (
    !rowId ||
    !kartId ||
    !isValidObjectId(rowId) ||
    !isValidObjectId(kartId)
  ) {
    // No need to start/end session for basic validation failure
    return sendErrorResponse(res, "Valid Row and Kart IDs are required.", 400);
  }

  try {
    session = await mongoose.startSession();
    session.startTransaction();
    console.log(
      `[${operation}] Transaction started for Row ${rowId}, Kart ${kartId}.`
    );

    // 2. Fetch Row and Kart within transaction
    const row = await Row.findById(rowId).session(session);
    if (!row) {
      throw new Error(`Row ${rowId} not found.`);
    }

    const kart = await Kart.findById(kartId).session(session);
    if (!kart) {
      throw new Error(`Kart ${kartId} not found.`);
    }

    console.log(
      `[${operation}] Found Row ${row.rowNumber} and Kart ${kart.number}.`
    );

    // 3. Perform Validation Checks
    // Check if row is already occupied
    if (row.currentKartId) {
      // Avoid assigning if the kart is the same one already there
      if (row.currentKartId.toString() === kartId) {
        console.log(
          `[${operation}] Info: Kart ${kart.number} is already assigned to Row ${row.rowNumber}. No action needed.`
        );
        await abortTransactionAndEndSession(session, operation); // Abort, not an error
        sendErrorResponse(res, "Kart is already assigned to this row.", 200);
        return;
      }
      // Row is occupied by a DIFFERENT kart
      throw new Error(
        `Row ${row.rowNumber} is already occupied by Kart ID ${row.currentKartId}.`
      );
    }

    // Check if kart is already assigned elsewhere (to another row or a team)
    if (kart.currentRowId) {
      throw new Error(
        `Kart ${kart.number} is already assigned to Row ID ${kart.currentRowId}.`
      );
    }
    if (kart.currentTeamId) {
      throw new Error(
        `Kart ${kart.number} is currently assigned to Team ID ${kart.currentTeamId}. Unassign from team first.`
      );
    }
    // Optional: Check kart status (e.g., allow only 'available' or 'maintenance' karts?)
    // if (!['available', 'maintenance'].includes(kart.status)) {
    //     throw new Error(`Kart ${kart.number} has status '${kart.status}' and cannot be assigned directly to a row.`);
    // }

    console.log(`[${operation}] Validation passed.`);

    // 4. Update Documents
    const previousTeamIdForLog = kart.currentTeamId; // Capture just before clearing (though validation should ensure it's null)

    // Update Row
    row.currentKartId = kart._id;

    // Update Kart
    kart.currentRowId = row._id;
    kart.currentTeamId = null; // Ensure it's not assigned to a team
    kart.status = "in_pit_row"; // Set appropriate status

    // 5. Create Log Entry
    const logEntry = new AssignmentLog({
      eventType: "ASSIGNED_TO_ROW",
      kartId: kart._id,
      rowId: row._id,
      previousTeamId: previousTeamIdForLog, // Log previous team if it existed (should be null based on validation)
      timestamp: new Date(),
    });

    console.log(`[${operation}] Preparing to save Row, Kart, and Log.`);

    // 6. Save all changes within transaction
    await Promise.all([
      row.save({ session }),
      kart.save({ session }),
      logEntry.save({ session }),
    ]);
    console.log(`[${operation}] Row, Kart, and Log saved.`);

    // 7. Commit Transaction
    await session.commitTransaction();
    console.log(`[${operation}] Transaction committed.`);

    // 8. Post-Commit Actions (Fetch updated data, send WS messages)
    const [updatedRow, updatedKart] = await Promise.all([
      Row.findById(rowId)
        .populate<{
          currentKartId: Pick<IKart, "number" | "_id"> | null;
        }>({ path: "currentKartId", select: "number _id" })
        .lean(),
      Kart.findById(kartId)
        .populate<{
          currentRowId: Pick<IRow, "rowNumber" | "color" | "_id"> | null;
        }>({ path: "currentRowId", select: "rowNumber color _id" })
        .lean(), // Populate row for kart update
    ]);
    console.log(`[${operation}] Fetched updated data post-commit.`);

    if (updatedRow)
      sendWebSocketMessage({
        event: ROW_UPDATE_EVENT,
        payload: { type: "update", row: updatedRow },
      });
    if (updatedKart)
      sendWebSocketMessage({
        event: KART_UPDATE_EVENT,
        payload: { type: "update", kart: updatedKart },
      });
    // Optional: Send WS message for the new log entry
    // sendWebSocketMessage({ event: "assignmentLogCreated", payload: { log: logEntry.toObject() } });
    console.log(`[${operation}] WebSocket messages sent.`);

    // 9. Send Success Response
    res.status(200).json({
      message: `Kart #${kart.number} successfully assigned to Row ${row.rowNumber}.`,
      row: updatedRow,
      kart: updatedKart,
    });
  } catch (error: unknown) {
    await abortTransactionAndEndSession(session, operation, error); // Handles abort/end
    // Determine status code based on error message
    const statusCode =
      error instanceof Error &&
      (error.message.includes("not found") ||
        error.message.includes("already occupied") ||
        error.message.includes("already assigned"))
        ? 409 // Conflict
        : 500; // Internal Server Error
    sendErrorResponse(
      res,
      `Error assigning kart to row: ${error instanceof Error ? error.message : "Unknown error"}`,
      statusCode,
      error
    );
  } finally {
    await safelyEndSession(session); // Ensure session is always ended
  }
};

/**
 * @description Unassign a Kart from a specific Row (Empty the Row)
 * @route DELETE /api/rows/:rowId/kart
 */
export const unassignKartFromRow = async (req: Request, res: Response): Promise<void> => {
    let session: ClientSession | null = null;
    const operation = "unassignKartFromRow";
    const { rowId } = req.params;

    // 1. Validate ID
    if (!rowId || !isValidObjectId(rowId)) {
        return sendErrorResponse(res, "Valid Row ID is required.", 400);
    }

    try {
        session = await mongoose.startSession();
        session.startTransaction();
        console.log(`[${operation}] Transaction started for Row ${rowId}.`);

        // 2. Fetch Row
        const row = await Row.findById(rowId).session(session);
        if (!row) {
            throw new Error(`Row ${rowId} not found.`);
        }
        console.log(`[${operation}] Found Row ${row.rowNumber}.`);

        const kartIdToUnassign = row.currentKartId;

        // 3. Check if row is already empty
        if (!kartIdToUnassign) {
            console.log(`[${operation}] Row ${row.rowNumber} is already empty.`);
            await abortTransactionAndEndSession(session, operation);
            res.status(200).json({ message: "Row is already empty.", row });
            return;
        }
        console.log(`[${operation}] Row contains Kart ${kartIdToUnassign}. Proceeding with unassignment.`);

        // 4. Update Row
        row.currentKartId = null;

        // 5. Update Kart
        const updatedKart = await Kart.findByIdAndUpdate(
            kartIdToUnassign,
            { $set: { currentRowId: null, status: 'available' } }, // Set status to available
            { new: true, session }
        );

        if (!updatedKart) {
            // Kart existed on row but not found? Data inconsistency. Warn but proceed.
            console.warn(`[${operation}] Kart ${kartIdToUnassign} assigned to row ${row.rowNumber} but not found during update. Row will still be emptied.`);
        } else {
            console.log(`[${operation}] Kart ${kartIdToUnassign} updated (currentRowId=null, status=available).`);
        }

        // 6. Create Log Entry
        const logEntry = new AssignmentLog({
            eventType: 'UNASSIGNED_FROM_ROW',
            kartId: kartIdToUnassign,
            previousRowId: row._id, // Log the row it left
            timestamp: new Date(),
        });

        // 7. Save changes
        await Promise.all([row.save({ session }), logEntry.save({ session })]);
        console.log(`[${operation}] Row and Log saved.`);

        // 8. Commit Transaction
        await session.commitTransaction();
        console.log(`[${operation}] Transaction committed.`);

        // 9. Send WebSocket updates
        sendWebSocketMessage({ event: ROW_UPDATE_EVENT, payload: { type: "update", row: row.toObject() } });
        if (updatedKart) sendWebSocketMessage({ event: KART_UPDATE_EVENT, payload: { type: "update", kart: updatedKart.toObject() } });
        console.log(`[${operation}] WebSocket messages sent.`);

        res.status(200).json({ message: `Row ${row.rowNumber} emptied successfully.`, row: row.toObject(), kart: updatedKart?.toObject() });

    } catch (error: unknown) {
        await abortTransactionAndEndSession(session, operation, error);
        sendErrorResponse(res, `Error unassigning kart from row: ${error instanceof Error ? error.message : "Unknown error"}`, 500, error);
    } finally {
        await safelyEndSession(session);
    }
};

// --- Smart Swap Kart between a Row and a Team (Works with both regular and apex teams) ---
export const swapKartWithTeamSmart = async (req: Request, res: Response): Promise<void> => {
  let session: ClientSession | null = null;
  const operation = "swapKartWithTeamSmart";
  try {
    session = await mongoose.startSession();
    session.startTransaction();
    console.log(`[${operation}] Transaction started.`);

    const { rowId, teamId } = req.params;
    const { sessionId } = req.query; // Optional session ID for apex mode

    // 1. Validate IDs
    if (!rowId || !teamId || !isValidObjectId(rowId)) {
      await abortTransactionAndEndSession(session, `${operation}Validation`);
      return sendErrorResponse(
        res,
        "Valid Row ID and Team ID required for swap.",
        400
      );
    }

    // Note: teamId validation is done later based on whether it's apex or regular mode
    console.log(`[${operation}] IDs validated: Row=${rowId}, Team=${teamId}, Session=${sessionId}`);

    // 2. Fetch Row (always regular)
    const row = await Row.findById(rowId).session(session);
    if (!row) {
      throw new Error(`Row with ID ${rowId} not found.`);
    }

    // 3. Determine if this is an apex team or regular team
    let team: ITeam | null = null;
    let apexTeam: any = null;
    let isApexMode = false;

    if (sessionId) {
      // Try apex mode first if sessionId is provided
      isApexMode = true;

      // First try to find by ObjectId (if teamId is a valid ObjectId)
      if (isValidObjectId(teamId)) {
        apexTeam = await ApexTeam.findOne({
          sessionId: sessionId,
          _id: teamId
        }).session(session);
      }

      // If not found by ObjectId, try by teamId string field
      if (!apexTeam) {
        apexTeam = await ApexTeam.findOne({
          sessionId: sessionId,
          teamId: teamId
        }).session(session);
      }

      if (!apexTeam) {
        // If not found in apex, try regular team mode
        isApexMode = false;
        if (!isValidObjectId(teamId)) {
          throw new Error(`Team with ID ${teamId} not found in apex session ${sessionId} and is not a valid ObjectId for regular teams.`);
        }
        team = await Team.findById(teamId).session(session);
        if (!team) {
          throw new Error(`Team with ID ${teamId} not found in regular teams either.`);
        }
        console.log(`[${operation}] Regular team ${team.name} found (fallback from apex mode).`);
      } else {
        console.log(`[${operation}] Apex team ${apexTeam.name} found.`);
      }
    } else {
      // Regular team mode (no sessionId provided)
      if (!isValidObjectId(teamId)) {
        throw new Error(`Invalid team ID format: ${teamId}. For apex teams, provide sessionId parameter.`);
      }
      team = await Team.findById(teamId).session(session);
      if (!team) {
        throw new Error(`Team with ID ${teamId} not found.`);
      }
      console.log(`[${operation}] Regular team ${team.name} found.`);
    }

    const rowKartId = row.currentKartId;
    let teamKartId: any = null;

    if (isApexMode && apexTeam) {
      // ⚠️  CRITICAL: In apex mode, we work with apex karts, not regular karts!
      // Use the team's currentKartId (now an ObjectId reference to ApexKart)
      if (apexTeam.currentKartId) {
        teamKartId = apexTeam.currentKartId;
        console.log(`[${operation}] Apex team ${apexTeam.name} has kart ${teamKartId}`);
      } else {
        console.log(`[${operation}] Apex team ${apexTeam.name} has no current kart`);
      }
    } else if (team) {
      teamKartId = team.currentKartId;
    }

    console.log(`[${operation}] Row Kart ID: ${rowKartId}, Team Kart ID: ${teamKartId}`);

    // 4. Handle Edge Cases
    if (rowKartId?.toString() === teamKartId?.toString()) {
      console.log(`[${operation}] Info: Row and Team already have the same kart or both are empty. No swap needed.`);
      await abortTransactionAndEndSession(session, operation);
      sendErrorResponse(res, "Row and Team already have the same kart.", 200);
      return;
    }

    // 5. Perform the swap logic (similar to original function)
    const now = new Date();
    const logEntriesToSave: IAssignmentLog[] = [];
    const kartsToSave: IKart[] = [];
    const updatedKartIds: string[] = [];
    let teamKart: IKart | null = null;
    let rowKart: IKart | null = null;

    // Handle team kart moving to row
    if (teamKartId) {
      if (isApexMode) {
        // ⚠️  CRITICAL: In apex mode, work with apex karts!
        teamKart = await ApexKart.findById(teamKartId).session(session);
        if (!teamKart) {
          throw new Error(`Apex kart ${teamKartId} (from team) not found.`);
        }

        // Update apex kart to be in pit row
        (teamKart as any).currentTeamId = null;
        (teamKart as any).currentRowId = row._id; // Set the row reference
        (teamKart as any).status = "in_pit_row";
        await (teamKart as any).save({ session });

        console.log(`[${operation}] Moved apex kart ${(teamKart as any).kartNumber} to pit row`);
      } else {
        teamKart = await Kart.findById(teamKartId).session(session);
        if (!teamKart) {
          throw new Error(`Kart ${teamKartId} (from team) not found.`);
        }

        teamKart.currentTeamId = null;
        teamKart.currentRowId = row._id;
        teamKart.status = "in_pit_row";
        kartsToSave.push(teamKart);
      }

      // Log kart moving TO row FROM team
      logEntriesToSave.push(
        new AssignmentLog({
          eventType: "ASSIGNED_TO_ROW",
          kartId: teamKart._id,
          rowId: row._id,
          previousTeamId: isApexMode ? undefined : team?._id,
          timestamp: now,
        })
      );

      updatedKartIds.push(teamKartId.toString());
    }

    // Handle row kart moving to team
    if (rowKartId) {
      if (isApexMode) {
        // ⚠️  CRITICAL: In apex mode, row might contain apex karts!
        // Try to find as apex kart first
        rowKart = await ApexKart.findById(rowKartId).session(session);
        if (!rowKart) {
          // Fallback to regular kart for backwards compatibility
          rowKart = await Kart.findById(rowKartId).session(session);
        }
        if (!rowKart) {
          throw new Error(`Kart ${rowKartId} (from row) not found in apex or regular karts.`);
        }

        // Update apex kart to be with team
        if ((rowKart as any).sessionId) {
          // This is an apex kart
          (rowKart as any).currentTeamId = apexTeam._id; // Use apex team ObjectId
          (rowKart as any).currentRowId = null; // Clear row reference
          (rowKart as any).status = "racing";
          await (rowKart as any).save({ session });
          console.log(`[${operation}] Moved apex kart ${(rowKart as any).kartNumber} to apex team ${apexTeam.name}`);
        } else {
          // This is a regular kart
          rowKart.currentTeamId = null; // Don't assign regular kart to apex team
          rowKart.currentRowId = null;
          rowKart.status = "on_track";
          kartsToSave.push(rowKart);
        }
      } else {
        rowKart = await Kart.findById(rowKartId).session(session);
        if (!rowKart) {
          throw new Error(`Kart ${rowKartId} (from row) not found.`);
        }

        rowKart.currentTeamId = team?._id || null;
        rowKart.currentRowId = null;
        rowKart.status = "on_track";
        kartsToSave.push(rowKart);
      }

      // Log kart moving TO team FROM row
      logEntriesToSave.push(
        new AssignmentLog({
          eventType: "ASSIGNED_TO_TEAM",
          kartId: rowKart._id,
          teamId: isApexMode ? undefined : team?._id,
          previousRowId: row._id,
          timestamp: now,
        })
      );

      updatedKartIds.push(rowKartId.toString());
    }

    // 6. Update Row and Team references
    row.currentKartId = teamKart?._id ?? null;

    if (!isApexMode && team) {
      team.currentKartId = rowKart?._id ?? undefined;
    } else if (isApexMode && apexTeam) {
      // ⚠️  CRITICAL: Update apex team's kart reference (now ObjectId)
      if (rowKart) {
        apexTeam.currentKartId = rowKart._id; // Store ObjectId reference
        await apexTeam.save({ session });
        console.log(`[${operation}] Updated apex team ${apexTeam.name} to use kart ${rowKart._id}`);
      } else {
        apexTeam.currentKartId = null;
        await apexTeam.save({ session });
        console.log(`[${operation}] Cleared kart reference for apex team ${apexTeam.name}`);
      }
    }

    // 7. Save all changes
    await row.save({ session });
    if (!isApexMode && team) {
      await team.save({ session });
    }

    if (kartsToSave.length > 0) {
      await Kart.bulkSave(kartsToSave, { session });
    }
    if (logEntriesToSave.length > 0) {
      await AssignmentLog.insertMany(logEntriesToSave, { session });
    }

    await session.commitTransaction();
    console.log(`[${operation}] Transaction committed successfully.`);

    // 8. Send WebSocket notifications
    sendWebSocketMessage({ event: ROW_UPDATE_EVENT, payload: { type: "update", row: row.toObject() } });
    if (!isApexMode && team) {
      sendWebSocketMessage({ event: TEAM_UPDATE_EVENT, payload: { type: "update", team: team.toObject() } });
    }
    if (updatedKartIds.length > 0) {
      sendWebSocketMessage({ event: KART_UPDATE_EVENT, payload: { type: "update", kartIds: updatedKartIds } });
    }

    // 9. Send response
    res.status(200).json({
      message: `Swap successful between Row ${row.rowNumber} and ${isApexMode ? 'Apex ' : ''}Team ${isApexMode ? apexTeam?.name : team?.name}`,
      updatedRow: row,
      updatedTeam: isApexMode ? apexTeam : team,
      originalRowKart: teamKart,
      originalTeamKart: rowKart,
    });

  } catch (error: unknown) {
    await abortTransactionAndEndSession(session, operation, error);
    const statusCode =
      error instanceof Error &&
      (error.message.includes("not found") ||
        error.message.includes("unexpectedly assigned"))
        ? 404
        : error instanceof Error &&
            error.message.includes("already have the same kart")
          ? 200
          : 500;
    sendErrorResponse(
      res,
      `Error during smart kart swap: ${error instanceof Error ? error.message : "Unknown error"}`,
      statusCode,
      error
    );
  } finally {
    await safelyEndSession(session);
  }
};
