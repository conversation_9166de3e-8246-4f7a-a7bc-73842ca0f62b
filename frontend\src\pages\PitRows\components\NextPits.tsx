import { IonBadge, IonButton, IonButtons, IonCard, IonCardContent, IonCardHeader, IonCardSubtitle, IonCardTitle, IonCol, IonContent, IonGrid, IonHeader, IonIcon, IonLabel, IonModal, IonRow, IonText, IonTitle, IonToolbar } from "@ionic/react";
import React, { useState } from "react";
import "./common.css"
import "./NextPits.css"
import { close, scan } from 'ionicons/icons';

interface table_nextPits {
    id: number;
    team: string;
    kart: number;
    avg: string;
    best: string;
    pitTime: string;
}


interface tableProps {
    data: table_nextPits[];
}

const TableRow: React.FC<{ item: table_nextPits }> = ({ item }) => (
    <IonRow className="data-row">
        <IonCol size="3" size-sm="1">
            <IonBadge color="primary">{item.id}</IonBadge>
        </IonCol>
        <IonCol size="6" size-sm="3">
            <IonText color="dark">{item.team}</IonText>
        </IonCol>
        <IonCol size="3" size-sm="2">
            <IonText color="medium">{item.kart}</IonText>
        </IonCol>
        <IonCol size="4" size-sm="2">
            <IonText color="success">{item.avg}</IonText>
        </IonCol>
        <IonCol size="4" size-sm="2">
            <IonText color="tertiary">{item.best}</IonText>
        </IonCol>
        <IonCol size="4" size-sm="2">
            <IonText color="warning">{item.pitTime}</IonText>
        </IonCol>
    </IonRow>
);

const FullTableModal: React.FC<{ isOpen: boolean; onClose: () => void; data: table_nextPits[] }> = ({ isOpen, onClose, data }) => (
    <IonModal isOpen={isOpen} onDidDismiss={onClose}>
        <IonHeader>
            <IonToolbar>
                <IonTitle>Next Pits</IonTitle>
                <IonButtons slot="end">
                    <IonButton onClick={onClose}>
                        <IonIcon icon={close} />
                    </IonButton>
                </IonButtons>
            </IonToolbar>
        </IonHeader>
        <IonContent>
            <IonGrid className="next-pits-table">
                <IonRow className="header-row">
                    <IonCol size="3" size-sm="1">ID</IonCol>
                    <IonCol size="6" size-sm="3">Team</IonCol>
                    <IonCol size="3" size-sm="2">Kart</IonCol>
                    <IonCol size="4" size-sm="2">Avg</IonCol>
                    <IonCol size="4" size-sm="2">Best</IonCol>
                    <IonCol size="4" size-sm="2">Pit Time</IonCol>
                </IonRow>
                {data.map((item) => (
                    <TableRow key={item.id} item={item} />
                ))}
            </IonGrid>
        </IonContent>
    </IonModal>
);

const NextPits: React.FC<tableProps> = ({ data = [] }) => {
    const [showFullTable, setShowFullTable] = useState(false);

    return (
        <IonCard>
            <IonCardHeader className="next-pits-header">
                <div className="next-pits-title-container">
                    <IonCardTitle>Next Pits</IonCardTitle>
                    <IonButton fill="outline"  onClick={() => setShowFullTable(true)}>
                        <IonIcon icon={scan}/>
                    </IonButton>
                </div>
            </IonCardHeader>

            <IonCardContent>
                <IonGrid className="ion-no-padding next-pits-table">
                    <IonRow className="header-row">
                        <IonCol size="2" size-sm="1">ID</IonCol>
                        <IonCol size="4" size-sm="3">Team</IonCol>
                        <IonCol size="2" size-sm="2">Kart</IonCol>
                        <IonCol size="4" size-sm="2">Avg</IonCol>
                        <IonCol size="4" size-sm="2">Best</IonCol>
                        <IonCol size="4" size-sm="2">Stint Time</IonCol>
                    </IonRow>
                    {data.slice(0, 5).map((item) => (
                        <TableRow key={item.id} item={item}/>
                    ))}
                </IonGrid>
            </IonCardContent>
            <FullTableModal isOpen={showFullTable} onClose={() => setShowFullTable(false)} data={data} />
        </IonCard>
    );
};

export default NextPits;