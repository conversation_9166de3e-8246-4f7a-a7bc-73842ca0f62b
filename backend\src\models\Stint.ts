// d:/Desktop/ManulilacRaceplanner/backend/src/models/Stint.ts
import mongoose, { Schema, Document, Types } from 'mongoose';

export interface IStint extends Document {
  raceStrategyId: Types.ObjectId; // Link to the parent RaceStrategy
  stintNumber: number;
  startTime: number; // Seconds from race start
  endTime: number;   // Seconds from race start
  pitEndTime: number | null; // Seconds from race start, null if last stint
  duration: number;  // Seconds
  startLap: number;
  endLap: number;
  laps: number;
  isPitWindowValid: boolean; // True if the pit stop *after* this stint is valid
  isExtraStint: boolean; // True for splash or final special stints
  isUserModified?: boolean; // Added flag
  actualPitEntryTime?: number | null; // Added field for user input
  actualPitDuration?: number | null; // Added field for user input
  actualStintDuration?: number | null; // Added field for calculated actual duration
  // Add these new fields
  plannedStintDuration?: number;
  plannedPitDuration?: number;
  stintDurationDiff?: number;
  pitDurationDiff?: number;
}

const StintSchema: Schema = new Schema(
  {
    raceStrategyId: {
      type: Schema.Types.ObjectId,
      ref: 'RaceStrategy',
      required: true,
      index: true,
    },
    stintNumber: { type: Number, required: true },
    startTime: { type: Number, required: true },
    endTime: { type: Number, required: true },
    pitEndTime: { type: Number, default: null }, // Can be null for the last stint
    duration: { type: Number, required: true },
    startLap: { type: Number, required: true },
    endLap: { type: Number, required: true },
    laps: { type: Number, required: true },
    isPitWindowValid: { type: Boolean, required: true },
    isExtraStint: { type: Boolean, default: false },
    isUserModified: { // Flag indicating if this stint's actuals were manually set
      type: Boolean,
      default: false,
    },
    actualPitEntryTime: { // User-provided actual pit entry time
      type: Number ,
      default: null,
    },
    actualPitDuration: { // User-provided actual pit duration
      type: Number, // Store as seconds
      default: null,
    },
    actualStintDuration: { // Calculated actual stint duration based on actualPitEntryTime
        type: Number,
        default: null,
    },
    // Add these new fields
    plannedStintDuration: { type: Number },
    plannedPitDuration: { type: Number },
    stintDurationDiff: { type: Number },
    pitDurationDiff: { type: Number }
  },
  {
    timestamps: true, // Adds createdAt and updatedAt
  }
);

// Compound index for querying stints of a specific strategy in order
StintSchema.index({ raceStrategyId: 1, stintNumber: 1 });

export default mongoose.model<IStint>('Stint', StintSchema);

// This interface matches the structure of your frontend StintInfo
// and the JSON output you provided.
export interface StintCalculationInfo {
  stintNumber: number;
  startTime: number;
  endTime: number;
  pitEndTime: number | null;
  duration: number;
  startLap: number;
  endLap: number;
  laps: number;
  isPitWindowValid: boolean;
  isExtraStint: boolean;
  isUserModified?: boolean; // Add this property
  actualPitEntryTime?: number | null; // Seconds from race start (number from backend)
  actualPitDuration?: number | null; // Seconds
  actualStintDuration?: number | null; // Seconds
}
