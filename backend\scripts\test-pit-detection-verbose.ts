#!/usr/bin/env ts-node

/**
 * Test script to verify verbose pit detection logging
 */

import mongoose from 'mongoose';
import { ApexParserSimple } from '../src/services/apexParserSimple';
import { ApexSession, ApexTeam, ApexKart, ApexCompetitor, ApexPitStop } from '../src/models/ApexModels';

// Test configuration
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/raceplanner';

async function connectToDatabase() {
  try {
    await mongoose.connect(MONGODB_URI);
    console.log('✅ Connected to MongoDB');
  } catch (error) {
    console.error('❌ Failed to connect to MongoDB:', error);
    process.exit(1);
  }
}

async function cleanupTestData() {
  console.log('🧹 Cleaning up test data...');
  await ApexSession.deleteMany({ title1: { $regex: /Pit Detection Test/ } });
  await ApexTeam.deleteMany({ name: { $regex: /TEST/ } });
  await ApexKart.deleteMany({ kartNumber: { $in: [99, 88] } });
  await ApexCompetitor.deleteMany({ name: { $regex: /TEST/ } });
  await ApexPitStop.deleteMany({});
}

async function testPitDetection() {
  console.log('\n🔍 Testing Pit Detection with Verbose Logging...\n');

  const parser = new ApexParserSimple({ enableLogging: true });

  // Test session creation with grid
  const gridMessage = `init|r|
title1||Pit Detection Test
title2||Verbose Logging Test
track||Test Track
grid||<tbody><tr data-id="r0"><td data-id="c1" data-type="sta">N/A</td><td data-id="c2" data-type="rk">Rnk</td><td data-id="c3" data-type="no">Kart</td><td data-id="c5" data-type="dr">Team</td><td data-id="c9" data-type="llp">Last lap</td><td data-id="c15" data-type="pit">Pits</td></tr><tr data-id="r17788"><td data-id="r17788c1">N/A</td><td data-id="r17788c2">1</td><td data-id="r17788c3">99</td><td data-id="r17788c5">TEST TEAM 1</td><td data-id="r17788c9"></td><td data-id="r17788c15">0</td></tr><tr data-id="r17799"><td data-id="r17799c1">N/A</td><td data-id="r17799c2">2</td><td data-id="r17799c3">88</td><td data-id="r17799c5">TEST TEAM 2</td><td data-id="r17799c9"></td><td data-id="r17799c15">0</td></tr></tbody>`;

  console.log('📊 Sending grid message...');
  await parser.parseMessage(gridMessage);

  // Wait for database operations to complete
  await new Promise(resolve => setTimeout(resolve, 1000));

  console.log('\n🏁 Testing pit status messages...\n');

  // Test numeric pit status (like your real logs)
  console.log('--- Testing numeric pit status: 6 ---');
  await parser.parseMessage('r17788c15|pit|6');
  
  await new Promise(resolve => setTimeout(resolve, 500));

  console.log('\n--- Testing numeric pit status: 0 (exit) ---');
  await parser.parseMessage('r17788c15|pit|0');

  await new Promise(resolve => setTimeout(resolve, 500));

  // Test string pit status
  console.log('\n--- Testing string pit status: IN ---');
  await parser.parseMessage('r17799c15|pit|IN');

  await new Promise(resolve => setTimeout(resolve, 500));

  console.log('\n--- Testing string pit status: OUT ---');
  await parser.parseMessage('r17799c15|pit|OUT');

  await new Promise(resolve => setTimeout(resolve, 1000));

  // Check database results
  console.log('\n📊 Checking database results...');
  
  const sessions = await ApexSession.find({ title1: { $regex: /Pit Detection Test/ } }).lean();
  console.log(`Sessions created: ${sessions.length}`);
  
  if (sessions.length > 0) {
    const session = sessions[0];
    console.log(`✅ Session: ${session._id} - ${session.title1}`);
    
    const competitors = await ApexCompetitor.find({ sessionId: session._id }).lean();
    console.log(`Competitors: ${competitors.length}`);
    
    const pitStops = await ApexPitStop.find({ sessionId: session._id }).lean();
    console.log(`Pit stops created: ${pitStops.length}`);
    
    if (pitStops.length > 0) {
      console.log('✅ Pit stop details:');
      pitStops.forEach((pit, index) => {
        console.log(`   ${index + 1}. Competitor: ${pit.competitorId}, In: ${pit.pitInTime}, Out: ${pit.pitOutTime || 'ACTIVE'}, Duration: ${pit.pitDuration || 'N/A'}s`);
      });
    } else {
      console.log('❌ No pit stops were created!');
    }
    
    return {
      sessionId: session._id,
      competitorsCreated: competitors.length,
      pitStopsCreated: pitStops.length
    };
  } else {
    console.log('❌ No sessions were created');
    return {
      sessionId: null,
      competitorsCreated: 0,
      pitStopsCreated: 0
    };
  }
}

async function main() {
  try {
    await connectToDatabase();
    await cleanupTestData();
    
    const results = await testPitDetection();
    
    console.log('\n📊 Test Results Summary:');
    console.log(`   Session ID: ${results.sessionId}`);
    console.log(`   Competitors Created: ${results.competitorsCreated}`);
    console.log(`   Pit Stops Created: ${results.pitStopsCreated}`);
    
    if (results.pitStopsCreated > 0) {
      console.log('\n✅ Pit detection test passed!');
    } else {
      console.log('\n❌ Pit detection test failed - no pit stops created!');
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  } finally {
    await cleanupTestData();
    await mongoose.disconnect();
    console.log('🔌 Disconnected from MongoDB');
  }
}

// Run the test
if (require.main === module) {
  main().catch(console.error);
}
