import React, { useState, useEffect, useRef } from 'react';
import { useDataContext } from '../context/DataContext';
import apiService from '../services/apiService';
import { liveTimingWebSocket } from '../services/liveTimingWebSocket';

interface LiveCompetitorData {
  competitorId: string;
  teamName: string;
  kartNumber: number;
  position: number;
  lastLapTime: string;
  bestLapTime: string;
  totalLaps: number;
  gap: string;
  interval: string;
  status: string;
  sector1: string;
  sector2: string;
  sector3: string;
  onTrackTime: string;
  pitStops: number;
  nationality: string;
  lastUpdate: Date;
}



const LiveTimingPage: React.FC = () => {
  const { selectedSession } = useDataContext();
  const [liveData, setLiveData] = useState<Map<string, LiveCompetitorData>>(new Map());
  const [isConnected, setIsConnected] = useState(false);
  const [lastUpdate, setLastUpdate] = useState<Date | null>(null);
  const [messageCount, setMessageCount] = useState(0);
  const tableRef = useRef<HTMLDivElement>(null);

  // Initialize live data from current session
  useEffect(() => {
    if (selectedSession) {
      // Load initial data from API
      loadInitialData();
    }
  }, [selectedSession]);

  // Setup websocket connection
  useEffect(() => {
    // Connect to websocket
    liveTimingWebSocket.connect();

    // Setup event handlers
    const handleLapRecorded = (lap: any) => {
      setMessageCount(prev => prev + 1);
      setLastUpdate(new Date());
      updateCompetitorLap(lap);
    };

    const handlePitStopRecorded = (pitStop: any) => {
      setMessageCount(prev => prev + 1);
      setLastUpdate(new Date());
      updateCompetitorPitStop(pitStop);
    };

    const handleApexData = (data: any) => {
      setMessageCount(prev => prev + 1);
      setLastUpdate(new Date());
      processApexData(data);
    };

    const handleConnected = () => {
      setIsConnected(true);
    };

    const handleDisconnected = () => {
      setIsConnected(false);
    };

    // Register event handlers
    liveTimingWebSocket.on('lapRecorded', handleLapRecorded);
    liveTimingWebSocket.on('pitStopRecorded', handlePitStopRecorded);
    liveTimingWebSocket.on('apexData', handleApexData);
    liveTimingWebSocket.on('connected', handleConnected);
    liveTimingWebSocket.on('disconnected', handleDisconnected);

    // Cleanup on unmount
    return () => {
      liveTimingWebSocket.off('lapRecorded', handleLapRecorded);
      liveTimingWebSocket.off('pitStopRecorded', handlePitStopRecorded);
      liveTimingWebSocket.off('apexData', handleApexData);
      liveTimingWebSocket.off('connected', handleConnected);
      liveTimingWebSocket.off('disconnected', handleDisconnected);
      liveTimingWebSocket.disconnect();
    };
  }, []);

  // Load initial data from API
  const loadInitialData = async () => {
    if (!selectedSession) return;

    try {
      // Fetch teams data
      const teamsResponse = await apiService.apex.getTeams(selectedSession._id);

      // Initialize live data from teams
      const initialData = new Map<string, LiveCompetitorData>();

      teamsResponse.forEach((team: any) => {
        initialData.set(team.teamId, {
          competitorId: team.teamId,
          teamName: team.name,
          kartNumber: team.kartNumber,
          position: team.position || 0,
          lastLapTime: team.lastLapTimeFormatted || '-',
          bestLapTime: team.bestLapTimeFormatted || '-',
          totalLaps: team.totalLaps || 0,
          gap: '-',
          interval: '-',
          status: team.status || 'Unknown',
          sector1: '-',
          sector2: '-',
          sector3: '-',
          onTrackTime: '-',
          pitStops: 0,
          nationality: team.nationality || 'Unknown',
          lastUpdate: new Date()
        });
      });

      setLiveData(initialData);
      console.log(`📊 Loaded initial data for ${initialData.size} competitors`);

    } catch (error) {
      console.error('Error loading initial data:', error);
    }
  };

  // Handle lap recorded from websocket
  const updateCompetitorLap = (lap: any) => {
    setLiveData(prev => {
      const newData = new Map(prev);
      const existing = newData.get(lap.competitorId);

      if (existing) {
        newData.set(lap.competitorId, {
          ...existing,
          lastLapTime: lap.lapTimeFormatted,
          bestLapTime: lap.isBestLap ? lap.lapTimeFormatted : existing.bestLapTime,
          totalLaps: Math.max(lap.lapNumber, existing.totalLaps),
          lastUpdate: new Date()
        });

        console.log(`🏁 Lap update: ${existing.teamName} - ${lap.lapTimeFormatted}`);
      }

      return newData;
    });
  };

  // Handle pit stop recorded from websocket
  const updateCompetitorPitStop = (pitStop: any) => {
    setLiveData(prev => {
      const newData = new Map(prev);
      const existing = newData.get(pitStop.competitorId);

      if (existing) {
        const updatedData = { ...existing, lastUpdate: new Date() };

        if (pitStop.type === 'entry') {
          updatedData.status = 'pit';
          console.log(`🏁 Pit entry: ${existing.teamName}`);
        } else if (pitStop.type === 'exit') {
          updatedData.status = 'active';
          updatedData.pitStops = existing.pitStops + 1;
          console.log(`🏁 Pit exit: ${existing.teamName} (${pitStop.pitDuration?.toFixed(1)}s)`);
        }

        newData.set(pitStop.competitorId, updatedData);
      }

      return newData;
    });
  };

  // Process apex data messages from websocket
  const processApexData = (data: any) => {
    if (!data.parsedMessage || !data.parsedMessage.data) return;

    // Process each field in the parsed message
    Object.entries(data.parsedMessage.data).forEach(([key, value]: [string, any]) => {
      if (key.startsWith('r') && key.includes('c')) {
        const parts = key.split('c');
        if (parts.length >= 2) {
          const competitorId = parts[0].replace('r', '');
          const columnId = parts[1];

          updateCompetitorField(competitorId, columnId, value.value);
        }
      }
    });
  };

  // Update competitor field based on column type
  const updateCompetitorField = (competitorId: string, columnId: string, value: string) => {
    setLiveData(prev => {
      const newData = new Map(prev);
      const existing = newData.get(competitorId);

      if (!existing) return prev;

      const updated = { ...existing, lastUpdate: new Date() };

      // Map column IDs to fields based on grid header types
      switch (columnId) {
        case '9': // llp - last lap
          if (value.includes(':')) {
            updated.lastLapTime = value;
          }
          break;
        case '12': // blp - best lap
          if (value.includes(':')) {
            updated.bestLapTime = value;
          }
          break;
        case '13': { // tlp - total laps
          const laps = parseInt(value);
          if (!isNaN(laps)) {
            updated.totalLaps = laps;
          }
          break;
        }
        case '2': { // rk - rank/position
          const position = parseInt(value);
          if (!isNaN(position)) {
            updated.position = position;
          }
          break;
        }
        case '10': // gap
          updated.gap = value;
          break;
        case '11': // int - interval
          updated.interval = value;
          break;
        case '1': // sta - status
          updated.status = value;
          break;
        case '6': // s1 - sector 1
          updated.sector1 = value;
          break;
        case '7': // s2 - sector 2
          updated.sector2 = value;
          break;
        case '8': // s3 - sector 3
          updated.sector3 = value;
          break;
        case '14': // otr - on track time
          updated.onTrackTime = value;
          break;
        case '15': { // pit - pit stops
          const pits = parseInt(value);
          if (!isNaN(pits)) {
            updated.pitStops = pits;
          }
          break;
        }
      }

      newData.set(competitorId, updated);
      return newData;
    });
  };

  // Control functions
  const toggleConnection = () => {
    if (isConnected) {
      liveTimingWebSocket.disconnect();
    } else {
      liveTimingWebSocket.connect();
    }
  };

  const refreshNow = () => {
    loadInitialData();
  };



  // Sort competitors by position
  const sortedCompetitors = Array.from(liveData.values()).sort((a, b) => {
    if (a.position === 0 && b.position === 0) return a.kartNumber - b.kartNumber;
    if (a.position === 0) return 1;
    if (b.position === 0) return -1;
    return a.position - b.position;
  });

  const getRowClassName = (competitor: LiveCompetitorData) => {
    const timeSinceUpdate = Date.now() - competitor.lastUpdate.getTime();
    
    if (timeSinceUpdate < 2000) {
      return 'bg-green-50 border-green-200'; // Recently updated
    } else if (timeSinceUpdate < 10000) {
      return 'bg-yellow-50 border-yellow-200'; // Somewhat recent
    } else {
      return 'bg-gray-50 border-gray-200'; // Stale data
    }
  };

  const formatTime = (timeStr: string) => {
    if (!timeStr || timeStr === '-') return '-';
    return timeStr;
  };

  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      {/* Header */}
      <div className="mb-6">
        <div className="flex justify-between items-center mb-4">
          <h1 className="text-3xl font-bold text-gray-900">Live Timing</h1>
          <div className="flex items-center space-x-4">
            <button
              onClick={toggleConnection}
              className={`flex items-center space-x-2 px-4 py-2 rounded-lg font-medium ${
                isConnected
                  ? 'bg-green-100 text-green-800 hover:bg-green-200'
                  : 'bg-red-100 text-red-800 hover:bg-red-200'
              }`}
            >
              <div className={`w-2 h-2 rounded-full ${
                isConnected ? 'bg-green-500' : 'bg-red-500'
              }`}></div>
              <span className="text-sm">
                {isConnected ? 'Connected' : 'Disconnected'}
              </span>
            </button>
            <button
              onClick={refreshNow}
              className="px-4 py-2 bg-blue-100 text-blue-800 rounded-lg hover:bg-blue-200 text-sm font-medium"
            >
              Refresh Data
            </button>
            <div className="text-sm text-gray-600">
              Messages: {messageCount}
            </div>
            {lastUpdate && (
              <div className="text-sm text-gray-600">
                Last: {lastUpdate.toLocaleTimeString()}
              </div>
            )}
          </div>
        </div>

        {selectedSession && (
          <div className="bg-white p-4 rounded-lg shadow">
            <h2 className="text-xl font-semibold text-gray-800">
              {selectedSession.title1} - {selectedSession.title2}
            </h2>
            <p className="text-gray-600">{selectedSession.track}</p>
          </div>
        )}
      </div>

      {/* Live Timing Table */}
      <div ref={tableRef} className="bg-white rounded-lg shadow overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-800 text-white">
              <tr>
                <th className="px-3 py-3 text-left text-xs font-medium uppercase tracking-wider">Pos</th>
                <th className="px-3 py-3 text-left text-xs font-medium uppercase tracking-wider">Kart</th>
                <th className="px-3 py-3 text-left text-xs font-medium uppercase tracking-wider">Team</th>
                <th className="px-3 py-3 text-left text-xs font-medium uppercase tracking-wider">Last Lap</th>
                <th className="px-3 py-3 text-left text-xs font-medium uppercase tracking-wider">Best Lap</th>
                <th className="px-3 py-3 text-left text-xs font-medium uppercase tracking-wider">Laps</th>
                <th className="px-3 py-3 text-left text-xs font-medium uppercase tracking-wider">Gap</th>
                <th className="px-3 py-3 text-left text-xs font-medium uppercase tracking-wider">Int</th>
                <th className="px-3 py-3 text-left text-xs font-medium uppercase tracking-wider">S1</th>
                <th className="px-3 py-3 text-left text-xs font-medium uppercase tracking-wider">S2</th>
                <th className="px-3 py-3 text-left text-xs font-medium uppercase tracking-wider">S3</th>
                <th className="px-3 py-3 text-left text-xs font-medium uppercase tracking-wider">Status</th>
                <th className="px-3 py-3 text-left text-xs font-medium uppercase tracking-wider">Pits</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {sortedCompetitors.map((competitor) => (
                <tr 
                  key={competitor.competitorId}
                  className={`transition-colors duration-300 ${getRowClassName(competitor)}`}
                >
                  <td className="px-3 py-2 whitespace-nowrap text-sm font-medium text-gray-900">
                    {competitor.position || '-'}
                  </td>
                  <td className="px-3 py-2 whitespace-nowrap text-sm font-bold text-blue-600">
                    {competitor.kartNumber}
                  </td>
                  <td className="px-3 py-2 whitespace-nowrap text-sm text-gray-900 max-w-xs truncate">
                    {competitor.teamName}
                  </td>
                  <td className="px-3 py-2 whitespace-nowrap text-sm font-mono text-gray-900">
                    {formatTime(competitor.lastLapTime)}
                  </td>
                  <td className="px-3 py-2 whitespace-nowrap text-sm font-mono text-purple-600 font-semibold">
                    {formatTime(competitor.bestLapTime)}
                  </td>
                  <td className="px-3 py-2 whitespace-nowrap text-sm text-gray-900">
                    {competitor.totalLaps}
                  </td>
                  <td className="px-3 py-2 whitespace-nowrap text-sm font-mono text-gray-700">
                    {competitor.gap}
                  </td>
                  <td className="px-3 py-2 whitespace-nowrap text-sm font-mono text-gray-700">
                    {competitor.interval}
                  </td>
                  <td className="px-3 py-2 whitespace-nowrap text-sm font-mono text-gray-600">
                    {competitor.sector1}
                  </td>
                  <td className="px-3 py-2 whitespace-nowrap text-sm font-mono text-gray-600">
                    {competitor.sector2}
                  </td>
                  <td className="px-3 py-2 whitespace-nowrap text-sm font-mono text-gray-600">
                    {competitor.sector3}
                  </td>
                  <td className="px-3 py-2 whitespace-nowrap text-sm">
                    <span className={`px-2 py-1 text-xs rounded-full ${
                      competitor.status === 'active' ? 'bg-green-100 text-green-800' :
                      competitor.status === 'pit' ? 'bg-yellow-100 text-yellow-800' :
                      'bg-gray-100 text-gray-800'
                    }`}>
                      {competitor.status}
                    </span>
                  </td>
                  <td className="px-3 py-2 whitespace-nowrap text-sm text-gray-900">
                    {competitor.pitStops}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {sortedCompetitors.length === 0 && (
        <div className="text-center py-12">
          <p className="text-gray-500 text-lg">No competitor data available</p>
          <p className="text-gray-400 text-sm mt-2">
            {!selectedSession ? 'Please select a session first' : 'Waiting for live data...'}
          </p>
        </div>
      )}
    </div>
  );
};

export default LiveTimingPage;
