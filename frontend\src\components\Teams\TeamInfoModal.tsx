// src/components/Teams/TeamInfoModal.tsx
import React, { useState, useEffect } from 'react';
import {
    IonModal,
    IonHeader,
    IonToolbar,
    IonTitle,
    IonButtons,
    IonButton,
    IonIcon,
    IonContent,
    IonCard,
    IonCardHeader,
    IonCardTitle,
    IonCardContent,
    IonList,
    IonItem,
    IonLabel,
    IonNote,
    IonInput,
    IonSpinner,
    IonText,
} from '@ionic/react';
import {
    close,
    pencilOutline,
    saveOutline,
    closeCircleOutline,
    swapHorizontalOutline,
    trashOutline,
    timeOutline,
    arrowForwardOutline,
    returnUpBackOutline,
    checkmarkCircleOutline,
    buildOutline,
    alertCircleOutline,
} from 'ionicons/icons';
import { Team, Kart, AssignmentLog } from '../../types'; // Adjust path if needed
import { getSpeedBgClass, speedLevels } from './Utils'; // Assuming helpers moved
import { formatDate } from './Utils'; // Assuming helpers moved

// Define props for the modal
interface TeamInfoModalProps {
    isOpen: boolean;
    onDidDismiss: () => void; // Runs AFTER modal closes
    onRequestClose: () => void; // Runs when user clicks close button
    team: Team | null; // The selected team data
    isProcessing: boolean; // Global processing state (loading, etc.)
    // Props for editing
    onUpdateName: (teamId: string, newName: string) => Promise<boolean>;
    onUpdateNumber: (teamId: string, newNumber: number) => Promise<boolean>;
    // Props for actions
    onAssignKart: (team: Team) => void; // Trigger opening assign modal
    onUnassignKart: (kartId: string, teamId: string, teamNumber: number) => Promise<boolean>;
    onDeleteTeam: (team: Team) => void; // Trigger delete confirmation
    // Props for history
    assignmentHistory: AssignmentLog[];
    isLoadingHistory: boolean;
    historyError: string | null;
}

export const TeamInfoModal: React.FC<TeamInfoModalProps> = ({
    isOpen,
    onDidDismiss,
    onRequestClose, // Add new prop
    team,
    isProcessing,
    onUpdateName,
    onUpdateNumber,
    onAssignKart,
    onUnassignKart,
    onDeleteTeam,
    assignmentHistory,
    isLoadingHistory,
    historyError,
}) => {
    // Local state for editing within the modal
    const [isEditingName, setIsEditingName] = useState<boolean>(false);
    const [editedTeamName, setEditedTeamName] = useState<string>('');
    const [isEditingNumber, setIsEditingNumber] = useState<boolean>(false);
    const [editedTeamNumber, setEditedTeamNumber] = useState<string>('');
    const [isMutatingLocal, setIsMutatingLocal] = useState<boolean>(false); // Local mutation state

    // Update local edit state when the team prop changes (modal opens/data refreshes)
    useEffect(() => {
        if (team) {
            setEditedTeamName(team.name || '');
            setEditedTeamNumber((team.number || 0).toString());
            // Reset editing states if the selected team changes
            setIsEditingName(false);
            setIsEditingNumber(false);
        } else {
            // Clear if team becomes null (modal closing)
            setEditedTeamName('');
            setEditedTeamNumber('');
            setIsEditingName(false);
            setIsEditingNumber(false);
        }
    }, [team]);

    // --- Local Handlers ---
    const handleInternalUpdateName = async () => {
        if (!team || !editedTeamName.trim() || editedTeamName.trim() === team.name) {
            setIsEditingName(false); return;
        }
        setIsMutatingLocal(true);
        const success = await onUpdateName(team._id, editedTeamName.trim());
        setIsMutatingLocal(false);
        if (success) setIsEditingName(false);
    };

    const handleInternalUpdateNumber = async () => {
        if (!team) return;
        const newNumberStr = editedTeamNumber.trim();
        const newNumber = parseInt(newNumberStr, 10);

        if (isNaN(newNumber) || newNumber <= 0) {
            // Maybe show local validation? Parent handles toast.
            return;
        }
        if (newNumber === team.number) {
            setIsEditingNumber(false); return; // No change
        }

        setIsMutatingLocal(true);
        const success = await onUpdateNumber(team._id, newNumber);
        setIsMutatingLocal(false);
        if (success) setIsEditingNumber(false);
    };

    const handleInternalUnassign = async () => {
        if (!team || !infoModalKart) return;
        setIsMutatingLocal(true);
        await onUnassignKart(infoModalKart._id, team._id, team.number);
        setIsMutatingLocal(false);
        // Parent handles refetch/toast
    };

    const handleTriggerAssign = () => {
        if (team) onAssignKart(team);
    };

    const handleTriggerDelete = () => {
        if (team) onDeleteTeam(team);
    };

    // --- Derived Data ---
    const infoModalKart = team?.currentKartId as Kart | null;
    const infoModalKartSpeed = infoModalKart ? speedLevels[infoModalKart.speed ?? 4] : null;
    const isBusy = isProcessing || isMutatingLocal; // Combine global and local busy states

    // --- Helper to render history entry details (copied from TeamGrid) ---
    const renderHistoryEntryDetails = (entry: AssignmentLog) => {
        const kartNum = entry.kartId?.number ?? 'N/A';
        const currentSelectedTeamId = team?._id; // Use current team prop
        const teamName = entry.teamId ? (entry.teamId.name ?? `Team ${entry.teamId.number ?? '?'}`) : '?';
        const prevTeamName = entry.previousTeamId ? (entry.previousTeamId.name ?? `Team ${entry.previousTeamId.number ?? '?'}`) : '?';
        const rowNum = entry.rowId?.rowNumber ?? '?';
        const prevRowNum = entry.previousRowId?.rowNumber ?? '?';

        let icon = timeOutline;
        let color: string | undefined = "medium";
        let details = `Kart #${kartNum}`;

        switch (entry.eventType) {
            case 'ASSIGNED_TO_TEAM':
                icon = checkmarkCircleOutline; color = "success";
                details = `Kart #${kartNum} assigned to this team`;
                if (entry.previousRowId) details += ` (from Row ${prevRowNum})`;
                else if (entry.previousTeamId) details += ` (from ${prevTeamName})`;
                break;
            case 'ASSIGNED_TO_ROW':
                icon = arrowForwardOutline; color = "primary";
                details = `Kart #${kartNum} moved to Row ${rowNum}`;
                if (entry.previousTeamId?._id === currentSelectedTeamId) details += ` (from this team)`;
                else if (entry.previousTeamId) details += ` (from ${prevTeamName})`;
                else if (entry.previousRowId) details += ` (from Row ${prevRowNum})`;
                break;
            case 'UNASSIGNED_FROM_TEAM':
                icon = returnUpBackOutline; color = "warning";
                details = `Kart #${kartNum} unassigned from this team`;
                if (entry.rowId) details += ` (to Row ${rowNum})`;
                else if (entry.teamId) details += ` (to ${teamName})`;
                else details += ` (became available)`;
                break;
            case 'UNASSIGNED_FROM_ROW':
                icon = returnUpBackOutline; color = "medium";
                details = `Kart #${kartNum} unassigned from Row ${prevRowNum}`;
                if (entry.teamId?._id === currentSelectedTeamId) details += ` (to this team)`;
                else if (entry.teamId) details += ` (to ${teamName})`;
                break;
            case 'SWAP_TEAM_ROW':
                icon = swapHorizontalOutline; color = "tertiary";
                if (entry.teamId?._id === currentSelectedTeamId) details = `Kart #${kartNum} received via swap (from Row ${prevRowNum})`;
                else if (entry.previousTeamId?._id === currentSelectedTeamId) details = `Kart #${kartNum} given via swap (to Row ${rowNum})`;
                else details = `Kart #${kartNum} involved in swap (Row ${prevRowNum} <-> Row ${rowNum})`;
                break;
            case 'COMPENSATION':
                icon = buildOutline; color = "secondary";
                details = `Compensation entry for Kart #${kartNum}`;
                if (entry.compensatesLogId) details += ` (Corrects previous event)`;
                break;
            default:
                const exhaustiveCheck: never = entry.eventType;
                details = `Kart #${kartNum}: Unknown event type`;
                console.warn("Unhandled assignment log event type:", exhaustiveCheck)
                break;
        }

        if (entry.isInvalid) {
            icon = alertCircleOutline; color = "danger";
            details = `[INVALID] ${details}`;
            if (entry.reasonInvalid) details += ` - ${entry.reasonInvalid}`;
        }

        return { icon, color, details };
    };

    return (
        <IonModal isOpen={isOpen} onDidDismiss={onDidDismiss} className="team-info-modal">
            <IonHeader>
                <IonToolbar>
                    <IonTitle>
                        {team ? `#${team.number} - ${team.name}` : 'Loading Info...'}
                    </IonTitle>
                    <IonButtons slot="end">
                        <IonButton onClick={onRequestClose} disabled={isBusy}> {/* Call onRequestClose */}
                            <IonIcon slot="icon-only" icon={close} />
                        </IonButton>
                    </IonButtons>
                </IonToolbar>
            </IonHeader>
            <IonContent className="ion-padding">
                {!team ? (
                    <div className="ion-text-center ion-padding">
                        <IonSpinner name="crescent" />
                        <p>Loading team data...</p>
                    </div>
                ) : (
                    <>
                        {/* Card for Team Details */}
                        <IonCard>
                            <IonCardHeader><IonCardTitle>Team Details</IonCardTitle></IonCardHeader>
                            <IonCardContent>
                                <IonList lines="none">
                                    {/* Team Name (Editable) */}
                                    <IonItem>
                                        <IonLabel position="stacked">Name</IonLabel>
                                        {isEditingName ? (
                                            <div className="edit-name-container">
                                                <IonInput value={editedTeamName} onIonInput={e => setEditedTeamName(e.detail.value ?? '')} disabled={isBusy} className="edit-name-input" autoFocus={true} onKeyDown={(e) => { if (e.key === 'Enter') handleInternalUpdateName(); }} />
                                                <IonButton fill="clear" size="small" onClick={handleInternalUpdateName} disabled={isBusy || !editedTeamName.trim() || editedTeamName.trim() === team.name}><IonIcon slot="icon-only" icon={saveOutline} color="success" /></IonButton>
                                                <IonButton fill="clear" size="small" onClick={() => { setIsEditingName(false); setEditedTeamName(team.name); }} disabled={isBusy}><IonIcon slot="icon-only" icon={closeCircleOutline} color="medium" /></IonButton>
                                            </div>
                                        ) : (
                                            <div className="display-name-container">
                                                <IonNote slot="end" className="team-name-display">{team.name}</IonNote>
                                                <IonButton fill="clear" size="small" onClick={() => setIsEditingName(true)} disabled={isBusy}><IonIcon slot="icon-only" icon={pencilOutline} /></IonButton>
                                            </div>
                                        )}
                                    </IonItem>
                                    {/* Team Number (Editable) */}
                                    <IonItem>
                                        <IonLabel position="stacked">Number</IonLabel>
                                        {isEditingNumber ? (
                                            <div className="edit-name-container">
                                                <IonInput type="number" inputmode="numeric" value={editedTeamNumber} onIonInput={e => setEditedTeamNumber(e.detail.value ?? '')} disabled={isBusy} className="edit-name-input" autoFocus={true} min="1" onKeyDown={(e) => { if (e.key === 'Enter') handleInternalUpdateNumber(); }} />
                                                <IonButton fill="clear" size="small" onClick={handleInternalUpdateNumber} disabled={isBusy || !editedTeamNumber.trim() || parseInt(editedTeamNumber.trim(), 10) === team.number || isNaN(parseInt(editedTeamNumber.trim(), 10)) || parseInt(editedTeamNumber.trim(), 10) <= 0}><IonIcon slot="icon-only" icon={saveOutline} color="success" /></IonButton>
                                                <IonButton fill="clear" size="small" onClick={() => { setIsEditingNumber(false); setEditedTeamNumber((team.number || 0).toString()); }} disabled={isBusy}><IonIcon slot="icon-only" icon={closeCircleOutline} color="medium" /></IonButton>
                                            </div>
                                        ) : (
                                            <div className="display-name-container">
                                                <IonNote slot="end" className="team-name-display">#{team.number}</IonNote>
                                                <IonButton fill="clear" size="small" onClick={() => setIsEditingNumber(true)} disabled={isBusy}><IonIcon slot="icon-only" icon={pencilOutline} /></IonButton>
                                            </div>
                                        )}
                                    </IonItem>
                                </IonList>
                            </IonCardContent>
                        </IonCard>

                        {/* Card for Kart Info & Actions */}
                        <IonCard>
                            <IonCardHeader><IonCardTitle>Current Kart</IonCardTitle></IonCardHeader>
                            <IonCardContent>
                                <IonList lines="none">
                                    <IonItem>
                                        <IonLabel>Assigned Kart</IonLabel>
                                        {infoModalKart ? <IonNote slot="end">#{infoModalKart.number}</IonNote> : <IonNote slot="end" color="medium">None</IonNote>}
                                    </IonItem>
                                    {infoModalKart && infoModalKartSpeed && (
                                        <IonItem>
                                            <IonLabel>Kart Speed</IonLabel>
                                            <div slot="end" className={`speed-indicator ${getSpeedBgClass(infoModalKart.speed)}`}>
                                                <span className="speed-label">{infoModalKartSpeed.label}</span>
                                            </div>
                                        </IonItem>
                                    )}
                                    {/* Actions Section */}
                                    <IonItem className="action-item-no-lines">
                                        {infoModalKart ? (
                                            <IonButton expand="block" color="warning" fill="clear" onClick={handleInternalUnassign} disabled={isBusy} className="ion-text-wrap">
                                                <IonIcon slot="start" icon={closeCircleOutline} /> Unassign Kart #{infoModalKart.number}
                                            </IonButton>
                                        ) : (
                                            <IonButton expand="block" color="primary" fill="clear" onClick={handleTriggerAssign} disabled={isBusy}>
                                                <IonIcon icon={swapHorizontalOutline} slot="start" /> Assign Available Kart
                                            </IonButton>
                                        )}
                                    </IonItem>
                                </IonList>
                            </IonCardContent>
                        </IonCard>

                        {/* Card for Assignment History */}
                        <IonCard>
                            <IonCardHeader>
                                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                                    <IonCardTitle>Assignment History</IonCardTitle>
                                    {isLoadingHistory && <IonSpinner name="dots" color="medium" />}
                                </div>
                            </IonCardHeader>
                            <IonCardContent>
                                {isLoadingHistory ? (
                                    <div className="ion-text-center"><IonSpinner name="crescent" /></div>
                                ) : historyError ? (
                                    <IonText color="danger"><p>Error loading history: {historyError}</p></IonText>
                                ) : assignmentHistory.length > 0 ? (
                                    <IonList lines="full" className="history-list-inset">
                                        {assignmentHistory.map((entry) => {
                                            const { icon, color, details } = renderHistoryEntryDetails(entry);
                                            return (
                                                <IonItem key={entry._id} className={`history-item ${entry.isInvalid ? 'history-item-invalid' : ''}`}>
                                                    <IonIcon icon={icon} slot="start" color={color} aria-hidden="true" />
                                                    <IonLabel className="ion-text-wrap">
                                                        <p>{details}</p>
                                                        <IonNote className="history-timestamp">{formatDate(entry.timestamp)}</IonNote>
                                                    </IonLabel>
                                                </IonItem>
                                            );
                                        })}
                                    </IonList>
                                ) : (
                                    <IonText color="medium"><p>No assignment history found for this team.</p></IonText>
                                )}
                            </IonCardContent>
                        </IonCard>

                        {/* Card for Danger Zone */}
                        <IonCard>
                            <IonCardHeader><IonCardTitle color="danger">Danger Zone</IonCardTitle></IonCardHeader>
                            <IonCardContent>
                                <IonButton expand="block" color="danger" fill="clear" onClick={handleTriggerDelete} disabled={isBusy}>
                                    <IonIcon icon={trashOutline} slot="start" /> Delete Team #{team.number}
                                </IonButton>
                            </IonCardContent>
                        </IonCard>
                    </>
                )}
            </IonContent>
        </IonModal>
    );
};
