const mongoose = require('mongoose');

const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/raceplanner';

async function testFixes() {
  try {
    await mongoose.connect(MONGODB_URI);
    console.log('✅ Connected to MongoDB');

    // Import models
    const { ApexSession, ApexPitStop } = require('../dist/models/ApexModels');
    const { ApexParserSimple } = require('../dist/services/apexParserSimple');

    // Clean up
    await ApexSession.deleteMany({ title1: { $regex: /Test/ } });

    console.log('\n🔍 Testing OTR and Race Time Fixes...\n');

    const parser = new ApexParserSimple({ enableLogging: true });

    // Create session with grid
    const gridMessage = `init|r|
title1||Test Session
title2||Fix Test
track||Test Track
grid||<tbody><tr data-id="r0"><td data-id="c1" data-type="sta">N/A</td><td data-id="c2" data-type="rk">Rnk</td><td data-id="c3" data-type="no">Kart</td><td data-id="c5" data-type="dr">Team</td><td data-id="c14" data-type="otr">On track</td><td data-id="c15" data-type="pit">Pits</td></tr><tr data-id="r17788"><td data-id="r17788c1">N/A</td><td data-id="r17788c2">1</td><td data-id="r17788c3">99</td><td data-id="r17788c5">TEST TEAM</td><td data-id="r17788c14">00:00:00</td><td data-id="r17788c15">0</td></tr></tbody>`;

    console.log('📊 Creating session...');
    await parser.parseMessage(gridMessage);
    await new Promise(resolve => setTimeout(resolve, 1000));

    console.log('\n⏱️ Testing dyn1 race time...');
    await parser.parseMessage('dyn1|text|5400000'); // 90 minutes
    await new Promise(resolve => setTimeout(resolve, 500));

    await parser.parseMessage('dyn1|text|5385000'); // 15 seconds elapsed
    await new Promise(resolve => setTimeout(resolve, 500));

    console.log('\n🏁 Testing pit entry...');
    await parser.parseMessage('r17788c15|in|1'); // Pit entry
    await new Promise(resolve => setTimeout(resolve, 500));

    console.log('\n⏱️ Testing otr pit duration...');
    await parser.parseMessage('r17788c14|otr|05.'); // 5 seconds
    await new Promise(resolve => setTimeout(resolve, 500));

    await parser.parseMessage('r17788c14|otr|10.'); // 10 seconds
    await new Promise(resolve => setTimeout(resolve, 500));

    console.log('\n📊 Checking results...');
    
    const sessions = await ApexSession.find({ title1: { $regex: /Test/ } }).lean();
    if (sessions.length === 0) {
      console.log('❌ No sessions found');
      return;
    }

    const session = sessions[0];
    const pitStops = await ApexPitStop.find({ sessionId: session._id }).lean();
    
    console.log(`\n🏁 Found ${pitStops.length} pit stops`);
    
    if (pitStops.length > 0) {
      const pit = pitStops[0];
      console.log('\n📊 Pit Stop Details:');
      console.log(`   Pit Current Duration: ${pit.pitCurrentDuration || 'undefined'}`);
      console.log(`   Race Time At Pit In: ${pit.raceTimeAtPitIn || 'undefined'}`);
      
      // Check fixes
      const otrWorking = pit.pitCurrentDuration && pit.pitCurrentDuration > 0;
      const raceTimeWorking = pit.raceTimeAtPitIn && pit.raceTimeAtPitIn > 0;
      
      console.log('\n🔍 Fix Status:');
      console.log(`   OTR pit duration: ${otrWorking ? '✅ WORKING' : '❌ NOT WORKING'}`);
      console.log(`   Race time calculation: ${raceTimeWorking ? '✅ WORKING' : '❌ NOT WORKING'}`);
      
      if (otrWorking && raceTimeWorking) {
        console.log('\n✅ Both fixes are working!');
      } else {
        console.log('\n❌ Some fixes are not working!');
      }
    } else {
      console.log('❌ No pit stops created');
    }

    // Clean up
    await ApexSession.deleteMany({ title1: { $regex: /Test/ } });

  } catch (error) {
    console.error('❌ Test failed:', error);
  } finally {
    await mongoose.disconnect();
    console.log('\n🔌 Disconnected from MongoDB');
  }
}

testFixes().catch(console.error);
