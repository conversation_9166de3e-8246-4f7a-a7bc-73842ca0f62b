import { Request, Response } from "express";
// FIX 1: Remove unused 'Types' import if not used elsewhere (assuming it's not needed based on errors)
import mongoose, { FilterQuery, ClientSession } from "mongoose";
import { Kart, IKart } from "../models/Kart";
import { Team, ITeam } from "../models/Team";
import { Row, IRow } from "../models/Row"; // Import Row and IRow
import { sendWebSocketMessage } from "../websocket/websocket"; // Adjust path if needed

// --- Constants for WebSocket events ---
const ROW_UPDATE_EVENT = "rowsUpdated"; // Or a more specific event
const KART_UPDATE_EVENT = "kartsUpdated";
const TEAM_UPDATE_EVENT = "teamsUpdated";

// Import helpers from the utility file
import {
  isValidObjectId,
  sendErrorResponse,
  safelyEndSession,
  abortTransactionAndEndSession,
} from "../utils/controllerUtils"; // Adjust path if needed
import { getLastTeamForKart } from "./assignmentLogController";
import { AssignmentLog, IAssignmentLog } from "../models/AssignmentLog"; // Import for logging

// --- Controller Functions ---

/**
 * @description Create a new Kart
 * @route POST /api/karts
 */
export const createKart = async (req: Request, res: Response) => {
  try {
    const { number } = req.body;

    const speed = req.body.speed ?? 0;

    if (number === undefined) {
      return sendErrorResponse(res, "Kart number is required.", 400);
    }
    // Ensure currentTeamId and currentRowId are null on creation via this route
    const kartData: Partial<IKart> = {
      number,
      speed,
      currentTeamId: null,
      currentRowId: null, // Initialize currentRowId
      status: "available", // Initialize status
    };
    const newKart = new Kart(kartData);
    await newKart.save();

    // Send WebSocket message
    sendWebSocketMessage({
      event: KART_UPDATE_EVENT,
      payload: { type: "create", kart: newKart },
    });

    res.status(201).json(newKart);
  } catch (error: unknown) {
    if (
      error &&
      typeof error === "object" &&
      "code" in error &&
      error.code === 11000
    ) {
      return sendErrorResponse(
        res,
        `Kart number ${req.body.number} already exists.`,
        409,
        error
      );
    }
    sendErrorResponse(res, "Error creating kart", 500, error);
  }
};

/**
 * @description Get all Karts, optionally filtered by assignment status or row status
 * @route GET /api/karts
 */
export const getAllKarts = async (req: Request, res: Response) => {
  try {
    const filter: FilterQuery<IKart> = {};
    const assignedQuery = req.query.assigned as string | undefined;
    const statusQuery = req.query.status as IKart["status"] | undefined; // Filter by status

    if (assignedQuery === "true") filter.currentTeamId = { $ne: null };
    else if (assignedQuery === "false") filter.currentTeamId = null;

    if (statusQuery) filter.status = statusQuery; // Add status filter

    const karts = await Kart.find(filter)
      .populate<{
        currentTeamId: Pick<ITeam, "name" | "number" | "_id"> | null;
      }>({
        path: "currentTeamId",
        select: "name number _id",
      })
      .populate<{
        currentRowId: Pick<IRow, "rowNumber" | "color" | "_id"> | null;
      }>({
        // Populate currentRowId
        path: "currentRowId",
        select: "rowNumber color _id",
      })
      .sort({ number: 1 })
      .lean() // Use .lean() for faster queries if not modifying results
      .exec();

    res.status(200).json(karts);
  } catch (error: unknown) {
    sendErrorResponse(res, "Error fetching karts", 500, error);
  }
};

/**
 * @description Get a single Kart by ID
 * @route GET /api/karts/:id
 */
export const getKartById = async (req: Request, res: Response) => {
  try {
    const { id: kartId } = req.params;
    if (!kartId || !isValidObjectId(kartId)) {
      return sendErrorResponse(res, "Valid Kart ID is required.", 400);
    }

    const kart = await Kart.findById(kartId)
      .populate<{
        currentTeamId: Pick<ITeam, "name" | "number" | "_id"> | null;
      }>({
        path: "currentTeamId",
        select: "name number _id",
      })
      .populate<{
        currentRowId: Pick<IRow, "rowNumber" | "color" | "_id"> | null;
      }>({
        // Populate currentRowId
        path: "currentRowId",
        select: "rowNumber color _id",
      })
      .lean()
      .exec();

    if (!kart) {
      return sendErrorResponse(res, "Kart not found.", 404);
    }
    res.status(200).json(kart);
  } catch (error: unknown) {
    sendErrorResponse(res, "Error fetching kart", 500, error);
  }
};

/**
 * @description Update Kart's own information (e.g., speed, status). Does NOT handle assignment.
 * @route PUT /api/karts/:id
 */
export const updateKartInfo = async (req: Request, res: Response) => {
  try {
    const { id: kartId } = req.params;
    if (!kartId || !isValidObjectId(kartId)) {
      return sendErrorResponse(res, "Valid Kart ID is required.", 400);
    }

    // Selectively build update payload, excluding currentTeamId and currentRowId
    const updateData: Partial<Pick<IKart, "number" | "speed" | "status">> = {}; // Allow only specific fields
    if (req.body.number !== undefined) updateData.number = req.body.number;
    if (req.body.speed !== undefined) updateData.speed = req.body.speed;
    if (req.body.status !== undefined) updateData.status = req.body.status; // Allow updating status
    // Add other editable Kart-specific fields here

    if (Object.keys(updateData).length === 0) {
      return sendErrorResponse(
        res,
        "No valid fields provided for update.",
        400
      );
    }

    const updatedKart = await Kart.findByIdAndUpdate(kartId, updateData, {
      new: true,
      runValidators: true,
    })
      .populate<{
        currentTeamId: Pick<ITeam, "name" | "number" | "_id"> | null;
      }>({
        path: "currentTeamId",
        select: "name number _id",
      })
      .populate<{
        currentRowId: Pick<IRow, "rowNumber" | "color" | "_id"> | null;
      }>({
        // Populate currentRowId
        path: "currentRowId",
        select: "rowNumber color _id",
      })
      .lean(); // Use lean if you only need the data

    if (!updatedKart) {
      return sendErrorResponse(res, "Kart not found.", 404);
    }

    sendWebSocketMessage({
      event: KART_UPDATE_EVENT,
      payload: { type: "update", kart: updatedKart },
    });
    res.status(200).json(updatedKart);
  } catch (error: unknown) {
    if (
      error &&
      typeof error === "object" &&
      "code" in error &&
      error.code === 11000
    ) {
      return sendErrorResponse(
        res,
        `Kart number ${req.body.number} may already exist.`,
        409,
        error
      );
    }
    if (
      error &&
      typeof error === "object" &&
      "name" in error &&
      error.name === "ValidationError"
    ) {
      return sendErrorResponse(
        res,
        `Validation Error: ${(error as Error).message}`,
        400,
        error
      );
    }
    sendErrorResponse(res, "Error updating kart info", 500, error);
  }
};

/**
 * @description Delete a Kart. Must unassign from team and row first if assigned.
 * @route DELETE /api/karts/:id
 */
export const deleteKart = async (req: Request, res: Response) => {
  let session: ClientSession | null = null;
  const operation = "deleteKart";
  try {
    session = await mongoose.startSession();
    session.startTransaction();
    console.log(`[${operation}] Transaction started.`);

    const { id: kartId } = req.params;
    if (!kartId || !isValidObjectId(kartId)) {
      await abortTransactionAndEndSession(session, `${operation}Validation`);
      return sendErrorResponse(res, "Valid Kart ID is required.", 400);
    }

    // Find kart first to get teamId/rowId if assigned
    const kart = await Kart.findById(kartId).session(session);
    if (!kart) {
      await abortTransactionAndEndSession(session, `${operation}NotFound`);
      return sendErrorResponse(res, "Kart not found.", 404);
    }
    console.log(`[${operation}] Kart ${kart.number} found.`);

    const teamId = kart.currentTeamId;
    const rowId = kart.currentRowId;
    let teamWasUpdated = false;
    let rowWasUpdated = false;

    // If assigned to a team, update the team within the transaction
    if (teamId) {
      console.log(
        `[${operation}] Kart ${kartId} is assigned to team ${teamId}. Unassigning team...`
      );
      const updateResult = await Team.findByIdAndUpdate(
        teamId,
        { $set: { currentKartId: null }, $addToSet: { pastKarts: kart._id } },
        { new: false, session } // Don't need the updated team doc back here
      );
      if (!updateResult) {
        console.warn(
          `[${operation}] Team ${teamId} not found while unassigning Kart ${kartId}. Kart deletion will proceed.`
        );
      } else {
        console.log(
          `[${operation}] Team ${teamId} unassigned from Kart ${kartId}.`
        );
        teamWasUpdated = true;
      }
    }

    // If assigned to a row, update the row within the transaction
    if (rowId) {
      console.log(
        `[${operation}] Kart ${kartId} is assigned to row ${rowId}. Unassigning row...`
      );
      const updateResult = await Row.findByIdAndUpdate(
        rowId,
        { $set: { currentKartId: null }, $addToSet: { pastKarts: kart._id } },
        { new: false, session }
      );
      if (!updateResult) {
        console.warn(
          `[${operation}] Row ${rowId} not found while unassigning Kart ${kartId}. Kart deletion will proceed.`
        );
      } else {
        console.log(
          `[${operation}] Row ${rowId} unassigned from Kart ${kartId}.`
        );
        rowWasUpdated = true;
      }
    }

    // Delete the kart itself
    const deleteResult = await Kart.findByIdAndDelete(kartId).session(session);
    if (!deleteResult) {
      // Should not happen if findById worked, but safety check
      throw new Error(`Failed to delete Kart ${kartId} after finding it.`);
    }
    console.log(`[${operation}] Kart ${kartId} deleted.`);

    await session.commitTransaction();
    console.log(`[${operation}] Transaction committed.`);

    // Send WebSocket messages AFTER commit
    sendWebSocketMessage({
      event: KART_UPDATE_EVENT,
      payload: { type: "delete", kartId: kartId },
    });
    if (teamWasUpdated && teamId) {
      // FIX 2: Check if teamId is not null
      // Send a generic team update/refresh, as we didn't fetch the updated team
      sendWebSocketMessage({
        event: TEAM_UPDATE_EVENT,
        payload: { type: "refresh", teamId: teamId.toString() },
      }); // Include ID if possible
    }
    if (rowWasUpdated && rowId) {
      // FIX 3: Check if rowId is not null
      // Send a generic row update/refresh
      sendWebSocketMessage({
        event: ROW_UPDATE_EVENT,
        payload: { type: "refresh", rowId: rowId.toString() },
      }); // Include ID if possible
    }

    res.status(200).json({ message: "Kart deleted successfully." });
  } catch (error: unknown) {
    await abortTransactionAndEndSession(session, operation, error); // Handles abort/end
    sendErrorResponse(res, "Error deleting kart", 500, error);
  } finally {
    await safelyEndSession(session); // Ensure session is ended even if error occurred before/after transaction block
  }
};

// === Assignment Management Controllers ===

/**
 * @description Assign a Kart to a Team (Handles unassignment from previous team/kart and row)
 * @route PUT /api/karts/:kartId/assign/:teamId
 */
// *** Add return type: Promise<void> ***
export const assignKartToTeam = async (req: Request, res: Response): Promise<void> => {
  let session: ClientSession | null = null;
  const operation = "assignKartToTeam"; // For logging context
  try {
    session = await mongoose.startSession();
    session.startTransaction();
    console.log(`[${operation}] Transaction started.`);

    const { kartId, teamId } = req.params;
    if (
      !kartId ||
      !teamId ||
      !isValidObjectId(kartId) ||
      !isValidObjectId(teamId)
    ) {
      // No session started yet, just send error
      sendErrorResponse(res, "Valid Kart and Team IDs required.", 400);
      // *** Add explicit return ***
      return;
    }
    console.log(`[${operation}] IDs validated: Kart=${kartId}, Team=${teamId}`);

    // Fetch documents within transaction
    const kart = await Kart.findById(kartId).session(session);
    if (!kart) {
      throw new Error(`Kart ${kartId} not found.`);
    }
    const team = await Team.findById(teamId).session(session);
    if (!team) {
      throw new Error(`Team ${teamId} not found.`);
    }
    console.log(
      `[${operation}] Kart ${kart.number} and Team ${team.number} found.`
    );

    // --- State Validation and Unassignment Logic ---
    const previousTeamId = kart.currentTeamId;
    const previousKartId = team.currentKartId;
    const previousRowId = kart.currentRowId; // Check if kart is in a row
    const now = new Date(); // Consistent timestamp for logs

    // 1. Check if already assigned to this team
    if (previousTeamId?.toString() === teamId) {
      console.log(
        `[${operation}] Info: Kart ${kart.number} already assigned to Team ${team.number}.`
      );
      await abortTransactionAndEndSession(session, operation); // Abort, no error needed
      res
        .status(200)
        .json({ message: "Kart already assigned to this team." });
      // *** Add explicit return ***
      return;
    }

    // 2. Check if kart is assigned to ANOTHER team
    if (previousTeamId) {
      throw new Error(
        `Kart ${kart.number} is already assigned to Team ID ${previousTeamId}. Unassign first.`
      );
      // Or, implement automatic unassignment from previousTeamId here if desired
    }

    // 3. Check if team already has ANOTHER kart
    if (previousKartId) {
      throw new Error(
        `Team ${team.number} already has Kart ID ${previousKartId} assigned. Unassign first.`
      );
      // Or, implement automatic unassignment of previousKartId here if desired
    }

    // 4. Check if kart is currently in a pit row and unassign if necessary
    let previousRowForLog: mongoose.Types.ObjectId | null = null; // For logging
    if (previousRowId) {
      previousRowForLog = previousRowId; // Capture for log
      console.log(
        `[${operation}] Kart ${kart.number} is currently in Row ${previousRowId}. Unassigning from row...`
      );
      const rowUpdateResult = await Row.findByIdAndUpdate(
        previousRowId,
        { $set: { currentKartId: null } }, // Only set currentKartId to null
        { session }
      );
      if (!rowUpdateResult) {
        console.warn(
          `[${operation}] Row ${previousRowId} not found during unassignment.`
        );
        // Decide if this is critical - maybe throw error? For now, just warn.
      } else {
        console.log(`[${operation}] Row ${previousRowId} updated.`);
      }
      kart.currentRowId = null; // Update kart object in memory
    }
    console.log(`[${operation}] State validation passed.`);

    // --- Perform Assignment ---
    kart.currentTeamId = team._id;
    kart.status = "on_track"; // Update status
    team.currentKartId = kart._id;
    // Remove from pastKarts if it was there
    team.pastKarts =
      team.pastKarts?.filter(
        (id: mongoose.Types.ObjectId | IKart | null | undefined) => {
          if (!id) return false; // Remove null/undefined entries
          const idString =
            id instanceof mongoose.Types.ObjectId
              ? id.toString()
              : (id as IKart)._id?.toString();
          return idString !== kartId;
        }
      ) ?? [];
    console.log(`[${operation}] Document properties updated in memory.`);

    // --- Create Log Entry ---
    const logEntry = new AssignmentLog({
        eventType: 'ASSIGNED_TO_TEAM',
        kartId: kart._id,
        teamId: team._id,
        previousRowId: previousRowForLog, // Log the row it came from, if any
        timestamp: now,
    });
    console.log(`[${operation}] Preparing log entry.`);

    // Save changes (Kart, Team, Log)
    await Promise.all([
        kart.save({ session }),
        team.save({ session }),
        logEntry.save({ session }) // Save the log entry
    ]);
    console.log(`[${operation}] Kart, Team, and Log saved.`);

    await session.commitTransaction();
    console.log(`[${operation}] Transaction committed.`);

    // --- Post-Commit Actions ---
    // Fetch updated data OUTSIDE transaction for response/WS
    const [updatedKartPopulated, updatedTeamPopulated] = await Promise.all([
      Kart.findById(kartId)
        .populate<{
          currentTeamId: Pick<ITeam, "name" | "number" | "_id"> | null;
        }>({ path: "currentTeamId", select: "name number _id" })
        .lean(),
      Team.findById(teamId)
        .populate<{
          currentKartId: Pick<IKart, "number" | "_id"> | null;
        }>({ path: "currentKartId", select: "number _id" })
        .lean(), // Only need kart number for team view
    ]);
    console.log(`[${operation}] Fetched updated data post-commit.`);

    // Send WebSocket messages
    if (updatedKartPopulated)
      sendWebSocketMessage({
        event: KART_UPDATE_EVENT,
        payload: { type: "update", kart: updatedKartPopulated },
      });
    if (updatedTeamPopulated)
      sendWebSocketMessage({
        event: TEAM_UPDATE_EVENT,
        payload: { type: "update", team: updatedTeamPopulated },
      });
    if (previousRowId) // If a row was involved
      sendWebSocketMessage({
        event: ROW_UPDATE_EVENT,
        payload: { type: "refresh", rowId: previousRowId.toString() },
      }); // Notify row update
    // Optional: Send WS message for the new log entry
    // sendWebSocketMessage({ event: "assignmentLogCreated", payload: { log: logEntry.toObject() } });
    console.log(`[${operation}] WebSocket messages sent.`);

    res.status(200).json({
      message: `Kart ${kart?.number} assigned to Team ${team?.number} successfully.`,
      kart: updatedKartPopulated,
      team: updatedTeamPopulated, // Include updated team in response
    });
  } catch (error: unknown) {
    await abortTransactionAndEndSession(session, operation, error); // Handles abort/end
    // Determine status code based on error message
    const statusCode =
      error instanceof Error &&
      (error.message.includes("not found") ||
        error.message.includes("already assigned") ||
        error.message.includes("already has Kart"))
        ? 409 // Conflict
        : 500; // Internal Server Error
    sendErrorResponse(
      res,
      `Error assigning kart: ${error instanceof Error ? error.message : "Unknown error"}`,
      statusCode,
      error
    );
  } finally {
    await safelyEndSession(session); // Final check to ensure session is ended
  }
};

/**
 * @description Unassign a Kart from its current Team (places it in 'available' status)
 * @route DELETE /api/karts/:kartId/assign
 */
// *** Add return type: Promise<void> ***
export const unassignKartFromTeam = async (req: Request, res: Response): Promise<void> => {
  let session: ClientSession | null = null;
  const operation = "unassignKartFromTeam";
  try {
    session = await mongoose.startSession();
    session.startTransaction();
    console.log(`[${operation}] Transaction started.`);

    const { kartId } = req.params;
    if (!kartId || !isValidObjectId(kartId)) {
      // No session started yet, just send error
      sendErrorResponse(res, "Valid Kart ID is required.", 400);
      // *** Add explicit return ***
      return;
    }
    console.log(`[${operation}] ID validated: Kart=${kartId}`);

    const kart = await Kart.findById(kartId).session(session);
    if (!kart) {
      throw new Error(`Kart ${kartId} not found.`);
    }
    console.log(`[${operation}] Kart ${kart.number} found.`);

    const teamId = kart.currentTeamId;
    if (!teamId) {
      // Already unassigned
      console.log(
        `[${operation}] Info: Kart ${kart.number} is already unassigned.`
      );
      await abortTransactionAndEndSession(session, operation);
      res
        .status(200)
        .json({ message: `Kart ${kart.number} is already unassigned.` });
      // *** Add explicit return ***
      return;
    }
    console.log(`[${operation}] Kart currently assigned to Team ${teamId}.`);

    // --- Prepare Updates ---
    const previousTeamIdForLog = kart.currentTeamId; // Capture for log
    const now = new Date();

    // Update Kart in memory
    kart.currentTeamId = null;
    kart.status = "available"; // Set status to available when unassigned from team
    kart.currentRowId = null; // Ensure it's not marked as in a row either

    // --- Create Log Entry ---
    const logEntry = new AssignmentLog({
        eventType: 'UNASSIGNED_FROM_TEAM',
        kartId: kart._id,
        previousTeamId: previousTeamIdForLog, // Log the team it left
        timestamp: now,
    });
    console.log(`[${operation}] Preparing log entry.`);

    // Update Team (using findByIdAndUpdate for atomicity within transaction)
    console.log(`[${operation}] Finding and updating Team ${teamId}...`);
    const teamUpdateResult = await Team.findByIdAndUpdate(
      teamId,
      { $set: { currentKartId: null }, $addToSet: { pastKarts: kart._id } },
      { new: false, session } // Don't need updated doc back immediately
    );

    let teamWasUpdated = false;
    if (!teamUpdateResult) {
      console.warn(
        `[${operation}] Team ${teamId} not found while unassigning Kart ${kartId}. Kart will still be unassigned.`
      );
      // Decide if this is critical - maybe throw error? For now, just warn.
    } else {
      teamWasUpdated = true;
      console.log(
        `[${operation}] Team ${teamId} updated (currentKartId set to null, pastKarts updated).`
      );
    }

    // Save Kart and Log Entry
    await Promise.all([
        kart.save({ session }),
        logEntry.save({ session }) // Save the log entry
    ]);
    console.log(
      `[${operation}] Kart ${kart.number} and Log saved.`
    );

    await session.commitTransaction();
    console.log(`[${operation}] Transaction committed.`);

    // --- Post-Commit Actions ---
    // Fetch updated data OUTSIDE transaction
    const updatedKartPopulated = await Kart.findById(kartId).lean(); // Now shows null teamId
    let updatedTeamPopulated = null;
    if (teamWasUpdated) {
      updatedTeamPopulated = await Team.findById(teamId)
        .populate<{
          currentKartId: Pick<IKart, "number" | "_id"> | null;
        }>({ path: "currentKartId", select: "number _id" })
        .lean();
    }
    console.log(`[${operation}] Fetched updated data post-commit.`);

    // Send WebSocket messages
    if (updatedKartPopulated)
      sendWebSocketMessage({
        event: KART_UPDATE_EVENT,
        payload: { type: "update", kart: updatedKartPopulated },
      });
    if (teamWasUpdated && updatedTeamPopulated)
      sendWebSocketMessage({
        event: TEAM_UPDATE_EVENT,
        payload: { type: "update", team: updatedTeamPopulated },
      });
    else if (teamWasUpdated && teamId) // If team was updated but couldn't be fetched after
      sendWebSocketMessage({
        event: TEAM_UPDATE_EVENT,
        payload: { type: "refresh", teamId: teamId.toString() },
      });
    // Optional: Send WS message for the new log entry
    // sendWebSocketMessage({ event: "assignmentLogCreated", payload: { log: logEntry.toObject() } });
    console.log(`[${operation}] WebSocket messages sent.`);

    res.status(200).json({
      message: `Kart ${kart.number} unassigned successfully.`,
      kart: updatedKartPopulated,
      team: updatedTeamPopulated, // Include updated team (or null) in response
    });
  } catch (error: unknown) {
    await abortTransactionAndEndSession(session, operation, error);
    const statusCode =
      error instanceof Error && error.message.includes("not found") ? 404 : 500;
    sendErrorResponse(
      res,
      `Error unassigning kart: ${error instanceof Error ? error.message : "Unknown error"}`,
      statusCode,
      error
    );
  } finally {
    await safelyEndSession(session);
  }
};

/**
 * @description Get the last team associated with a specific Kart based on assignment history.
 * @route GET /api/karts/:kartId/last-team
 */
export const getKartLastTeam = async (req: Request, res: Response): Promise<void> => {
  const { kartId } = req.params;
  const operation = "getKartLastTeam";

  if (!kartId || !isValidObjectId(kartId)) {
    // Use sendErrorResponse and return to exit
    sendErrorResponse(res, "Valid Kart ID is required.", 400);
    return;
  }

  try {
    console.log(`[${operation}] Finding last team for Kart ${kartId}`);
    const lastTeam = await getLastTeamForKart(kartId);

    if (!lastTeam) {
      // Use sendErrorResponse and return to exit
      sendErrorResponse(
        res,
        `No team assignment history found for Kart ${kartId}.`,
        404
      );
      return;
    }

    console.log(
      `[${operation}] Found last team: ${lastTeam.name} (#${lastTeam.number}) for Kart ${kartId}`
    );
    // Send success response (no return needed here)
    res.status(200).json(lastTeam);

  } catch (error: unknown) {
    console.error(
      `[${operation}] Error fetching last team for Kart ${kartId}:`,
      error
    );
    // Use sendErrorResponse (no return needed here as it's the end of the catch block)
    sendErrorResponse(
      res,
      `Error fetching last team for kart ${kartId}`,
      500,
      error
    );
  }
};

/**
 * @description Move a Kart to Maintenance status, unassigning from Team or Row if necessary.
 * @route POST /api/karts/:kartId/set-maintenance
 */
export const moveKartToMaintenance = async (req: Request, res: Response): Promise<void> => {
    let session: ClientSession | null = null;
    const operation = "moveKartToMaintenance";
    const { kartId } = req.params;

    // 1. Validate ID
    if (!kartId || !isValidObjectId(kartId)) {
        return sendErrorResponse(res, "Valid Kart ID is required.", 400);
    }

    try {
        session = await mongoose.startSession();
        session.startTransaction();
        console.log(`[${operation}] Transaction started for Kart ${kartId}.`);

        // 2. Fetch Kart
        const kart = await Kart.findById(kartId).session(session);
        if (!kart) {
            throw new Error(`Kart ${kartId} not found.`);
        }
        console.log(`[${operation}] Found Kart ${kart.number}. Current status: ${kart.status}, Team: ${kart.currentTeamId}, Row: ${kart.currentRowId}`);

        if (kart.status === 'maintenance') {
            console.log(`[${operation}] Kart ${kart.number} is already in maintenance.`);
            await abortTransactionAndEndSession(session, operation);
            res.status(200).json({ message: "Kart already in maintenance.", kart });
            return;
        }

        // 3. Prepare Updates and Log
        const previousTeamId = kart.currentTeamId;
        const previousRowId = kart.currentRowId;
        const now = new Date();
        let logEntry: IAssignmentLog | null = null;
        let teamWasUpdated = false;
        let rowWasUpdated = false;

        // Update Kart fields
        kart.status = 'maintenance';
        kart.currentTeamId = null;
        kart.currentRowId = null;

        // Create log based on previous state
        if (previousTeamId) {
            logEntry = new AssignmentLog({ eventType: 'UNASSIGNED_FROM_TEAM', kartId: kart._id, previousTeamId: previousTeamId, timestamp: now });
            // Update the previous team
            await Team.findByIdAndUpdate(previousTeamId, { $set: { currentKartId: null } }, { session });
            teamWasUpdated = true;
            console.log(`[${operation}] Unassigning from Team ${previousTeamId}.`);
        } else if (previousRowId) {
            logEntry = new AssignmentLog({ eventType: 'UNASSIGNED_FROM_ROW', kartId: kart._id, previousRowId: previousRowId, timestamp: now });
            // Update the previous row
            await Row.findByIdAndUpdate(previousRowId, { $set: { currentKartId: null } }, { session });
            rowWasUpdated = true;
            console.log(`[${operation}] Unassigning from Row ${previousRowId}.`);
        }

        // 4. Save Kart and Log (if created)
        await kart.save({ session });
        if (logEntry) await logEntry.save({ session });
        console.log(`[${operation}] Kart saved. Log ${logEntry ? 'saved' : 'not needed'}.`);

        // 5. Commit Transaction
        await session.commitTransaction();
        console.log(`[${operation}] Transaction committed.`);

        // 6. Send WebSocket updates
        sendWebSocketMessage({ event: KART_UPDATE_EVENT, payload: { type: "update", kart: kart.toObject() } }); // Send updated kart
        if (teamWasUpdated && previousTeamId) sendWebSocketMessage({ event: TEAM_UPDATE_EVENT, payload: { type: "refresh", teamId: previousTeamId.toString() } });
        if (rowWasUpdated && previousRowId) sendWebSocketMessage({ event: ROW_UPDATE_EVENT, payload: { type: "refresh", rowId: previousRowId.toString() } });
        console.log(`[${operation}] WebSocket messages sent.`);

        res.status(200).json({ message: `Kart ${kart.number} moved to maintenance.`, kart });

    } catch (error: unknown) {
        await abortTransactionAndEndSession(session, operation, error);
        sendErrorResponse(res, `Error moving kart to maintenance: ${error instanceof Error ? error.message : "Unknown error"}`, 500, error);
    } finally {
        await safelyEndSession(session);
    }
};
