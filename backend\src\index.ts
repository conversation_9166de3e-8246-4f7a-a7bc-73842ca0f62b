import express from "express";
import mongoose from "mongoose";
import dotenv from "dotenv";
import cors from 'cors'; // Import cors
import apiRoutes from "./routes/api";
import { setupWebSocketServer, initializeReplayService, initializeApexReceiver } from "./websocket/websocket"; // Assuming you refactor WS setup
import { seedInitialData } from "./utils/seedData";
import http from 'http'; // Import http

dotenv.config();
const app = express();

// --- CORS Configuration ---
const allowedOrigins = [
    'https://raceplanner-frontend.onrender.com', // Your deployed frontend
    'http://localhost:3000' // Your local frontend dev server
];

app.use(cors({
    origin: function (origin, callback) {
        // Allow requests with no origin (like mobile apps or curl requests)
        if (!origin) return callback(null, true);
        if (allowedOrigins.indexOf(origin) === -1) {
            const msg = 'The CORS policy for this site does not allow access from the specified Origin.';
            return callback(new Error(msg), false);
        }
        return callback(null, true);
    },
    credentials: true // If you need to handle cookies or authorization headers
}));
// ------------------------

app.use(express.json());
app.use("/api", apiRoutes);

// --- Create HTTP server for both Express and WebSocket ---
const server = http.createServer(app);
// ---------------------------------------------------------

mongoose
  .connect(process.env.MONGO_URI!)
  .then(async() => {
    console.log("MongoDB connected");
    await seedInitialData();

    // --- Attach WebSocket server to the HTTP server ---
    setupWebSocketServer(server); // Pass the server instance
    initializeReplayService(); // Initialize log replay service
    initializeApexReceiver(); // Initialize apex realtime receiver
    // ------------------------------------------------

    // --- Start the combined server ---
    const PORT = process.env.PORT || 5000; // Use 5000 as default if PORT not set
    server.listen(PORT, () => {
      console.log(`Server running on port ${PORT}`);
      // Note: WebSocket server is now running on the same port
    });
    // ---------------------------------
  })
  .catch((err) => {
    console.error("MongoDB connection error:", err);
    process.exit(1); // Exit on DB connection error
  });
