# Real-World Pit Status Handling

## Overview

This document explains the implementation of real-world pit status handling based on actual log data, where pit statuses are **numeric values** representing pit duration in seconds, and pit duration is tracked via the `otr` (on track time) field.

## Real-World Log Format Analysis

### **Pit Status Field (`c15` - `pit`)**
```
r17788c15|pit|6    # Competitor 17788 has been in pit for 6 seconds
r17788c15|pit|0    # Competitor 17788 exits pit (0 seconds = out)
```

**Format**: Numeric values representing seconds in pit
- `0` = Not in pit / Pit exit
- `1, 2, 3, 4, 5, 6...` = Seconds spent in pit

### **Pit Duration Field (`c14` - `otr`)**
```
r17788c14|otr|06.  # Current pit duration: 6 seconds
r17788c14|otr|00.  # Pit duration reset to 0 (pit exit)
```

**Format**: `XX.` where XX is seconds with trailing dot
- `00.` = 0 seconds
- `01.` = 1 second
- `30.` = 30 seconds

## Implementation

### ✅ **1. Enhanced Field Type Detection**

```typescript
case 'pit': // Pit status (can be number or IN/OUT)
  if (competitorId && data.value) {
    await this.handlePitUpdate(competitorId, data.value);
  }
  break;
case 'otr': // On track time - used for pit duration
  if (competitorId && data.value) {
    await this.handlePitDurationUpdate(competitorId, data.value);
  }
  break;
```

### ✅ **2. Numeric Pit Status Handling**

```typescript
/**
 * Handle different pit status formats
 */
private async handlePitUpdate(websocketId: string, pitStatus: string): Promise<void> {
  // Handle different pit status formats
  if (pitStatus === 'IN') {
    // Handle pit entry (string format)
    await this.handlePitEntry(competitor, competitorObjectId);
  } else if (pitStatus === 'OUT') {
    // Handle pit exit (string format)
    await this.handlePitExit(competitor, competitorObjectId);
  } else if (this.isPitStatusNumber(pitStatus)) {
    // Handle numeric pit status (e.g., "6" means in pit for 6 seconds)
    await this.handleNumericPitStatus(competitor, competitorObjectId, pitStatus);
  }
}
```

### ✅ **3. Numeric Pit Status Logic**

```typescript
/**
 * Handle numeric pit status (e.g., "6" means in pit for 6 seconds)
 */
private async handleNumericPitStatus(competitor: any, competitorObjectId: mongoose.Types.ObjectId, pitStatus: string): Promise<void> {
  const pitDurationSeconds = parseInt(pitStatus);

  // Check if there's an active pit stop for this competitor
  let activePitStop = await ApexPitStop.findOne({
    sessionId: this.currentSession._id,
    competitorId: competitorObjectId,
    pitOutTime: { $exists: false }
  }).sort({ pitInTime: -1 });

  if (!activePitStop && pitDurationSeconds > 0) {
    // No active pit stop but we have duration - create pit entry
    await this.handlePitEntry(competitor, competitorObjectId);
    activePitStop = await ApexPitStop.findOne({...}); // Get newly created
  }

  if (activePitStop) {
    // Update current duration
    await ApexPitStop.updateOne(
      { _id: activePitStop._id },
      { 
        pitCurrentDuration: pitDurationSeconds,
        pitTotalDuration: (activePitStop.pitTotalDuration || 0) + pitDurationSeconds
      }
    );
  }

  // If duration is 0, it means pit exit
  if (pitDurationSeconds === 0 && activePitStop) {
    await this.handlePitExit(competitor, competitorObjectId);
  }
}
```

### ✅ **4. Pit Duration Updates**

```typescript
/**
 * Handle pit duration updates from otr field
 */
private async handlePitDurationUpdate(websocketId: string, durationValue: string): Promise<void> {
  // Parse duration (format like "00." or "30." for seconds)
  const durationSeconds = this.parsePitDuration(durationValue);
  
  // Find active pit stop and update current duration
  const activePitStop = await ApexPitStop.findOne({
    sessionId: this.currentSession._id,
    competitorId: competitorObjectId,
    pitOutTime: { $exists: false }
  });

  if (activePitStop) {
    await ApexPitStop.updateOne(
      { _id: activePitStop._id },
      { pitCurrentDuration: durationSeconds }
    );
  }
}

/**
 * Parse pit duration from otr field (e.g., "00." = 0 seconds, "30." = 30 seconds)
 */
private parsePitDuration(durationValue: string): number | null {
  // Remove trailing dot and parse as integer
  const cleanValue = durationValue.replace(/\.$/, '');
  const seconds = parseInt(cleanValue);
  return isNaN(seconds) ? null : seconds;
}
```

## Real-World Pit Stop Flow

### **Pit Entry Sequence**
```
[10:04:45] r17788c15|pit|1    # Pit status: 1 second in pit
           r17788c14|otr|01.  # Duration: 1 second

[10:04:46] r17788c15|pit|2    # Pit status: 2 seconds in pit  
           r17788c14|otr|02.  # Duration: 2 seconds

[10:04:47] r17788c15|pit|3    # Pit status: 3 seconds in pit
           r17788c14|otr|03.  # Duration: 3 seconds
```

**Database Result**:
```javascript
{
  _id: ObjectId("..."),
  competitorId: ObjectId("17788"),
  pitInTime: "2024-11-09T10:04:45.000Z",
  pitCurrentDuration: 3,  // Updated in real-time
  pitTotalDuration: 3,    // Cumulative
  isActive: true,
  pitOutTime: null        // Still in pit
}
```

### **Pit Exit Sequence**
```
[10:05:15] r17788c15|pit|0    # Pit status: 0 (exit)
           r17788c14|otr|00.  # Duration: 0 (reset)
```

**Database Result**:
```javascript
{
  _id: ObjectId("..."),
  competitorId: ObjectId("17788"),
  pitInTime: "2024-11-09T10:04:45.000Z",
  pitOutTime: "2024-11-09T10:05:15.000Z",  // Exit time recorded
  pitDuration: 30,                         // Total pit duration
  pitCurrentDuration: 0,                   // Reset to 0
  pitTotalDuration: 30,                    // Final total
  isActive: false                          // Completed
}
```

## Sample Log Testing

### **Real-World Pit Test Log**
Created `backend/sample-logs/real-world-pit-test.txt` with:

**VANHAT KOIRAT Pit Stop (30 seconds)**:
```
[10:04:45] r17788c15|pit|1 + r17788c14|otr|01.
[10:04:46] r17788c15|pit|2 + r17788c14|otr|02.
...
[10:04:54] r17788c15|pit|10 + r17788c14|otr|10.
[10:05:15] r17788c15|pit|0 + r17788c14|otr|00.  # Exit after 30 seconds
```

**SPEED DEMONS Pit Stop (21 seconds)**:
```
[10:07:15] r17799c15|pit|1 + r17799c14|otr|01.
[10:07:16] r17799c15|pit|2 + r17799c14|otr|02.
...
[10:07:19] r17799c15|pit|5 + r17799c14|otr|05.
[10:07:35] r17799c15|pit|0 + r17799c14|otr|00.  # Exit after 21 seconds
```

## Expected Database Results

### **Pit Stops Created**: 2
```javascript
// VANHAT KOIRAT pit stop
{
  competitorId: ObjectId("17788"),
  pitInTime: "2024-11-09T10:04:45.000Z",
  pitOutTime: "2024-11-09T10:05:15.000Z",
  pitDuration: 30,
  pitCurrentDuration: 0,
  pitTotalDuration: 30,
  lapNumber: 3,
  isActive: false
}

// SPEED DEMONS pit stop  
{
  competitorId: ObjectId("17799"),
  pitInTime: "2024-11-09T10:07:15.000Z",
  pitOutTime: "2024-11-09T10:07:35.000Z", 
  pitDuration: 20,
  pitCurrentDuration: 0,
  pitTotalDuration: 20,
  lapNumber: 4,
  isActive: false
}
```

## Logging Output

### **Pit Entry Detection**
```
🏁 Numeric pit status: VANHAT KOIRAT - 1 seconds in pit
🏁 Creating pit entry for: VANHAT KOIRAT
🏁 Pit entry recorded: VANHAT KOIRAT - Lap 3 - PitStop ID: 674a1b2c3d4e5f6789012345
```

### **Real-time Duration Updates**
```
⏱️ Updated pit duration: VANHAT KOIRAT - current: 2s
⏱️ Updated pit duration: VANHAT KOIRAT - current: 3s
⏱️ Updated pit duration: VANHAT KOIRAT - current: 4s
⏱️ Pit duration update: 17788 -> 05.
⏱️ Updated pit current duration: VANHAT KOIRAT - 5s
```

### **Pit Exit Detection**
```
🏁 Numeric pit status: VANHAT KOIRAT - 0 seconds in pit
🏁 Processing pit exit for: VANHAT KOIRAT
🏁 Pit exit recorded: VANHAT KOIRAT - Duration: 30.0s - Total: 30.0s
```

## Benefits

### ✅ **Real-time Pit Tracking**
- Live updates of pit duration as it happens
- Accurate pit entry/exit detection
- Real-time dashboard capabilities

### ✅ **Flexible Format Support**
- Handles both numeric (6) and string (IN/OUT) pit statuses
- Supports multiple duration formats (06., 00:00:06)
- Backward compatible with existing logs

### ✅ **Comprehensive Duration Tracking**
- `pitCurrentDuration`: Real-time current pit time
- `pitTotalDuration`: Cumulative pit time across all stops
- `pitDuration`: Individual pit stop duration

### ✅ **Robust Error Handling**
- Handles missing pit stops gracefully
- Creates pit entries when duration appears without entry
- Validates numeric formats properly

## Testing Commands

### **Load Real-World Test Log**
```bash
# Use the real-world pit test log
cat backend/sample-logs/real-world-pit-test.txt
```

### **Verify Database Results**
```javascript
// Check pit stops created
db.apex_pitstops.find({}).sort({pitInTime: 1})

// Check real-time duration updates
db.apex_pitstops.find({pitCurrentDuration: {$gt: 0}})

// Check completed pit stops
db.apex_pitstops.find({pitOutTime: {$exists: true}})
```

This implementation now properly handles real-world pit status data with numeric values and duration tracking, ensuring accurate pit stop detection and recording.
