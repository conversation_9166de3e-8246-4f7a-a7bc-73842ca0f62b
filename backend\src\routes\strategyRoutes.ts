import express from 'express';
import * as strategyController from '../controllers/strategyController';
import * as stintController from '../controllers/stintController';

const router = express.Router();

// Strategy routes
router.get('/', strategyController.getStrategy);
router.get('/all', strategyController.getAllStrategies);
router.get('/:id', strategyController.getStrategyById);
router.post('/', strategyController.createStrategy);
router.put('/', strategyController.updateStrategy);
router.delete('/:id', strategyController.deleteStrategy);
router.put('/:id/activate', strategyController.activateStrategy);
router.post('/:id/duplicate', strategyController.duplicateStrategy);
router.post('/:id/calculate-stints', strategyController.calculateAndSaveStints);
router.post('/start', strategyController.startRace);
router.post('/stop', strategyController.stopRace); // Now we can add this back
router.post('/reset', strategyController.resetStrategyToDefaults);

// Stint routes
router.get('/:id/stints', stintController.getStintsByStrategy);

export default router;
