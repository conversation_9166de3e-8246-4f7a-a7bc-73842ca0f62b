import express from "express";
import { register, login, getCurrentUser, updateUserPreferences } from "../controllers/authController";
import { protect } from "../middleware/authMiddleware";

const router = express.Router();

// Public routes
router.post("/register", register);
router.post("/login", login);

// Protected routes
router.get("/me", protect, getCurrentUser);
router.put("/preferences", protect, updateUserPreferences);

export default router;