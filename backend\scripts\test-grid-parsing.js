#!/usr/bin/env node

/**
 * Script to test grid parsing for both 24h Serres and Master Vittoria formats
 * This script will parse both log files and compare the grid structures
 */

const fs = require('fs');
const path = require('path');

// Import the grid parser (we'll need to compile TypeScript or use a simple version)
const parseGridData = (grid) => {
  const id = Date.now();
  const drivers = {};
  const header = { types: {}, labels: {} };

  if (!grid) {
    console.warn('No grid data provided');
    return { drivers, header_labels: header.labels, header_types: header.types, id };
  }

  // Regex to capture each row with a `data-id` attribute
  const rowsRegex = /<tr[^>]*data-id="(r\d+)"[^>]*>(.*?)<\/tr>/gs;
  let rowMatch;

  while ((rowMatch = rowsRegex.exec(grid)) !== null) {
    const rowId = rowMatch[1]; // Extract the row ID (e.g., "r0", "r3")
    let driver = null; // Initialize driver only if needed

    // Regex to match all <td> or nested <div>/<p> with data-id attribute inside the row
    const cellRegex = /<td[^>]*data-id="(r?\d*c\d+|c\d+)"[^>]*>(.*?)<\/td>|<(?:div|p)[^>]*data-id="(r?\d*c\d+|c\d+)"[^>]*>(.*?)<\/(?:div|p)>/gs;
    let cellMatch;

    // Process each cell in the row
    while ((cellMatch = cellRegex.exec(rowMatch[2])) !== null) {
      const cellId = cellMatch[1] || cellMatch[3]; // Either <td> or nested <div>/<p> ID
      const cleanCellId = cellId.replace(/^r?\d*/, ""); // Remove row number part of the ID
      const cellContent = (cellMatch[2] || cellMatch[4] || "")
        .replace(/<[^>]+>/g, "")
        .trim(); // Clean cell content by stripping HTML tags

      if (rowId === "r0") {
        // For the header row (r0), capture the labels and types
        const headerTypeMatch = /data-type="([^"]*)"/.exec(cellMatch[0]);
        let headerType = headerTypeMatch ? headerTypeMatch[1] : "";

        // If the data-type is empty, assign a custom headerType based on cell content
        if (!headerType) {
          headerType = cellContent.toLowerCase().replace(/\s+/g, "");
        }

        if (headerType) {
          header.types[cleanCellId] = headerType;
        }
        header.labels[cleanCellId] = cellContent;
      } else {
        // Initialize the driver if it hasn't been initialized already
        if (!driver) {
          driver = {
            gridId: id,
            grp: { type: "", value: "" },
            sta: { type: "", value: "" },
            rk: { type: "", value: "" },
            no: { type: "", value: "" },
            dr: { type: "", value: "" },
            s1: { type: "", value: "" },
            s2: { type: "", value: "" },
            s3: { type: "", value: "" },
            sp1: { type: "", value: "" },
            llp: { type: "", value: "" },
            blp: { type: "", value: "" },
            gap: { type: "", value: "" },
            tlp: { type: "", value: "" },
            nat: { type: "", value: "" },
            otr: { type: "", value: "" },
            pit: { type: "", value: "" },
            int: { type: "", value: "" },
            Laps: [],
            Pits: [],
            pastKartIds: [],
            currentKartId: 0,
          };
        }

        // Get the header type for this column
        const headerType = header.types[cleanCellId];

        if (headerType && driver.hasOwnProperty(headerType)) {
          driver[headerType] = { type: headerType, value: cellContent };
        } else if (headerType) {
          // Create dynamic property for unknown header types
          driver[headerType] = { type: headerType, value: cellContent };
        } else {
          // If headerType is missing, create a new type name based on cell content
          const dynamicType = cellContent.toLowerCase().replace(/\s+/g, "");
          if (dynamicType) {
            driver[dynamicType] = { type: dynamicType, value: cellContent };
          }
        }
      }
    }

    // Store the driver data if it was created
    if (driver) {
      drivers[rowId] = driver;
    }
  }

  return { drivers, header_labels: header.labels, header_types: header.types, id };
};

// Function to extract grid data from log file
const extractGridFromLog = (logContent, raceName) => {
  console.log(`\n🔍 Extracting grid data from ${raceName}...`);
  
  // Find all grid entries
  const gridRegex = /grid\|\|([^]*?)(?=\n[^\s]|\n\n|\n$)/g;
  const grids = [];
  let match;
  
  while ((match = gridRegex.exec(logContent)) !== null) {
    grids.push(match[1].trim());
  }
  
  console.log(`   Found ${grids.length} grid entries`);
  
  if (grids.length === 0) {
    console.log(`   ❌ No grid data found in ${raceName}`);
    return null;
  }
  
  // Use the first grid entry for analysis
  const gridData = grids[0];
  console.log(`   📊 Using first grid entry (${gridData.length} characters)`);
  
  return parseGridData(gridData);
};

// Function to analyze parsed grid data
const analyzeGridData = (gridResult, raceName) => {
  console.log(`\n📈 Analysis for ${raceName}:`);
  console.log(`   Drivers found: ${Object.keys(gridResult.drivers).length}`);
  console.log(`   Header columns: ${Object.keys(gridResult.header_types).length}`);
  
  // Show header mapping
  console.log(`\n   📋 Header mapping:`);
  for (const [column, type] of Object.entries(gridResult.header_types)) {
    const label = gridResult.header_labels[column] || 'N/A';
    console.log(`      ${column}: ${type} ("${label}")`);
  }
  
  // Show first few drivers
  console.log(`\n   👥 Sample drivers:`);
  const driverIds = Object.keys(gridResult.drivers).slice(0, 3);
  for (const driverId of driverIds) {
    const driver = gridResult.drivers[driverId];
    const name = driver.dr?.value || 'N/A';
    const kartNumber = driver.no?.value || 'N/A';
    const nationality = driver.nat?.value || 'N/A';
    console.log(`      ${driverId}: "${name}" (Kart: ${kartNumber}, Nation: ${nationality})`);
  }
  
  return gridResult;
};

// Function to compare grid structures
const compareGrids = (serresGrid, vittoriaGrid) => {
  console.log(`\n🔄 Comparing grid structures:`);
  
  // Compare header types
  const serresHeaders = Object.keys(serresGrid.header_types);
  const vittoriaHeaders = Object.keys(vittoriaGrid.header_types);
  
  console.log(`\n   📊 Header columns:`);
  console.log(`      24h Serres: ${serresHeaders.length} columns`);
  console.log(`      Master Vittoria: ${vittoriaHeaders.length} columns`);
  
  // Find common and different headers
  const commonHeaders = serresHeaders.filter(h => vittoriaHeaders.includes(h));
  const serresOnly = serresHeaders.filter(h => !vittoriaHeaders.includes(h));
  const vittoriaOnly = vittoriaHeaders.filter(h => !serresHeaders.includes(h));
  
  console.log(`\n   🤝 Common headers (${commonHeaders.length}):`);
  commonHeaders.forEach(h => {
    const serresType = serresGrid.header_types[h];
    const vittoriaType = vittoriaGrid.header_types[h];
    const match = serresType === vittoriaType ? '✅' : '❌';
    console.log(`      ${h}: ${serresType} vs ${vittoriaType} ${match}`);
  });
  
  if (serresOnly.length > 0) {
    console.log(`\n   🔵 24h Serres only (${serresOnly.length}):`);
    serresOnly.forEach(h => {
      console.log(`      ${h}: ${serresGrid.header_types[h]} ("${serresGrid.header_labels[h]}")`);
    });
  }
  
  if (vittoriaOnly.length > 0) {
    console.log(`\n   🟡 Master Vittoria only (${vittoriaOnly.length}):`);
    vittoriaOnly.forEach(h => {
      console.log(`      ${h}: ${vittoriaGrid.header_types[h]} ("${vittoriaGrid.header_labels[h]}")`);
    });
  }
  
  // Check critical fields
  console.log(`\n   🎯 Critical field mapping:`);
  const criticalFields = ['no', 'dr', 'nat', 'llp', 'blp', 'rk', 'sta'];
  
  criticalFields.forEach(field => {
    const serresHas = Object.values(serresGrid.header_types).includes(field);
    const vittoriaHas = Object.values(vittoriaGrid.header_types).includes(field);
    const status = serresHas && vittoriaHas ? '✅' : serresHas ? '🔵' : vittoriaHas ? '🟡' : '❌';
    console.log(`      ${field}: ${status} (Serres: ${serresHas}, Vittoria: ${vittoriaHas})`);
  });
};

// Main execution
const main = () => {
  console.log('🚀 Grid Parsing Test Script');
  console.log('============================');
  
  const apexParserDir = path.join(__dirname, '..', 'apex parser files');
  const serresFile = path.join(apexParserDir, '24h serres.txt');
  const vittoriaFile = path.join(apexParserDir, 'master vitoria.txt');
  
  // Check if files exist
  if (!fs.existsSync(serresFile)) {
    console.error(`❌ File not found: ${serresFile}`);
    return;
  }
  
  if (!fs.existsSync(vittoriaFile)) {
    console.error(`❌ File not found: ${vittoriaFile}`);
    return;
  }
  
  try {
    // Read log files
    console.log('📖 Reading log files...');
    const serresContent = fs.readFileSync(serresFile, 'utf8');
    const vittoriaContent = fs.readFileSync(vittoriaFile, 'utf8');
    
    console.log(`   24h Serres: ${serresContent.length} characters`);
    console.log(`   Master Vittoria: ${vittoriaContent.length} characters`);
    
    // Extract and parse grid data
    const serresGrid = extractGridFromLog(serresContent, '24h Serres');
    const vittoriaGrid = extractGridFromLog(vittoriaContent, 'Master Vittoria');
    
    if (!serresGrid || !vittoriaGrid) {
      console.error('❌ Failed to extract grid data from one or both files');
      return;
    }
    
    // Analyze each grid
    analyzeGridData(serresGrid, '24h Serres');
    analyzeGridData(vittoriaGrid, 'Master Vittoria');
    
    // Compare grids
    compareGrids(serresGrid, vittoriaGrid);
    
    console.log('\n✅ Grid parsing test completed successfully!');
    
  } catch (error) {
    console.error('❌ Error during grid parsing test:', error.message);
  }
};

// Run the script
if (require.main === module) {
  main();
}

module.exports = { parseGridData, extractGridFromLog, analyzeGridData, compareGrids };
