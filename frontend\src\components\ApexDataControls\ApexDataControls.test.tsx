import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { DataProvider } from '../../context/DataContext';
import ApexDataControls from './ApexDataControls';

// Mock the API service
jest.mock('../../services/apiService', () => ({
  get: jest.fn(),
}));

const mockApiService = require('../../services/apiService');

// Mock data
const mockSessions = [
  {
    _id: '64a1b2c3d4e5f6789012345a',
    title1: 'EKO 4H of Serres',
    title2: 'Final',
    track: 'Karting Track (1200m)',
    raceId: 'EKO 4H of Serres',
    isActive: true,
    createdAt: '2024-01-15T10:30:00.000Z'
  },
  {
    _id: '64a1b2c3d4e5f6789012345b',
    title1: 'Test Race',
    title2: 'Practice',
    track: 'Test Track',
    raceId: 'Test Race',
    isActive: false,
    createdAt: '2024-01-14T09:00:00.000Z'
  }
];

const mockTeams = [
  {
    _id: '64a1b2c3d4e5f6789012346a',
    teamId: '17749',
    name: 'RNT',
    kartNumber: 1,
    nationality: 'Unknown',
    status: 'active',
    sessionId: '64a1b2c3d4e5f6789012345a'
  },
  {
    _id: '64a1b2c3d4e5f6789012346b',
    teamId: '17733',
    name: 'ATLAS GT2',
    kartNumber: 2,
    nationality: 'Unknown',
    status: 'active',
    sessionId: '64a1b2c3d4e5f6789012345a'
  }
];

// Test component wrapper
const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <DataProvider>
    {children}
  </DataProvider>
);

describe('ApexDataControls', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    localStorage.clear();
  });

  test('renders with default props', () => {
    render(
      <TestWrapper>
        <ApexDataControls />
      </TestWrapper>
    );

    expect(screen.getByText('Apex Database Data')).toBeInTheDocument();
    expect(screen.getByText('Use live data from Apex racing sessions')).toBeInTheDocument();
    expect(screen.getByText('Use Apex Database')).toBeInTheDocument();
  });

  test('renders with custom props', () => {
    render(
      <TestWrapper>
        <ApexDataControls 
          title="Custom Title"
          description="Custom description"
          showTeamSelection={true}
        />
      </TestWrapper>
    );

    expect(screen.getByText('Custom Title')).toBeInTheDocument();
    expect(screen.getByText('Custom description')).toBeInTheDocument();
  });

  test('shows manual data entry message when apex database is disabled', () => {
    render(
      <TestWrapper>
        <ApexDataControls />
      </TestWrapper>
    );

    expect(screen.getByText('Using Manual Data Entry')).toBeInTheDocument();
    expect(screen.getByText('Enable Apex Database to use live race data from parsed sessions.')).toBeInTheDocument();
  });

  test('enables apex database and loads sessions', async () => {
    mockApiService.get.mockResolvedValueOnce({ data: mockSessions });

    render(
      <TestWrapper>
        <ApexDataControls />
      </TestWrapper>
    );

    // Find and click the toggle
    const toggle = screen.getByRole('checkbox');
    fireEvent.click(toggle);

    // Wait for sessions to load
    await waitFor(() => {
      expect(mockApiService.get).toHaveBeenCalledWith('/debug/sessions');
    });

    // Check that session selection appears
    expect(screen.getByText('Session Selection')).toBeInTheDocument();
  });

  test('loads teams when session is selected', async () => {
    mockApiService.get
      .mockResolvedValueOnce({ data: mockSessions })
      .mockResolvedValueOnce({ data: mockTeams });

    render(
      <TestWrapper>
        <ApexDataControls showTeamSelection={true} />
      </TestWrapper>
    );

    // Enable apex database
    const toggle = screen.getByRole('checkbox');
    fireEvent.click(toggle);

    // Wait for sessions to load
    await waitFor(() => {
      expect(mockApiService.get).toHaveBeenCalledWith('/debug/sessions');
    });

    // Wait for teams to load (should happen automatically for first session)
    await waitFor(() => {
      expect(mockApiService.get).toHaveBeenCalledWith('/apex/sessions/64a1b2c3d4e5f6789012345a/teams');
    });

    // Check that team selection appears
    expect(screen.getByText('Team Selection (for Race Strategy)')).toBeInTheDocument();
  });

  test('shows no sessions message when no sessions available', async () => {
    mockApiService.get.mockResolvedValueOnce({ data: [] });

    render(
      <TestWrapper>
        <ApexDataControls />
      </TestWrapper>
    );

    // Enable apex database
    const toggle = screen.getByRole('checkbox');
    fireEvent.click(toggle);

    // Wait for sessions to load
    await waitFor(() => {
      expect(screen.getByText('No Sessions Found')).toBeInTheDocument();
    });

    expect(screen.getByText('No Apex sessions available. Parse some race data first to create sessions.')).toBeInTheDocument();
  });

  test('shows no teams message when no teams available', async () => {
    mockApiService.get
      .mockResolvedValueOnce({ data: mockSessions })
      .mockResolvedValueOnce({ data: [] });

    render(
      <TestWrapper>
        <ApexDataControls showTeamSelection={true} />
      </TestWrapper>
    );

    // Enable apex database
    const toggle = screen.getByRole('checkbox');
    fireEvent.click(toggle);

    // Wait for data to load
    await waitFor(() => {
      expect(screen.getByText('No Teams Found')).toBeInTheDocument();
    });

    expect(screen.getByText('No teams available in the selected session. Make sure the session has been parsed and teams have been created.')).toBeInTheDocument();
  });

  test('persists settings in localStorage', async () => {
    mockApiService.get.mockResolvedValueOnce({ data: mockSessions });

    render(
      <TestWrapper>
        <ApexDataControls />
      </TestWrapper>
    );

    // Enable apex database
    const toggle = screen.getByRole('checkbox');
    fireEvent.click(toggle);

    // Check localStorage
    await waitFor(() => {
      expect(localStorage.getItem('useApexDatabase')).toBe('true');
      expect(localStorage.getItem('selectedSessionId')).toBe('64a1b2c3d4e5f6789012345a');
    });
  });

  test('handles API errors gracefully', async () => {
    mockApiService.get.mockRejectedValueOnce(new Error('API Error'));

    render(
      <TestWrapper>
        <ApexDataControls />
      </TestWrapper>
    );

    // Enable apex database
    const toggle = screen.getByRole('checkbox');
    fireEvent.click(toggle);

    // Wait for error to be handled
    await waitFor(() => {
      expect(mockApiService.get).toHaveBeenCalledWith('/debug/sessions');
    });

    // Error should be handled gracefully (no crash)
    expect(screen.getByText('Session Selection')).toBeInTheDocument();
  });
});

export default {};
