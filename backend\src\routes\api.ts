// backend/src/routes/api.ts
import express from "express";
import teamRoutes from "./teamRoutes";
import kartRoutes from "./kartRoutes";
import lapRoutes from "./lapRoutes";
import driverRoutes from "./driverRoutes";
import rowRoutes from "./rowRoutes";
import pitlaneRoutes from "./pitlaneRoutes";
import assignmentLogRoutes from "./assignmentLogRoutes";
import adminRoutes from "./adminRoutes";
import strategyRoutes from "./strategyRoutes";
import authRoutes from "./authRoutes";
import stintRoutes from "./stintRoutes";
import apexRoutes from "./apexRoutes";
import replayRoutes from "./replayRoutes";
import debugRoutes from "./debugRoutes";

const router = express.Router();

// Auth routes
router.use("/auth", authRoutes);

// API routes
router.use("/teams", teamRoutes);
router.use("/karts", kartRoutes);
router.use("/laps", lapRoutes);
router.use("/drivers", driverRoutes);
router.use("/rows", rowRoutes);
router.use("/pitlane", pitlaneRoutes);
router.use("/assignment-history", assignmentLogRoutes);
router.use("/admin", adminRoutes);
router.use("/strategy", strategyRoutes);
router.use("/stint", stintRoutes); // Fixed: removed '/api' prefix
router.use("/apex", apexRoutes); // Apex parser routes
router.use("/replay", replayRoutes); // Log replay routes
router.use("/debug", debugRoutes); // Debug and monitoring routes
export default router;
