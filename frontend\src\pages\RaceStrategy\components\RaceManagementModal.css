/* Race Management Modal Styles */
.race-management-content {
  height: 100%;
  overflow-y: auto;
}

/* Strategy Header Card */
.strategy-header-card {
  margin: 16px;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.strategy-header-card ion-card-header {
  padding-bottom: 8px;
}

.strategy-header-card ion-card-content {
  padding-top: 0;
}

.race-type-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 1rem;
  color: var(--ion-color-medium);
}

/* Quick Rules Overview */
.quick-rules-container {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
  padding: 0 16px 16px;
}

.quick-rule {
  display: flex;
  align-items: center;
  gap: 12px;
  background: var(--ion-card-background);
  border-radius: 10px;
  padding: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.quick-rule-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  background-color: var(--ion-color-light);
  border-radius: 50%;
}

.quick-rule-icon ion-icon {
  font-size: 20px;
  color: var(--ion-color-primary);
}

.quick-rule-content {
  display: flex;
  flex-direction: column;
}

.quick-rule-label {
  font-size: 0.8rem;
  color: var(--ion-color-medium);
}

.quick-rule-value {
  font-size: 1rem;
  font-weight: 500;
}

/* Detailed Rules Accordion */
ion-accordion-group {
  margin: 0 16px 16px;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

ion-accordion {
  background: var(--ion-card-background);
}

.detailed-rules-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
}

.detailed-rule {
  display: flex;
  flex-direction: column;
}

.detailed-rule-label {
  font-size: 0.8rem;
  color: var(--ion-color-medium);
}

.detailed-rule-value {
  font-size: 1rem;
  font-weight: 500;
}

/* Strategy Selector */
.strategy-selector-toolbar {
  --background: var(--ion-color-light);
  --border-color: transparent;
}

.strategy-selector {
  width: 100%;
  max-width: 100%;
  --padding-start: 16px;
  --padding-end: 16px;
  font-weight: 500;
}

/* Dark mode adjustments */
@media (prefers-color-scheme: dark) {
  .quick-rule,
  ion-accordion {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  }
  
  .quick-rule-icon {
    background-color: var(--ion-color-dark-shade);
  }
  
  .strategy-selector-toolbar {
    --background: var(--ion-color-dark-shade);
  }
}

/* Ripple Effect */
.ripple-parent {
  position: relative;
  overflow: hidden;
}

/* Make sure padding is applied correctly */
.ion-padding {
  padding: 16px;
  height: auto;
  min-height: 100%;
}

/* Calculate Strategy button */
.action-item ion-icon[name="calculator-outline"] {
  font-size: 1.2rem;
}

/* Strategy detail item for stints */
.strategy-detail-item:last-child {
  margin-top: 8px;
}




