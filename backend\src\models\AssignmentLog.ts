// backend/src/models/AssignmentLog.ts
import mongoose, { Document, Schema, Types } from 'mongoose';

// Define the types of events you want to log
type AssignmentEventType =
    | 'ASSIGNED_TO_ROW'     // Kart moved from Team/Available -> Row
    | 'ASSIGNED_TO_TEAM'    // Kart moved from Row/Available -> Team
    | 'SWAP_TEAM_ROW'       // Direct swap occurred (logs two related events or one complex one)
    | 'UNASSIGNED_FROM_ROW' // Kart left row (e.g., to maintenance, retired, or back to team)
    | 'UNASSIGNED_FROM_TEAM';// Kart left team (e.g., to row, maintenance, retired)

export interface IAssignmentLog extends Document {
  timestamp: Date;          // When the event occurred
  eventType: AssignmentEventType;
  kartId: Types.ObjectId;   // The kart involved
  teamId?: Types.ObjectId | null; // Team involved (current or previous, depending on event)
  rowId?: Types.ObjectId | null;  // Row involved (current or previous, depending on event)
  // Optional: Store previous state for easier querying
  previousTeamId?: Types.ObjectId | null;
  previousRowId?: Types.ObjectId | null;
  // Optional: Link related swap events if logging separately
  relatedLogId?: Types.ObjectId | null;
}

const assignmentLogSchema = new Schema<IAssignmentLog>({
  timestamp: { type: Date, default: Date.now, required: true },
  eventType: { type: String, required: true, enum: ['ASSIGNED_TO_ROW', 'ASSIGNED_TO_TEAM', 'SWAP_TEAM_ROW', 'UNASSIGNED_FROM_ROW', 'UNASSIGNED_FROM_TEAM'] },
  kartId: { type: Schema.Types.ObjectId, ref: 'Kart', required: true, index: true }, // Index for faster kart history lookup
  teamId: { type: Schema.Types.ObjectId, ref: 'Team', default: null, index: true }, // Index for faster team history lookup
  rowId: { type: Schema.Types.ObjectId, ref: 'Row', default: null, index: true }, // Index for faster row history lookup
  previousTeamId: { type: Schema.Types.ObjectId, ref: 'Team', default: null },
  previousRowId: { type: Schema.Types.ObjectId, ref: 'Row', default: null },
  relatedLogId: { type: Schema.Types.ObjectId, ref: 'AssignmentLog', default: null },
}, { timestamps: true }); // Adds createdAt/updatedAt

// Optional: Compound index if you often query by multiple fields
// assignmentLogSchema.index({ teamId: 1, timestamp: -1 });

export const AssignmentLog = mongoose.model<IAssignmentLog>('AssignmentLog', assignmentLogSchema);
