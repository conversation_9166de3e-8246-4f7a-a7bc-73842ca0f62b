appApex.factory('apexParserService',
    ["raceService",
        function (raceService) {
            var factory = {};

            factory.getDriverNameFromGrid = function(rowCol){
                if(!rowCol)
                    return;

                var result = "";

                jQuery('#tgrid tr').each(function (i, row)
                {
                     var jQueryrow = jQuery(row);

                     var trId = jQueryrow.attr("data-id");
                     if(trId === 'r0'){

                     }
                    else{
                        jQueryrow.find('td').each(function(i,col){
                            var dataType = jQuery(col).attr("data-id");
                            var dataValue = jQuery(col).val();

                            if(dataType == rowCol){
                                result = dataValue;
                            }
                        
                        })
                    }

                   
                });

                return result;
            }

            factory.raceLayout = function(){
                var layout = {
                    "grp":"",
                    "sta":"",
                    "rk":"",
                    "no":"",
                    "dr":"",
                    "s1":"",
                    "s2":"",
                    "s3": "",
                    "llp":"",
                    "blp":"",
                    "int":"",
                    "gap":"",
                    "tlp":"",
                    "pit":"",
                    "nat": "",
                    "otr":"" //tempo in pista
                }

                return layout;
            }
            
            //Cerca il numero di colonne presenti in gara
            factory.findRaceLayout = function(tr){
                if(!tr){
                    console.log("Non sono riuscito a costruire un layout di gara")
                    return;
                }
                
                var layout = factory.raceLayout();

                tr.find('td').each(function(i,col){
                    var dataType = jQuery(col).attr("data-type");
                    var dataId = jQuery(col).attr("data-id");

                    if(layout.hasOwnProperty(dataType)){
                        layout[dataType] = dataId;
                    }
                    else {
                        console.log("Il layout di gara non contiene la colonna: "+ dataType);
                        console.log("DataId: "+ dataId);
                        console.log("DataType: "+ dataType);
                    }
                    
                    
                })

                return layout;
            }

            factory.getGrid = function(data, isLightRed){
                if(!data)
                    return [];

                //Elenco dei piloti. risultati di ritorno
                var driverModels = [];

                //Scorro le righe della tabella
                jQuery(data).find('tr').each(function (i, row)
                {
                    var jQueryrow = jQuery(row);

                    var trId = jQueryrow.attr("data-id");
                    if(trId == "r0"){
                        var layout = factory.findRaceLayout(jQueryrow);
                        raceService.raceLayout = layout;
                    }
                    else{
                        var driverModel = raceService.driverModel();

                        jQueryrow.find('td').each(function(i,col){
                            var dataType = jQuery(col).attr("class");
                            var dataAttrId = jQuery(col).attr("data-id");
                            var dataValue = col.innerHTML;
                            var dataId = raceService.buildDataId(dataAttrId);

                            if(dataType == "no"){
                                dataAttrId = jQuery(col.innerHTML).attr("data-id");
                                
                                //console.log("dataAttrId:"+dataAttrId)
                                dataValue = jQuery(col.innerHTML).text();
                                dataId = raceService.buildDataId(dataAttrId);
                                     //console.log("dataValue:"+dataValue)
                                //console.log("dataId:"+dataId)
                            }
                            else if(dataType == "rk" && !isLightRed){
                                //else if(dataType == "rk"){
                                dataAttrId = jQuery(col.innerHTML).find("p").attr("data-id");
                                //dataAttrId = jQueryrow.attr("data-pos");                                
                                //console.log("dataAttrId:"+dataAttrId)
                                dataValue = jQueryrow.attr("data-pos");
                                dataId = raceService.buildDataId(dataAttrId);
                                //console.log("dataValue:"+dataValue)
                                //console.log("dataId:"+dataId)
                            }

                            raceService.populateDriverModel(driverModel, dataAttrId, dataType, dataValue, dataId);

                            if(!driverModel.dataId )
                            throw Error("dataId null")
                        })

                        if(driverModel.dataId && driverModel.name)
                            driverModels.push(driverModel);
                    }
                });

                return driverModels;                
            }


          

            return factory;
        }]);