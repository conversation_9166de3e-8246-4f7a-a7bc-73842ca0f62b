import { Schema, model, Types } from "mongoose";
import { Document } from 'mongoose'; // Import Document
import { IKart } from "./Kart";
import { IPit } from "./Pit";
import { ILap } from "./Lap";
import { IDriver } from "./Driver";

// Interface extending Document
export interface ITeam extends Document {
  _id: Types.ObjectId;
  name: string;
  number: number;
  currentKartId?: Types.ObjectId | IKart | null; // Optionality correct (can be null)
  pastKarts: (Types.ObjectId | IKart )[]; // Property exists, array might be empty
  pits: (Types.ObjectId | IPit)[]; // Property exists, array might be empty
  laps: (Types.ObjectId | ILap)[]; // Property exists, array might be empty
  drivers: (Types.ObjectId | IDriver)[]; // Property exists, array might be empty
  createdAt: Date;
  updatedAt: Date;
}

// Schema remains the same
const teamSchema = new Schema<ITeam>({
  name: {
    type: String,
    required: [true, "Team name is required"],
    trim: true,
  },
  number: {
    type: Number,
    required: [true, "Team number is required"],
    unique: true,
    index: true,
  },
  currentKartId: {
    type: Schema.Types.ObjectId,
    ref: "Kart",
    default: null,
  },
  pastKarts: [{ type: Schema.Types.ObjectId, ref: "Kart" }],
  pits: [{ type: Schema.Types.ObjectId, ref: "Pit" }],
  laps: [{ type: Schema.Types.ObjectId, ref: "Lap" }],
  drivers: [{ type: Schema.Types.ObjectId, ref: "Driver" }],
}, {
  timestamps: true
});

export const Team = model<ITeam>('Team', teamSchema);
