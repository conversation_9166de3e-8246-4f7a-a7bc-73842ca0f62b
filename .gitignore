# Dependency directories
node_modules/
frontend/node_modules/
backend/node_modules/

# Build output folders
frontend/build/
backend/build/

# Vite cache
frontend/.vite/

# TypeScript cache file
*.tsbuildinfo

# Environment variables
.env
backend/.env
frontend/.env

.env.*
!.env.example

# Logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# OS generated files
.DS_Store
Thumbs.db

# Editor directories and files
.vscode/
.idea/
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Test results and coverage
coverage/
.nyc_output/

# Optional: Uncomment if you don't want to commit package lock files
# package-lock.json
# yarn.lock
# pnpm-lock.yaml
