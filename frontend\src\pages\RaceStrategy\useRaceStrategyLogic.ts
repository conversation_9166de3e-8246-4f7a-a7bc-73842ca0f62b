import { useState, useEffect, useCallback } from 'react';
import { RefresherEventDetail } from '@ionic/core';
import apiService from '../../services/apiService';
import useWebSocket from '../../hooks/webSocketHandler';
import { StrategyData } from '../../types';
import { StintInfo, StrategyResult } from './types';

export interface UseRaceStrategyLogicReturn {
    strategyData: StrategyData | null;
    isLoading: boolean;
    error: string | null;
    toastMessage: string | null;
    toastColor: "success" | "danger" | "warning" | "medium";
    showEditStrategyModal: boolean;
    currentRaceTime: number;
    showStrategyModal: boolean;
    showManagementModal: boolean;
    showDeleteAlert: boolean;
    stintStrategy: StrategyResult | null;

    fetchStrategyData: () => Promise<void>;
    handleEditStrategy: () => void;
    handleDeleteStrategy: () => void;
    confirmDeleteStrategy: () => Promise<void>;
    handleStrategyUpdate: (updatedStrategy: StrategyData) => void;
    handleCreateStrategy: () => void;
    handleRefresh: (event: CustomEvent<RefresherEventDetail>) => Promise<void>;
    handleSelectStrategy: (selectedStrategyData: StrategyData) => void;
    handleCalculateStints: () => Promise<void>;
    handleStintUpdated: (updatedStint: StintInfo) => Promise<void>;

    setShowEditStrategyModal: React.Dispatch<React.SetStateAction<boolean>>;
    setShowStrategyModal: React.Dispatch<React.SetStateAction<boolean>>;
    setShowManagementModal: React.Dispatch<React.SetStateAction<boolean>>;
    setShowDeleteAlert: React.Dispatch<React.SetStateAction<boolean>>;
    setToastMessage: React.Dispatch<React.SetStateAction<string | null>>;

    getCurrentStintNumber: () => number;
    getPitStopsDone: () => number;
    getExtraStintsUsed: () => number;
    getTotalExtraStints: () => number;
}

export const useRaceStrategyLogic = (): UseRaceStrategyLogicReturn => {
    const [strategyData, setStrategyData] = useState<StrategyData | null>(null);
    const [isLoading, setIsLoading] = useState<boolean>(true);
    const [error, setError] = useState<string | null>(null);
    const [toastMessage, setToastMessage] = useState<string | null>(null);
    const [toastColor, setToastColor] = useState<"success" | "danger" | "warning" | "medium">("medium");
    const [showEditStrategyModal, setShowEditStrategyModal] = useState<boolean>(false);
    const [currentRaceTime, setCurrentRaceTime] = useState<number>(0);
    const [showStrategyModal, setShowStrategyModal] = useState(false);
    const [showManagementModal, setShowManagementModal] = useState(false);
    const [showDeleteAlert, setShowDeleteAlert] = useState(false);
    const [stintStrategy, setStintStrategy] = useState<StrategyResult | null>(null);

    const showToast = useCallback((message: string, color: "success" | "danger" | "warning" | "medium") => {
        setToastMessage(message);
        setToastColor(color);
    }, []);

    const fetchStrategyData = useCallback(async () => {
        console.log('[RaceStrategy Logic] Fetching strategy data...');
        setIsLoading(true);
        setError(null);
        try {
            const response = await apiService.strategy.getActive();
            setStrategyData(response);
            console.log('[RaceStrategy Logic] Fetched strategy data:', response);
        } catch (err) {
            console.error('[RaceStrategy Logic] Error fetching strategy:', err);
            setError('Failed to load strategy data. Please try again.');
        } finally {
            setIsLoading(false);
        }
    }, []);

    const handleEditStrategy = useCallback(() => {
        if (!strategyData) {
            showToast('No strategy selected to edit.', 'warning');
            return;
        }
        setShowManagementModal(false);
        setShowEditStrategyModal(true);
    }, [strategyData, showToast]);

    const handleDeleteStrategy = useCallback(() => {
        if (!strategyData || !strategyData._id) {
            showToast('No strategy selected to delete.', 'warning');
            return;
        }
        setShowManagementModal(false);
        setShowDeleteAlert(true);
    }, [strategyData, showToast]);

    const confirmDeleteStrategy = useCallback(async () => {
        if (!strategyData || !strategyData._id) {
            showToast('Error: Strategy ID missing for deletion.', 'danger');
            return;
        }
        setIsLoading(true);
        try {
            await apiService.strategy.delete(strategyData._id);
            setStrategyData(null);
            fetchStrategyData();
            showToast('Strategy deleted successfully!', 'success');
        } catch (err) {
            console.error('[RaceStrategy Logic] Error deleting strategy:', err);
            setError('Failed to delete strategy');
            showToast('Failed to delete strategy. Please try again.', 'danger');
        } finally {
            setIsLoading(false);
            setShowDeleteAlert(false);
        }
    }, [strategyData, showToast, fetchStrategyData]);

    const handleStrategyUpdate = useCallback((updatedStrategy: StrategyData) => {
        console.log('[RaceStrategy Logic] Strategy updated:', updatedStrategy);
        setStrategyData(updatedStrategy);
        setShowEditStrategyModal(false);
        showToast('Strategy updated successfully!', 'success');
    }, [showToast]);

    const handleCreateStrategy = useCallback(() => {
        setShowManagementModal(false);
        setStrategyData(null);
        setShowEditStrategyModal(true);
    }, []);

    const handleWebSocketMessage = useCallback((message: { event: string; payload?: unknown }) => {
        const { event, payload } = message;
        console.log('[RaceStrategy Logic] WebSocket message received:', event, payload);
        switch (event) {
            case 'strategyUpdated':
            case 'strategyCreated':
            case 'strategyDeleted':
                console.log(`[RaceStrategy Logic] Strategy event '${event}' received via WebSocket.`);
                fetchStrategyData();
                break;
            default:
                break;
        }
    }, [fetchStrategyData]);

    useWebSocket(handleWebSocketMessage);

    useEffect(() => {
        fetchStrategyData();
    }, [fetchStrategyData]);

    useEffect(() => {
        if (!strategyData?.startTime) {
            setCurrentRaceTime(0);
            return;
        }
        const timer = setInterval(() => {
            const startTime = new Date(strategyData.startTime!).getTime();
            const now = new Date().getTime();
            const elapsedSeconds = Math.floor((now - startTime) / 1000);
            setCurrentRaceTime(elapsedSeconds);
        }, 1000);
        return () => clearInterval(timer);
    }, [strategyData?.startTime]);

    const handleRefresh = async (event: CustomEvent<RefresherEventDetail>) => {
        console.log('[RaceStrategy Logic] Handling pull-to-refresh.');
        await fetchStrategyData();
        event.detail.complete();
    };

    useEffect(() => {
        if (!strategyData) {
            console.log('[RaceStrategy Logic] No strategyData, clearing stintStrategy.');
            setStintStrategy(null);
            return;
        }
        console.log('[RaceStrategy Logic] Processing strategyData for stints:', strategyData);
        if (strategyData.stints && strategyData.stints.length > 0) {
            const totalRaceTimeSeconds = strategyData.raceDurationType === 'time'
                ? strategyData.raceDurationValue
                : strategyData.raceDurationValue * strategyData.avgLapTimeSeconds;
            const totalPitTimeSeconds = strategyData.mandatoryPitStops * strategyData.minPitDurationSeconds;
            const totalDrivingTimeSeconds = totalRaceTimeSeconds - totalPitTimeSeconds;
            const result: StrategyResult = {
                totalRaceTimeSeconds,
                totalPitTimeSeconds,
                totalDrivingTimeSeconds,
                idealStintTimeSeconds: strategyData.maxStintTimeSeconds,
                stints: strategyData.stints as StintInfo[],
                progress: totalRaceTimeSeconds > 0 ? Math.min(currentRaceTime / totalRaceTimeSeconds, 1) : 0
            };
            setStintStrategy(result);
            console.log('[RaceStrategy Logic] Using database stints:', result);
            setIsLoading(false);
        } else {
            console.log('[RaceStrategy Logic] StrategyData loaded but no stints found.');
            setStintStrategy(null);
            setIsLoading(false);
        }
    }, [strategyData, currentRaceTime]);

    const handleSelectStrategy = useCallback((selectedStrategyData: StrategyData) => {
        setStrategyData(selectedStrategyData);
        setShowStrategyModal(false);
        setShowManagementModal(false);
        showToast(`Strategy "${selectedStrategyData.raceName}" is now active.`, 'success');
    }, [showToast]);

    const handleCalculateStints = useCallback(async () => {
        console.log('[RaceStrategy Logic] Manual stint calculation triggered.');
        if (!strategyData || !strategyData._id) {
            showToast('No strategy selected to calculate stints for.', 'warning');
            return;
        }
        setShowManagementModal(false);
        setIsLoading(true);
        setError(null);
        setStintStrategy(null);
        try {
            const updatedStrategy = await apiService.strategy.calculateStints(strategyData._id);
            setStrategyData(updatedStrategy);
            console.log('[RaceStrategy Logic] Backend stint calculation complete.');
            showToast('Stints calculated successfully!', 'success');
        } catch (calcError) {
            console.error('[RaceStrategy Logic] Error triggering backend stint calculation:', calcError);
            setError('Failed to calculate strategy stints. Please check parameters.');
            showToast('Failed to calculate stints. Check strategy parameters.', 'danger');
        } finally {
            setIsLoading(false);
        }
    }, [strategyData, showToast]);

    const handleStintUpdated = useCallback(async (updatedStint: StintInfo) => {
        console.log('[RaceStrategy Logic] Stint updated:', updatedStint);
        try {
            // Fetch the updated strategy data which includes all recalculated stints
            await fetchStrategyData();
            showToast('Stint updated successfully!', 'success');
        } catch (error) {
            console.error('[RaceStrategy Logic] Error updating stint:', error);
            showToast('Failed to update stint. Please try again.', 'danger');
        }
    }, [fetchStrategyData, showToast]);

    const getCurrentStintNumber = useCallback(() => {
        if (!stintStrategy || !stintStrategy.stints.length || !strategyData?.startTime) return 0;
        const currentStint = stintStrategy.stints.find(stint => currentRaceTime >= stint.startTime && currentRaceTime <= stint.endTime);
        if (currentStint) return currentStint.stintNumber;
        const completedStints = stintStrategy.stints.filter(stint => stint.isUserModified);
        if (completedStints.length > 0) return Math.min(completedStints.length + 1, stintStrategy.stints.length);
        return 1;
    }, [stintStrategy, strategyData, currentRaceTime]);

    const getPitStopsDone = useCallback(() => {
        if (!stintStrategy || !stintStrategy.stints.length) return 0;
        return stintStrategy.stints.filter(stint => stint.isUserModified && stint.stintNumber < stintStrategy.stints.length).length;
    }, [stintStrategy]);

    const getExtraStintsUsed = useCallback(() => {
        if (!stintStrategy || !stintStrategy.stints.length) return 0;
        return stintStrategy.stints.filter(stint => stint.isExtraStint).length;
    }, [stintStrategy]);

    const getTotalExtraStints = useCallback(() => {
        if (!stintStrategy || !strategyData) return 0;
        const totalRaceTime = strategyData.raceDurationValue || 0;
        const avgStintTime = stintStrategy.idealStintTimeSeconds || 0;
        const mandatoryPits = strategyData.mandatoryPitStops || 0;
        if (avgStintTime <= 0) return 0;
        const maxPossibleStints = Math.floor(totalRaceTime / avgStintTime) + 1;
        return Math.max(0, maxPossibleStints - (mandatoryPits + 1));
    }, [stintStrategy, strategyData]);

    return {
        strategyData, isLoading, error, toastMessage, toastColor, showEditStrategyModal,
        currentRaceTime, showStrategyModal, showManagementModal, showDeleteAlert,stintStrategy, fetchStrategyData, 
        handleEditStrategy, handleDeleteStrategy, confirmDeleteStrategy,
        handleStrategyUpdate, handleCreateStrategy, handleRefresh, handleSelectStrategy,
         handleCalculateStints, handleStintUpdated,
        setShowEditStrategyModal, setShowStrategyModal, setShowManagementModal,
        setShowDeleteAlert, setToastMessage,
        getCurrentStintNumber, getPitStopsDone, getExtraStintsUsed, getTotalExtraStints,
    };
};
