import React from 'react';
import { IonInput, IonLabel, IonItem, IonNote } from '@ionic/react';

interface TimeInputProps {
  label: string;
  value: number;
  onChange: (value: number) => void;
  placeholder?: string;
  error?: string;
  format: 'mm:ss' | 'hh:mm';
  disabled?: boolean;
}

const TimeInput: React.FC<TimeInputProps> = ({
  label,
  value,
  onChange,
  placeholder,
  error,
  format,
  disabled = false
}) => {
  // Format seconds to mm:ss or hh:mm
  const formatTime = (seconds: number): string => {
    if (format === 'mm:ss') {
      const minutes = Math.floor(seconds / 60);
      const secs = seconds % 60;
      return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    } else {
      const hours = Math.floor(seconds / 3600);
      const minutes = Math.floor((seconds % 3600) / 60);
      return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
    }
  };

  // Parse mm:ss or hh:mm to seconds
  const parseTime = (timeString: string): number | null => {
    if (format === 'mm:ss') {
      const [minutesStr, secondsStr] = timeString.split(':');
      const minutes = parseInt(minutesStr, 10);
      const seconds = parseInt(secondsStr, 10);
      
      if (isNaN(minutes) || isNaN(seconds)) return null;
      return minutes * 60 + seconds;
    } else {
      const [hoursStr, minutesStr] = timeString.split(':');
      const hours = parseInt(hoursStr, 10);
      const minutes = parseInt(minutesStr, 10);
      
      if (isNaN(hours) || isNaN(minutes)) return null;
      return hours * 3600 + minutes * 60;
    }
  };

  const handleChange = (e: CustomEvent) => {
    const timeString = e.detail.value as string;
    const seconds = parseTime(timeString);
    if (seconds !== null) {
      onChange(seconds);
    }
  };

  return (
    <IonItem>
      <IonLabel position="stacked">{label}</IonLabel>
      <IonInput
        type="text"
        value={formatTime(value)}
        onIonChange={handleChange}
        placeholder={placeholder || (format === 'mm:ss' ? '05:00' : '01:00')}
        disabled={disabled}
      />
      {error && <IonNote color="danger">{error}</IonNote>}
    </IonItem>
  );
};

export default TimeInput;