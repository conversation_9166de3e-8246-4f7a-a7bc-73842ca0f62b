// backend/src/routes/kartRoutes.ts
import express from "express";
import {
    createKart,
    getAllKarts,
    getKartById,
    updateKartInfo,
    deleteKart,
    assignKartToTeam,
    unassignKartFromTeam,
    // *** NEW: Import the new controller ***
    getKartLastTeam,
    moveKartToMaintenance, // <-- Import new controller
} from "../controllers/kartController"; // Make sure to import getKartLastTeam

const router = express.Router();

// === Core Kart CRUD ===
router.post("/", createKart);
router.get("/", getAllKarts);
router.get("/:id", getKartById);
router.put("/:id", updateKartInfo);
router.delete("/:id", deleteKart);


// === Kart Assignment Management ===

router.put("/:kartId/assign/:teamId", assignKartToTeam);
router.delete("/:kartId/assign", unassignKartFromTeam);

// *** NEW: Route to get the last associated team ***
router.get("/:kartId/last-team", getKartLastTeam);

// *** NEW: Route to move a kart to maintenance (handles unassignment) ***
router.post("/:kartId/set-maintenance", moveKartToMaintenance);


export default router;