#!/usr/bin/env node

/**
 * Simple test script to analyze grid formats in both log files
 * This script doesn't require database connection - just analyzes the grid structures
 */

const fs = require('fs');
const path = require('path');

// Simple grid parser (JavaScript version)
function parseGridData(grid) {
  const id = Date.now();
  const drivers = {};
  const header = { types: {}, labels: {} };

  if (!grid) {
    console.warn('No grid data provided');
    return { drivers, header_labels: header.labels, header_types: header.types, id };
  }

  // Regex to capture each row with a `data-id` attribute
  const rowsRegex = /<tr[^>]*data-id="(r\d+)"[^>]*>(.*?)<\/tr>/gs;
  let rowMatch;

  while ((rowMatch = rowsRegex.exec(grid)) !== null) {
    const rowId = rowMatch[1]; // Extract the row ID (e.g., "r0", "r3")
    let driver = null; // Initialize driver only if needed

    // Regex to match all <td> or nested <div>/<p> with data-id attribute inside the row
    const cellRegex = /<td[^>]*data-id="(r?\d*c\d+|c\d+)"[^>]*>(.*?)<\/td>|<(?:div|p)[^>]*data-id="(r?\d*c\d+|c\d+)"[^>]*>(.*?)<\/(?:div|p)>/gs;
    let cellMatch;

    // Process each cell in the row
    while ((cellMatch = cellRegex.exec(rowMatch[2])) !== null) {
      const cellId = cellMatch[1] || cellMatch[3]; // Either <td> or nested <div>/<p> ID
      const cleanCellId = cellId.replace(/^r?\d*/, ""); // Remove row number part of the ID
      const cellContent = (cellMatch[2] || cellMatch[4] || "")
        .replace(/<[^>]+>/g, "")
        .trim(); // Clean cell content by stripping HTML tags

      if (rowId === "r0") {
        // For the header row (r0), capture the labels and types
        const headerTypeMatch = /data-type="([^"]*)"/.exec(cellMatch[0]);
        let headerType = headerTypeMatch ? headerTypeMatch[1] : "";

        // If the data-type is empty, assign a custom headerType based on cell content
        if (!headerType) {
          headerType = cellContent.toLowerCase().replace(/\s+/g, "");
        }

        if (headerType) {
          header.types[cleanCellId] = headerType;
        }
        header.labels[cleanCellId] = cellContent;
      } else {
        // Initialize the driver if it hasn't been initialized already
        if (!driver) {
          driver = {
            gridId: id,
            grp: { type: "", value: "" },
            sta: { type: "", value: "" },
            rk: { type: "", value: "" },
            no: { type: "", value: "" },
            dr: { type: "", value: "" },
            s1: { type: "", value: "" },
            s2: { type: "", value: "" },
            s3: { type: "", value: "" },
            sp1: { type: "", value: "" },
            llp: { type: "", value: "" },
            blp: { type: "", value: "" },
            gap: { type: "", value: "" },
            tlp: { type: "", value: "" },
            nat: { type: "", value: "" },
            otr: { type: "", value: "" },
            pit: { type: "", value: "" },
            int: { type: "", value: "" },
            Laps: [],
            Pits: [],
            pastKartIds: [],
            currentKartId: 0,
          };
        }

        // Get the header type for this column
        const headerType = header.types[cleanCellId];

        if (headerType && driver.hasOwnProperty(headerType)) {
          driver[headerType] = { type: headerType, value: cellContent };
        } else if (headerType) {
          // Create dynamic property for unknown header types
          driver[headerType] = { type: headerType, value: cellContent };
        } else {
          // If headerType is missing, create a new type name based on cell content
          const dynamicType = cellContent.toLowerCase().replace(/\s+/g, "");
          if (dynamicType) {
            driver[dynamicType] = { type: dynamicType, value: cellContent };
          }
        }
      }
    }

    // Store the driver data if it was created
    if (driver) {
      drivers[rowId] = driver;
    }
  }

  return { drivers, header_labels: header.labels, header_types: header.types, id };
}

// Extract messages from log file
function extractMessagesFromLog(logContent, raceName) {
  console.log(`\n🔍 Extracting messages from ${raceName}...`);
  
  const lines = logContent.split('\n').filter(line => line.trim().length > 0);
  const gridMessages = [];
  const updateMessages = [];
  const initMessages = [];
  
  for (const line of lines) {
    if (line.includes('grid||')) {
      gridMessages.push(line);
    } else if (line.includes('init||')) {
      initMessages.push(line);
    } else if (line.includes('|') && (line.includes('r') || line.includes('c'))) {
      updateMessages.push(line);
    }
  }
  
  console.log(`   📊 Found:`);
  console.log(`      Grid messages: ${gridMessages.length}`);
  console.log(`      Init messages: ${initMessages.length}`);
  console.log(`      Update messages: ${updateMessages.length}`);
  console.log(`      Total lines: ${lines.length}`);
  
  return { gridMessages, updateMessages, initMessages };
}

// Analyze grid structure
function analyzeGridStructure(gridData, raceName) {
  console.log(`\n📈 Grid Analysis for ${raceName}:`);
  console.log(`   Drivers found: ${Object.keys(gridData.drivers).length}`);
  console.log(`   Header columns: ${Object.keys(gridData.header_types).length}`);
  
  // Show header mapping
  console.log(`\n   📋 Header mapping:`);
  for (const [column, type] of Object.entries(gridData.header_types)) {
    const label = gridData.header_labels[column] || 'N/A';
    console.log(`      ${column}: ${type} ("${label}")`);
  }
  
  // Show first few drivers
  console.log(`\n   👥 Sample drivers:`);
  const driverIds = Object.keys(gridData.drivers).slice(0, 3);
  for (const driverId of driverIds) {
    const driver = gridData.drivers[driverId];
    const name = driver.dr?.value || 'N/A';
    const kartNumber = driver.no?.value || 'N/A';
    const nationality = driver.nat?.value || 'N/A';
    const rank = driver.rk?.value || 'N/A';
    console.log(`      ${driverId}: "${name}" (Kart: ${kartNumber}, Rank: ${rank}, Nation: ${nationality})`);
  }
  
  return gridData;
}

// Test message flow simulation
function testMessageFlow(messages, raceName) {
  console.log(`\n🔄 Testing message flow for ${raceName}:`);
  
  const { gridMessages, updateMessages, initMessages } = messages;
  
  // Simulate parser logic
  console.log(`\n   🧪 Simulation:`);
  
  // Step 1: Process init/grid messages first
  if (gridMessages.length > 0 || initMessages.length > 0) {
    console.log(`   ✅ Step 1: Found ${gridMessages.length + initMessages.length} init/grid messages`);
    console.log(`      → Would create session and database entities`);
  } else {
    console.log(`   ❌ Step 1: No init/grid messages found`);
    console.log(`      → Cannot create session, updates would be skipped`);
    return;
  }
  
  // Step 2: Process update messages
  if (updateMessages.length > 0) {
    console.log(`   ✅ Step 2: Found ${updateMessages.length} update messages`);
    console.log(`      → Would update existing entities (lap times, status, etc.)`);
    
    // Show sample update messages
    const sampleUpdates = updateMessages.slice(0, 3);
    console.log(`      Sample updates:`);
    for (const update of sampleUpdates) {
      const shortUpdate = update.length > 50 ? update.substring(0, 50) + '...' : update;
      console.log(`        ${shortUpdate}`);
    }
  } else {
    console.log(`   ⚠️ Step 2: No update messages found`);
  }
}

// Compare grid formats
function compareGridFormats(serresData, vittoriaData) {
  console.log(`\n🔄 Comparing Grid Formats:`);
  console.log('='.repeat(50));
  
  // Compare header types
  const serresHeaders = Object.keys(serresData.header_types);
  const vittoriaHeaders = Object.keys(vittoriaData.header_types);
  
  console.log(`\n   📊 Header columns:`);
  console.log(`      24h Serres: ${serresHeaders.length} columns`);
  console.log(`      Master Vittoria: ${vittoriaHeaders.length} columns`);
  
  // Find common and different headers
  const commonHeaders = serresHeaders.filter(h => vittoriaHeaders.includes(h));
  const serresOnly = serresHeaders.filter(h => !vittoriaHeaders.includes(h));
  const vittoriaOnly = vittoriaHeaders.filter(h => !serresHeaders.includes(h));
  
  console.log(`\n   🤝 Common headers (${commonHeaders.length}):`);
  commonHeaders.forEach(h => {
    const serresType = serresData.header_types[h];
    const vittoriaType = vittoriaData.header_types[h];
    const match = serresType === vittoriaType ? '✅' : '❌';
    console.log(`      ${h}: ${serresType} vs ${vittoriaType} ${match}`);
  });
  
  if (serresOnly.length > 0) {
    console.log(`\n   🔵 24h Serres only (${serresOnly.length}):`);
    serresOnly.forEach(h => {
      console.log(`      ${h}: ${serresData.header_types[h]} ("${serresData.header_labels[h]}")`);
    });
  }
  
  if (vittoriaOnly.length > 0) {
    console.log(`\n   🟡 Master Vittoria only (${vittoriaOnly.length}):`);
    vittoriaOnly.forEach(h => {
      console.log(`      ${h}: ${vittoriaData.header_types[h]} ("${vittoriaData.header_labels[h]}")`);
    });
  }
  
  // Check critical fields
  console.log(`\n   🎯 Critical field compatibility:`);
  const criticalFields = ['no', 'dr', 'nat', 'llp', 'blp', 'rk', 'sta'];
  
  criticalFields.forEach(field => {
    const serresHas = Object.values(serresData.header_types).includes(field);
    const vittoriaHas = Object.values(vittoriaData.header_types).includes(field);
    const status = serresHas && vittoriaHas ? '✅' : serresHas ? '🔵' : vittoriaHas ? '🟡' : '❌';
    console.log(`      ${field}: ${status} (Serres: ${serresHas}, Vittoria: ${vittoriaHas})`);
  });
}

// Main execution
function main() {
  console.log('🚀 Grid Format Analysis Script');
  console.log('===============================');
  
  const apexParserDir = path.join(__dirname, '..', 'apex parser files');
  const serresFile = path.join(apexParserDir, '24h serres.txt');
  const vittoriaFile = path.join(apexParserDir, 'master vitoria.txt');
  
  // Check if files exist
  if (!fs.existsSync(serresFile)) {
    console.error(`❌ File not found: ${serresFile}`);
    return;
  }
  
  if (!fs.existsSync(vittoriaFile)) {
    console.error(`❌ File not found: ${vittoriaFile}`);
    return;
  }
  
  try {
    // Read log files
    console.log('📖 Reading log files...');
    const serresContent = fs.readFileSync(serresFile, 'utf8');
    const vittoriaContent = fs.readFileSync(vittoriaFile, 'utf8');
    
    console.log(`   24h Serres: ${serresContent.length} characters`);
    console.log(`   Master Vittoria: ${vittoriaContent.length} characters`);
    
    // Extract messages from both files
    const serresMessages = extractMessagesFromLog(serresContent, '24h Serres');
    const vittoriaMessages = extractMessagesFromLog(vittoriaContent, 'Master Vittoria');
    
    // Parse grid data from both files
    let serresGridData = null;
    let vittoriaGridData = null;

    if (serresMessages.gridMessages.length > 0) {
      const gridMatch = serresMessages.gridMessages[0].match(/grid\|\|([^]*?)(?=\n|$)/);
      if (gridMatch) {
        serresGridData = parseGridData(gridMatch[1]);
        analyzeGridStructure(serresGridData, '24h Serres');
      }
    }

    if (vittoriaMessages.gridMessages.length > 0) {
      // Use the first complete grid message (some might be partial)
      let bestGrid = null;
      let maxDrivers = 0;

      for (const gridMessage of vittoriaMessages.gridMessages) {
        const gridMatch = gridMessage.match(/grid\|\|([^]*?)(?=\n|$)/);
        if (gridMatch) {
          const testGrid = parseGridData(gridMatch[1]);
          const driverCount = Object.keys(testGrid.drivers).length;
          if (driverCount > maxDrivers) {
            maxDrivers = driverCount;
            bestGrid = testGrid;
          }
        }
      }

      if (bestGrid) {
        vittoriaGridData = bestGrid;
        analyzeGridStructure(vittoriaGridData, 'Master Vittoria');
        console.log(`   📊 Used best grid with ${maxDrivers} drivers out of ${vittoriaMessages.gridMessages.length} grid messages`);
      }
    }
    
    // Test message flows
    testMessageFlow(serresMessages, '24h Serres');
    testMessageFlow(vittoriaMessages, 'Master Vittoria');
    
    // Compare grid formats
    if (serresGridData && vittoriaGridData) {
      compareGridFormats(serresGridData, vittoriaGridData);
    }
    
    console.log('\n✅ Grid format analysis completed!');
    console.log('\n📝 Key Findings:');
    console.log('   - Both formats use data-type attributes for field mapping');
    console.log('   - Parser should work with both formats using the same logic');
    console.log('   - Init/grid messages are essential for session creation');
    console.log('   - Update messages should be skipped without prior session');
    
  } catch (error) {
    console.error('❌ Error during analysis:', error);
  }
}

// Run the script
if (require.main === module) {
  main();
}

module.exports = { parseGridData, extractMessagesFromLog, analyzeGridStructure, compareGridFormats };
