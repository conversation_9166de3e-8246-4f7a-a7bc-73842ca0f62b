/* src/theme/list-styles.css */
/* General styles for IonList and IonItem layouts */

/* --- <PERSON>y List Header --- */
.list-header-sticky {
    /* Apply this class to an IonItem or a div acting as a header */
    font-weight: bold;
    padding: 12px 8px; /* Default padding, can be overridden */
    position: sticky;
    top: 0; /* Adjust if needed based on app header height */
    z-index: 10; /* Ensure it stays above scrolling content */
    background-color: var(--ion-color-light-shade); /* Default background */
    border-bottom: 1px solid var(--ion-border-color); /* Default border */
  }
  
  .list-header-content {
    /* Use inside the header element for alignment */
    display: flex;
    align-items: center;
    justify-content: flex-start;
    gap: 5px; /* Default gap */
  }
  
  .list-header-sortable {
    /* Add to header content elements that should be clickable for sorting */
    cursor: pointer;
  }
  .list-header-sortable:hover {
    /* Optional: Add hover effect */
    opacity: 0.8;
  }
  
  
  /* --- List Item Base Styles --- */
  .list-item-base {
    /* Apply to IonItem for common base styling */
     --padding-start: 0; 
     --padding-end: 0; 
     --inner-padding-end: 0; 
  }
  
  .list-item-no-padding {
     /* Apply specifically when no padding is desired */
    --padding-start: 0;
    --padding-end: 0;
    --inner-padding-end: 0;
  }
  
  /* --- List Row Content Alignment --- */
  .list-row-content {
    /* Apply to an IonRow or a div inside IonItem/IonLabel for alignment */
    display: flex; /* Use flex for alignment within the row */
    align-items: center;
    width: 100%;
    gap: 8px; /* Default gap between elements in the row */
  }
  
  
  /* --- Common Text/Data Styles --- */
  .list-item-primary-text {
    /* Example: For main text like team name or item title */
    font-weight: 500;
  }
  
  .list-item-secondary-text {
     /* Example: For less important text */
     color: var(--ion-color-step-600, #666);
     font-size: 0.9em;
  }
  
  .list-item-highlight {
    /* Example: For emphasized text like numbers or IDs */
    font-weight: bold;
    font-size: 1.1em;
  }
  
  .list-item-data-group {
    /* Use for grouping related data points within a row */
    display: flex;
    align-items: center;
    gap: 5px;
  }
  
  
  /* --- Utility Classes --- */
  .truncate-text {
    /* Keep this useful utility */
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 100%; /* Important within flex/grid */
  }
  
  .no-data-message {
    /* Generic class for empty list messages */
    padding: 20px;
    text-align: center;
    font-style: italic;
    color: var(--ion-color-step-600, #666);
  }
  /* Optional: Add icon styling for no-data */
  .no-data-message ion-icon {
    font-size: 2em;
    margin-bottom: 8px;
    display: block; /* Center icon */
    margin-left: auto;
    margin-right: auto;
  }
  
  
  /* --- Action Buttons --- */
  .list-item-actions {
    /* Container for action buttons, typically at the end of an item */
    display: flex;
    justify-content: flex-end;
    align-items: center;
    gap: 5px;
  }
  
  
  /* --- Responsive Styles --- */
  .hide-sm {
    /* Keep this utility */
    @media (max-width: 768px) {
      display: none !important; /* Use !important cautiously if needed */
    }
  }
  
  .show-sm-only {
    /* Utility to show only on small screens */
    display: none;
    @media (max-width: 768px) {
      display: block; /* Or flex, inline-block etc. */
    }
  }
  
  .list-details-mobile {
    /* Generic class for mobile-specific detail views */
    display: none; /* Hidden by default */
  }
  
  @media (max-width: 768px) {
    .list-details-mobile {
      display: flex; /* Or block, depending on layout */
      flex-wrap: wrap;
      width: 100%; /* Take full width */
      padding-top: 8px;
      margin-top: 8px;
      border-top: 1px solid var(--ion-border-color);
      gap: 8px; /* Spacing between mobile detail items */
    }
  
    /* Example: Style individual detail items for mobile */
    .list-details-mobile .detail-item {
       display: flex;
       align-items: center;
       gap: 5px;
       font-size: 0.9em;
    }
  }
  
