import React from 'react';
import {
  IonHeader,
  IonToolbar,
  IonTitle,
  IonContent,
  IonList,
  IonItem,
  IonLabel,
  IonIcon,
  IonButtons,
  IonButton,
  IonCard,
  IonCardContent,
  IonCardHeader,
  IonCardTitle,
  IonBadge,
  IonText,
  IonRippleEffect
} from '@ionic/react';
import { 
  pencil, 
  play,
  close,
  informationCircleOutline,
  alertCircleOutline
} from 'ionicons/icons';
import { StrategyData } from '../../../types';
import './SettingsModal.css';

interface SettingsModalProps {
  onClose: () => void;
  onEditStrategy: () => void;
  onStartRace: () => void;
  strategyData: StrategyData | null;
  isRaceActive: boolean;
}

const SettingsModal: React.FC<SettingsModalProps> = ({
  onClose,
  onEditStrategy,
  onStartRace,
  strategyData,
  isRaceActive
}) => {
  return (
    <>
      <IonHeader>
        <IonToolbar color="primary">
          <IonTitle>Race Settings</IonTitle>
          <IonButtons slot="end">
            <IonButton onClick={onClose}>
              <IonIcon icon={close} />
            </IonButton>
          </IonButtons>
        </IonToolbar>
      </IonHeader>
      
      <IonContent className="settings-modal-content">
        <div className="ion-padding">
          {/* Strategy Info Card */}
          {strategyData ? (
            <IonCard className="strategy-info-card">
              <IonCardHeader>
                <IonCardTitle>{strategyData.raceName}</IonCardTitle>
                {isRaceActive && (
                  <IonBadge color="success" className="race-status-badge">
                    Race in Progress
                  </IonBadge>
                )}
              </IonCardHeader>
              <IonCardContent>
                <div className="strategy-info-item">
                  <IonText color="medium">Race Duration:</IonText>
                  <IonText>
                    {strategyData.raceDurationType === 'time' 
                      ? `${Math.floor(strategyData.raceDurationValue / 3600)}h ${Math.floor((strategyData.raceDurationValue % 3600) / 60)}m`
                      : `${strategyData.raceDurationValue} Laps`}
                  </IonText>
                </div>
                <div className="strategy-info-item">
                  <IonText color="medium">Mandatory Pit Stops:</IonText>
                  <IonText>{strategyData.mandatoryPitStops}</IonText>
                </div>
              </IonCardContent>
            </IonCard>
          ) : (
            <div className="no-strategy-message">
              <IonIcon icon={informationCircleOutline} />
              <IonText>No race strategy selected</IonText>
            </div>
          )}

          {/* Actions List */}
          <IonList className="settings-action-list" lines="full">
            <IonItem button onClick={onEditStrategy} detail={true} className="settings-action-item ion-activatable ripple-parent">
              <IonIcon icon={pencil} slot="start" color="primary" />
              <IonLabel>
                <h2>Edit Race Strategy</h2>
                <p>Modify race parameters and settings</p>
              </IonLabel>
              <IonRippleEffect />
            </IonItem>
            
            <IonItem 
              button 
              onClick={onStartRace} 
              disabled={isRaceActive || !strategyData}
              detail={true}
              className={`settings-action-item ion-activatable ripple-parent ${isRaceActive ? 'disabled-item' : ''}`}
            >
              <IonIcon 
                icon={isRaceActive ? alertCircleOutline : play} 
                slot="start" 
                color={isRaceActive ? "medium" : "success"} 
              />
              <IonLabel>
                <h2>Start Race</h2>
                <p>{isRaceActive 
                  ? 'Race already in progress' 
                  : !strategyData 
                    ? 'Select a strategy first' 
                    : 'Begin race timing and tracking'}
                </p>
              </IonLabel>
              <IonRippleEffect />
            </IonItem>
          </IonList>

          {/* Help Text */}
          <div className="settings-help-text">
            <IonIcon icon={informationCircleOutline} />
            <p>
              {isRaceActive 
                ? "The race is currently in progress. You can still edit the strategy, but timing will continue." 
                : "Select an action above to manage your race strategy."}
            </p>
          </div>
        </div>
      </IonContent>
    </>
  );
};

export default SettingsModal;


