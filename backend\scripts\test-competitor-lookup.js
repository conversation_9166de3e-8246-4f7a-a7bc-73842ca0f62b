const mongoose = require('mongoose');

// Define schemas directly since we can't import TypeScript modules
const ApexSessionSchema = new mongoose.Schema({
  title1: { type: String, default: '' },
  title2: { type: String, default: '' },
  track: { type: String, default: '' },
  isActive: { type: Boolean, default: true },
  gridData: { type: mongoose.Schema.Types.Mixed },
  sessionData: { type: mongoose.Schema.Types.Mixed, default: {} }
}, {
  timestamps: true,
  collection: 'apex_sessions'
});

const ApexTeamSchema = new mongoose.Schema({
  sessionId: { type: mongoose.Schema.Types.ObjectId, ref: 'ApexSession', required: true },
  name: { type: String, required: true },
  currentKartId: { type: mongoose.Schema.Types.ObjectId, ref: 'ApexKart', default: null },
  pastKarts: [{ type: mongoose.Schema.Types.ObjectId, ref: 'ApexKart' }],
  pits: [{ type: mongoose.Schema.Types.ObjectId, ref: 'Pit' }],
  drivers: [{ type: String }],
  nationality: { type: String, default: 'Unknown' },
  totalLaps: { type: Number, default: 0 },
  bestLapTime: { type: Number },
  bestLapTimeFormatted: { type: String },
  lastLapTime: { type: Number },
  lastLapTimeFormatted: { type: String },
  position: { type: Number },
  status: { type: String, default: 'active' },
  isActive: { type: Boolean, default: true }
}, {
  timestamps: true,
  collection: 'apex_teams'
});

const ApexKartSchema = new mongoose.Schema({
  sessionId: { type: mongoose.Schema.Types.ObjectId, ref: 'ApexSession', required: true },
  kartNumber: { type: Number, required: true },
  speed: { type: Number, default: 3 },
  currentTeamId: { type: mongoose.Schema.Types.ObjectId, ref: 'ApexTeam', default: null },
  currentRowId: { type: mongoose.Schema.Types.ObjectId, ref: 'Row', default: null },
  status: {
    type: String,
    enum: ['on_track', 'in_pit_row', 'maintenance', 'available'],
    default: 'available',
    required: true,
  },
  lastLapTimeFormatted: { type: String },
  bestLapTimeFormatted: { type: String },
  totalLaps: { type: Number, default: 0 },
  isActive: { type: Boolean, default: true }
}, {
  timestamps: true,
  collection: 'apex_karts'
});

const ApexCompetitorSchema = new mongoose.Schema({
  sessionId: { type: mongoose.Schema.Types.ObjectId, ref: 'ApexSession', required: true },
  teamId: { type: mongoose.Schema.Types.ObjectId, ref: 'ApexTeam', required: true },
  kartId: { type: mongoose.Schema.Types.ObjectId, ref: 'ApexKart', required: true },
  websocketId: { type: String, required: true },
  name: { type: String, required: true },
  nationality: { type: String, default: 'Unknown' },
  drivers: [{ type: String }],
  isActive: { type: Boolean, default: true }
}, {
  timestamps: true,
  collection: 'apex_competitors'
});

const ApexLapSchema = new mongoose.Schema({
  sessionId: { type: mongoose.Schema.Types.ObjectId, ref: 'ApexSession', required: true },
  competitorId: { type: mongoose.Schema.Types.ObjectId, ref: 'ApexCompetitor', required: true },
  kartId: { type: mongoose.Schema.Types.ObjectId, ref: 'ApexKart', required: true },
  lapNumber: { type: Number, default: 0 },
  lapTime: { type: Number, required: true },
  lapTimeFormatted: { type: String, required: true },
  sector1: { type: Number },
  sector2: { type: Number },
  sector3: { type: Number },
  isBestLap: { type: Boolean, default: false },
  isPersonalBest: { type: Boolean, default: false },
  timestamp: { type: Date, default: Date.now }
}, {
  timestamps: true,
  collection: 'apex_laps'
});

// Create models
const ApexSession = mongoose.model('ApexSession', ApexSessionSchema);
const ApexTeam = mongoose.model('ApexTeam', ApexTeamSchema);
const ApexKart = mongoose.model('ApexKart', ApexKartSchema);
const ApexCompetitor = mongoose.model('ApexCompetitor', ApexCompetitorSchema);
const ApexLap = mongoose.model('ApexLap', ApexLapSchema);

async function testCompetitorLookup() {
  try {
    // Connect to MongoDB
    await mongoose.connect('mongodb://localhost:27017/raceplanner');
    console.log('✅ Connected to MongoDB');

    // Find the most recent session
    const session = await ApexSession.findOne().sort({ createdAt: -1 });
    if (!session) {
      console.log('❌ No sessions found');
      return;
    }

    console.log(`\n📋 Testing session: ${session.title1} - ${session.title2}`);
    console.log(`   Session ID: ${session._id}`);

    // Get all competitors for this session
    const competitors = await ApexCompetitor.find({ sessionId: session._id });
    console.log(`\n👥 Found ${competitors.length} competitors:`);
    
    competitors.forEach(competitor => {
      console.log(`   - ${competitor.name} (websocketId: ${competitor.websocketId}, _id: ${competitor._id})`);
    });

    // Test lookup by websocketId
    console.log('\n🔍 Testing competitor lookup by websocketId:');
    
    if (competitors.length > 0) {
      const testWebsocketId = competitors[0].websocketId;
      console.log(`   Looking for websocketId: ${testWebsocketId}`);
      
      const foundCompetitor = await ApexCompetitor.findOne({
        sessionId: session._id,
        websocketId: testWebsocketId
      });
      
      if (foundCompetitor) {
        console.log(`   ✅ Found: ${foundCompetitor.name} (${foundCompetitor._id})`);
      } else {
        console.log(`   ❌ Not found`);
      }
    }

    // Test with the problematic websocketId from the error
    console.log('\n🔍 Testing lookup for websocketId "17794":');
    const problematicCompetitor = await ApexCompetitor.findOne({
      sessionId: session._id,
      websocketId: '17794'
    });
    
    if (problematicCompetitor) {
      console.log(`   ✅ Found: ${problematicCompetitor.name} (${problematicCompetitor._id})`);
    } else {
      console.log(`   ❌ Competitor with websocketId "17794" not found in session ${session._id}`);
      
      // Check if it exists in any session
      const anySessionCompetitor = await ApexCompetitor.findOne({ websocketId: '17794' });
      if (anySessionCompetitor) {
        console.log(`   ℹ️ But found in session: ${anySessionCompetitor.sessionId}`);
      } else {
        console.log(`   ℹ️ Competitor "17794" doesn't exist in any session`);
      }
    }

    // Get teams and karts for this session
    const teams = await ApexTeam.find({ sessionId: session._id });
    const karts = await ApexKart.find({ sessionId: session._id });
    
    console.log(`\n📊 Session statistics:`);
    console.log(`   Teams: ${teams.length}`);
    console.log(`   Karts: ${karts.length}`);
    console.log(`   Competitors: ${competitors.length}`);

    // Check for any laps
    const laps = await ApexLap.find({ sessionId: session._id }).limit(5);
    console.log(`   Recent laps: ${laps.length}`);
    
    if (laps.length > 0) {
      console.log(`   Latest lap: ${laps[0].lapTimeFormatted} by competitor ${laps[0].competitorId}`);
    }

  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await mongoose.disconnect();
    console.log('\n✅ Disconnected from MongoDB');
  }
}

// Run the test
testCompetitorLookup();
