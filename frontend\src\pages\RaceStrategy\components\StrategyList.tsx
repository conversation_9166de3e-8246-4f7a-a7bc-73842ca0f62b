import React, { useState, useEffect } from 'react';
import { 
  IonList, 
  IonItem, 
  IonLabel, 
  IonButton, 
  IonIcon, 
  IonItemSliding, 
  IonItemOptions, 
  IonItemOption,
  IonBadge,
  IonSkeletonText,
  IonAlert
} from '@ionic/react';
import { 
  copyOutline, 
  createOutline, 
  trashOutline, 
  addOutline 
} from 'ionicons/icons';
import axios from 'axios';
import { StrategyData } from '../../../types';
import { formatSecondsToTimeString } from '../utils/timeUtils';

interface StrategyListProps {
  onSelectStrategy: (strategy: StrategyData) => void;
  onEditStrategy: (strategy: StrategyData) => void;
  onCreateStrategy: () => void;
  activeStrategyId: string | null;
  apiBaseUrl: string;
}

const StrategyList: React.FC<StrategyListProps> = ({ 
  onSelectStrategy, 
  onEditStrategy, 
  onCreateStrategy,
  apiBaseUrl
}) => {
  const [strategies, setStrategies] = useState<StrategyData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [deleteConfirmId, setDeleteConfirmId] = useState<string | null>(null);

  // Fetch all strategies
  const fetchStrategies = async () => {
    console.log('[StrategyList] Fetching all strategies...');
    setLoading(true);
    try {
      const token = localStorage.getItem('token'); // Assuming token is needed
      const response = await axios.get<StrategyData[]>(`${apiBaseUrl}/strategy/all`, {
        headers: token ? { Authorization: `Bearer ${token}` } : {},
      });
      setStrategies(response.data); // Assuming response.data is StrategyData[]
      setError(null);
    } catch (err) {
      console.error('Error fetching strategies:', err);
      setError(`Failed to load strategies: ${err instanceof Error ? err.message : String(err)}`);
    } finally {
      setLoading(false);
    }
  };

  // Load strategies on component mount
  useEffect(() => {
    fetchStrategies();
  }, []);

  // Activate a strategy
  const handleActivate = async (id: string) => {
    console.log(`[StrategyList] Activating strategy ID: ${id}`);
    try {
      const token = localStorage.getItem('token');
      const response = await axios.put<StrategyData>(`${apiBaseUrl}/strategy/${id}/activate`, {}, {
        headers: token ? { Authorization: `Bearer ${token}` } : {},
      });
      // Update local state
      setStrategies(prevStrategies => 
        prevStrategies.map(strategy => ({
          ...strategy,
          isActive: strategy._id === id // Ensure isActive is part of StrategyData
        }))
      );
      // Notify parent
      onSelectStrategy(response.data);
    } catch (err) {
      console.error(`Error activating strategy ID ${id}:`, err);
      setError('Failed to activate strategy');
    }
  };

  // Duplicate a strategy
  const handleDuplicate = async (id: string) => {
    try {
      console.log(`[StrategyList] Duplicating strategy ID: ${id}`);
      const token = localStorage.getItem('token');
      await axios.post<StrategyData>(`${apiBaseUrl}/strategy/${id}/duplicate`, {}, {
        headers: token ? { Authorization: `Bearer ${token}` } : {},
      });
      // Refresh the list
      fetchStrategies();
    } catch (err) {
      console.error(`Error duplicating strategy ID ${id}:`, err);
      console.error('Error duplicating strategy:', err);
      setError('Failed to duplicate strategy');
    }
  };

  // Delete a strategy
  const handleDelete = async (id: string) => {
    console.log(`[StrategyList] Deleting strategy ID: ${id}`);
    try {
      const token = localStorage.getItem('token');
      await axios.delete(`${apiBaseUrl}/strategy/${id}`, {
        headers: token ? { Authorization: `Bearer ${token}` } : {},
      });
      // Remove from local state
      setStrategies(prevStrategies => 
        prevStrategies.filter(strategy => strategy._id !== id)
      );
      setDeleteConfirmId(null); // Close the alert
      console.log(`[StrategyList] Strategy ID ${id} deleted successfully.`);
    } catch (err) {
      console.error('Error deleting strategy:', err);
      setError('Failed to delete strategy');
    }
  };

  // Format duration for display
  const formatDuration = (strategy: StrategyData): string => {
    if (strategy.raceDurationType === 'laps') {
      return `${strategy.raceDurationValue} Laps`;
    } else {
      return formatSecondsToTimeString(strategy.raceDurationValue, 'hh:mm:ss');
    }
  };

  if (loading) {
    return (
      <IonList>
        {[1, 2, 3].map(i => (
          <IonItem key={i}>
            <IonLabel>
              <IonSkeletonText animated style={{ width: '70%' }} />
              <IonSkeletonText animated style={{ width: '40%' }} />
            </IonLabel>
          </IonItem>
        ))}
      </IonList>
    );
  }

  if (error) {
    return <div className="ion-padding ion-text-center">{error}</div>;
  }

  return (
    <>
      <IonList>
        {strategies.length === 0 ? (
          <IonItem>
            <IonLabel className="ion-text-center">No strategies found</IonLabel>
          </IonItem>
        ) : (
          strategies.map(strategy => (
            <IonItemSliding key={strategy._id}>
              <IonItem 
                button 
                detail={false} 
                onClick={() => strategy.isActive ? null : handleActivate(strategy._id || '')}
              >
                <IonLabel>
                  <h2>{strategy.raceName}</h2>
                  <p>{formatDuration(strategy)}</p>
                </IonLabel>
                {strategy.isActive && (
                  <IonBadge color="success" slot="end">Active</IonBadge>
                )}
                {/* strategy._id should be string here for existing strategies */}
              </IonItem>
              
              <IonItemOptions side="end">
                <IonItemOption color="primary" onClick={() => onEditStrategy(strategy)}>
                  <IonIcon slot="icon-only" icon={createOutline} />
                </IonItemOption>
                <IonItemOption color="secondary" onClick={() => handleDuplicate(strategy._id || '')}>
                  <IonIcon slot="icon-only" icon={copyOutline} />
                </IonItemOption>
                {/* strategy._id should be string here for existing strategies */}
                {!strategy.isActive && (
                  <IonItemOption color="danger" onClick={() => setDeleteConfirmId(strategy._id || null)}>
                    <IonIcon slot="icon-only" icon={trashOutline} />
                  </IonItemOption>
                )}
              </IonItemOptions>
            </IonItemSliding>
          ))
        )}
      </IonList>
      
      <div className="ion-padding">
        <IonButton expand="block" onClick={onCreateStrategy}>
          <IonIcon slot="start" icon={addOutline} />
          Create New Strategy
        </IonButton>
      </div>
      
      <IonAlert
        isOpen={!!deleteConfirmId}
        header="Confirm Delete"
        message="Are you sure you want to delete this strategy? This action cannot be undone."
        buttons={[
          {
            text: 'Cancel',
            role: 'cancel',
            handler: () => setDeleteConfirmId(null)
          },
          {
            text: 'Delete',
            handler: () => {
              if (deleteConfirmId) {
                handleDelete(deleteConfirmId);
                return true;
              }
              return true;
            }
          }
        ]}
        onDidDismiss={() => setDeleteConfirmId(null)}
      />
    </>
  );
};

export default StrategyList;


