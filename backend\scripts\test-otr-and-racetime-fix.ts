#!/usr/bin/env ts-node

/**
 * Test script to verify otr pit duration and race time fixes
 */

import mongoose from 'mongoose';
import { ApexParserSimple } from '../src/services/apexParserSimple';
import { ApexSession, ApexTeam, ApexKart, ApexCompetitor, ApexPitStop } from '../src/models/ApexModels';

// Test configuration
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/raceplanner';

async function connectToDatabase() {
  try {
    await mongoose.connect(MONGODB_URI);
    console.log('✅ Connected to MongoDB');
  } catch (error) {
    console.error('❌ Failed to connect to MongoDB:', error);
    process.exit(1);
  }
}

async function cleanupTestData() {
  console.log('🧹 Cleaning up test data...');
  await ApexSession.deleteMany({ title1: { $regex: /OTR and Race Time Test/ } });
  await ApexTeam.deleteMany({ name: { $regex: /TEST/ } });
  await ApexKart.deleteMany({ kartNumber: { $in: [99, 88] } });
  await ApexCompetitor.deleteMany({ websocketId: { $in: ['17788', '17799'] } });
  await ApexPitStop.deleteMany({});
}

async function testOtrAndRaceTimeFix() {
  console.log('\n🔍 Testing OTR Pit Duration and Race Time Fix...\n');

  const parser = new ApexParserSimple({ enableLogging: true });

  // Test session creation with grid
  const gridMessage = `init|r|
title1||OTR and Race Time Test
title2||Fix Verification
track||Test Track
grid||<tbody><tr data-id="r0"><td data-id="c1" data-type="sta">N/A</td><td data-id="c2" data-type="rk">Rnk</td><td data-id="c3" data-type="no">Kart</td><td data-id="c5" data-type="dr">Team</td><td data-id="c14" data-type="otr">On track</td><td data-id="c15" data-type="pit">Pits</td></tr><tr data-id="r17788"><td data-id="r17788c1">N/A</td><td data-id="r17788c2">1</td><td data-id="r17788c3">99</td><td data-id="r17788c5">TEST TEAM 1</td><td data-id="r17788c14">00:00:00</td><td data-id="r17788c15">0</td></tr><tr data-id="r17799"><td data-id="r17799c1">N/A</td><td data-id="r17799c2">2</td><td data-id="r17799c3">88</td><td data-id="r17799c5">TEST TEAM 2</td><td data-id="r17799c14">00:00:00</td><td data-id="r17799c15">0</td></tr></tbody>`;

  console.log('📊 Creating session and competitors...');
  await parser.parseMessage(gridMessage);

  await new Promise(resolve => setTimeout(resolve, 1000));

  console.log('\n⏱️ Testing race time tracking with dyn1...\n');

  // Test dyn1 race time tracking
  console.log('--- Sending dyn1 message: 01:30:00 (race duration) ---');
  await parser.parseMessage('dyn1|text|01:30:00');

  await new Promise(resolve => setTimeout(resolve, 500));

  console.log('\n--- Sending dyn1 message: 01:29:45 (15 seconds into race) ---');
  await parser.parseMessage('dyn1|text|01:29:45');

  await new Promise(resolve => setTimeout(resolve, 500));

  console.log('\n🏁 Testing pit entry with numeric status...\n');

  // Test pit entry with numeric status (should create pit stop)
  console.log('--- Sending pit status: r17788c15|in|1 (1 second in pit) ---');
  await parser.parseMessage('r17788c15|in|1');

  await new Promise(resolve => setTimeout(resolve, 500));

  console.log('\n⏱️ Testing otr pit duration updates...\n');

  // Test otr pit duration updates (should update pitCurrentDuration)
  console.log('--- Sending otr update: r17788c14|otr|05. (5 seconds pit duration) ---');
  await parser.parseMessage('r17788c14|otr|05.');

  await new Promise(resolve => setTimeout(resolve, 500));

  console.log('--- Sending otr update: r17788c14|otr|10. (10 seconds pit duration) ---');
  await parser.parseMessage('r17788c14|otr|10.');

  await new Promise(resolve => setTimeout(resolve, 500));

  console.log('--- Sending otr update: r17788c14|otr|15. (15 seconds pit duration) ---');
  await parser.parseMessage('r17788c14|otr|15.');

  await new Promise(resolve => setTimeout(resolve, 500));

  console.log('\n--- Sending pit exit: r17788c15|in|0 (pit exit) ---');
  await parser.parseMessage('r17788c15|in|0');

  await new Promise(resolve => setTimeout(resolve, 1000));

  // Check database results
  console.log('\n📊 Checking database results...\n');
  
  const sessions = await ApexSession.find({ title1: { $regex: /OTR and Race Time Test/ } }).lean();
  console.log(`Sessions created: ${sessions.length}`);
  
  if (sessions.length > 0) {
    const session = sessions[0];
    console.log(`✅ Session: ${session._id} - ${session.title1}`);

    const competitors = await ApexCompetitor.find({ sessionId: session._id }).lean();
    console.log(`Competitors: ${competitors.length}`);

    const teams = await ApexTeam.find({ sessionId: session._id }).lean();
    console.log(`Teams: ${teams.length}`);

    if (teams.length > 0) {
      console.log('✅ Team status details:');
      teams.forEach((team, index) => {
        console.log(`   ${index + 1}. ${team.name}: status = ${team.status}`);
      });
    }

    const pitStops = await ApexPitStop.find({ sessionId: session._id }).lean();
    console.log(`🏁 Pit stops created: ${pitStops.length}`);

    if (pitStops.length > 0) {
      console.log('✅ Pit stop details:');
      pitStops.forEach((pit, index) => {
        console.log(`   ${index + 1}. Competitor: ${pit.competitorId}`);
        console.log(`      Pit In Time: ${pit.pitInTime}`);
        console.log(`      Pit Out Time: ${pit.pitOutTime || 'ACTIVE'}`);
        console.log(`      Pit Duration: ${pit.pitDuration || 'N/A'}s`);
        console.log(`      Current Duration: ${pit.pitCurrentDuration || 0}s`);
        console.log(`      Total Duration: ${pit.pitTotalDuration || 'N/A'}s`);
        console.log(`      Race Time In: ${pit.raceTimeAtPitIn || 'N/A'}s`);
        console.log(`      Race Time Out: ${pit.raceTimeAtPitOut || 'N/A'}s`);
        console.log('');
      });

      // Check specific issues
      const latestPitStop = pitStops[pitStops.length - 1];

      console.log('🔍 Checking fixes:');

      // Check if pitCurrentDuration was updated from otr
      if (latestPitStop && latestPitStop.pitCurrentDuration && latestPitStop.pitCurrentDuration > 0) {
        console.log(`✅ pitCurrentDuration updated from otr: ${latestPitStop.pitCurrentDuration}s`);
      } else {
        console.log(`❌ pitCurrentDuration NOT updated from otr: ${latestPitStop?.pitCurrentDuration || 'undefined'}`);
      }

      // Check if raceTimeAtPitIn is not 0
      if (latestPitStop && latestPitStop.raceTimeAtPitIn && latestPitStop.raceTimeAtPitIn > 0) {
        console.log(`✅ raceTimeAtPitIn calculated correctly: ${latestPitStop.raceTimeAtPitIn}s`);
      } else {
        console.log(`❌ raceTimeAtPitIn is 0 or null: ${latestPitStop?.raceTimeAtPitIn || 'undefined'}`);
      }

    } else {
      console.log('❌ No pit stops were created!');
    }

    return {
      sessionId: session._id,
      competitorsCreated: competitors.length,
      pitStopsCreated: pitStops.length,
      pitCurrentDurationWorking: pitStops.length > 0 && pitStops[0] && (pitStops[0].pitCurrentDuration || 0) > 0,
      raceTimeWorking: pitStops.length > 0 && pitStops[0] && (pitStops[0].raceTimeAtPitIn || 0) > 0
    };
  } else {
    console.log('❌ No sessions were created');
    return {
      sessionId: null,
      competitorsCreated: 0,
      pitStopsCreated: 0,
      pitCurrentDurationWorking: false,
      raceTimeWorking: false
    };
  }
}

async function main() {
  try {
    await connectToDatabase();
    await cleanupTestData();
    
    const results = await testOtrAndRaceTimeFix();
    
    console.log('\n📊 Test Results Summary:');
    console.log(`   Session ID: ${results.sessionId}`);
    console.log(`   Competitors Created: ${results.competitorsCreated}`);
    console.log(`   Pit Stops Created: ${results.pitStopsCreated}`);
    console.log(`   Pit Current Duration Working: ${results.pitCurrentDurationWorking ? '✅' : '❌'}`);
    console.log(`   Race Time Working: ${results.raceTimeWorking ? '✅' : '❌'}`);
    
    if (results.pitCurrentDurationWorking && results.raceTimeWorking) {
      console.log('\n✅ OTR and Race Time fixes working correctly!');
    } else {
      console.log('\n❌ Some fixes are not working correctly!');
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  } finally {
    await cleanupTestData();
    await mongoose.disconnect();
    console.log('🔌 Disconnected from MongoDB');
  }
}

// Run the test
if (require.main === module) {
  main().catch(console.error);
}
