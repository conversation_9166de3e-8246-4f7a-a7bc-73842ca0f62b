// d:\Downloads\Nuova cartella\my-app\frontend\src\pages\KartStatusPage.tsx
import React from 'react';
import { IonPage } from '@ionic/react';

// Adjust the import path based on where you created KartStatusList.tsx
import KartStatusList from '../components/Karts/KartStatusList';

const KartsStatusPage: React.FC = () => {
  return (
    // IonPage is often used as the root for routed pages in Ionic
    <IonPage>
      <KartStatusList />
    </IonPage>
  );
};

export default KartsStatusPage;
