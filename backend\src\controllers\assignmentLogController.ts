// backend/src/controllers/assignmentLogController.ts
import { Request, Response } from "express";
import mongoose, { FilterQuery } from "mongoose";
import { AssignmentLog, IAssignmentLog } from "../models/AssignmentLog";
import { isValidObjectId, sendErrorResponse } from "../utils/controllerUtils";
import { ITeam, Team } from "../models/Team";

/**
 * @description Get assignment history, optionally filtered.
 * @route GET /api/assignment-history
 * @queryParam kartId - Filter by Kart ID
 * @queryParam teamId - Filter by Team ID (involved as current or previous)
 * @queryParam rowId - Filter by Row ID (involved as current or previous)
 * @queryParam limit - Limit number of results (default 50)
 * @queryParam sort - Sort order (default 'desc' by timestamp)
 */
export const getAssignmentHistory = async (req: Request, res: Response) => {
  try {
    const { kartId, teamId, rowId } = req.query;
    const limit = parseInt((req.query.limit as string) || "50", 10);
    const sortOrder = ((req.query.sort as string) || "desc") === "asc" ? 1 : -1;

    const filter: FilterQuery<IAssignmentLog> = {};

    if (kartId && typeof kartId === "string" && isValidObjectId(kartId)) {
      filter.kartId = new mongoose.Types.ObjectId(kartId);
    }
    // Filter if team was involved either as current or previous
    if (teamId && typeof teamId === "string" && isValidObjectId(teamId)) {
      filter.$or = [
        { teamId: new mongoose.Types.ObjectId(teamId) },
        { previousTeamId: new mongoose.Types.ObjectId(teamId) },
      ];
    }
    // Filter if row was involved either as current or previous
    if (rowId && typeof rowId === "string" && isValidObjectId(rowId)) {
      // If filter.$or already exists, add to it using $and
      if (filter.$or) {
        filter.$and = [
          { $or: filter.$or }, // Keep existing team filter
          {
            $or: [
              { rowId: new mongoose.Types.ObjectId(rowId) },
              { previousRowId: new mongoose.Types.ObjectId(rowId) },
            ],
          },
        ];
        delete filter.$or; // Remove original $or
      } else {
        filter.$or = [
          { rowId: new mongoose.Types.ObjectId(rowId) },
          { previousRowId: new mongoose.Types.ObjectId(rowId) },
        ];
      }
    }

    const historyLogs = await AssignmentLog.find(filter)
      .sort({ timestamp: sortOrder }) // Sort by event time
      .limit(limit)
      .populate("kartId", "number") // Populate kart number
      .populate("teamId", "name number") // Populate current team details
      .populate("rowId", "rowNumber color") // Populate current row details
      .populate("previousTeamId", "name number") // Populate previous team details
      .populate("previousRowId", "rowNumber color") // Populate previous row details
      .lean()
      .exec();

    res.status(200).json(historyLogs);
  } catch (error: unknown) {
    sendErrorResponse(res, "Error fetching assignment history", 500, error);
  }
};

// Function to find the last team associated with a kart
export async function getLastTeamForKart(
  kartId: string | mongoose.Types.ObjectId
): Promise<Pick<ITeam, "_id" | "name" | "number"> | null> {
  try {
    if (!kartId || !isValidObjectId(kartId.toString())) {
      console.warn(`[getLastTeamForKart] Invalid kartId provided: ${kartId}`);
      return null;
    }
    const objectKartId = new mongoose.Types.ObjectId(kartId);

    // Find the most recent log entry for this kart where a team was involved
    const lastLog = await AssignmentLog.findOne({
      kartId: objectKartId,
      $or: [
        { teamId: { $ne: null } }, // Was assigned to a team, or unassigned from a team
        { previousTeamId: { $ne: null } }, // Was assigned to a row *from* a team
      ],
    })
      .sort({ timestamp: -1 }) // Get the latest entry first
      .select("eventType teamId previousTeamId") // Select only necessary fields
      .lean(); // Use lean for performance

    if (!lastLog) {
      console.log(
        `[getLastTeamForKart] No relevant assignment history found for kart ${kartId}`
      );
      return null; // No relevant history found
    }

    let relevantTeamId: mongoose.Types.ObjectId | null | undefined = null;

    // Determine the most relevant team ID based on the last event
    if (lastLog.eventType === "ASSIGNED_TO_ROW" && lastLog.previousTeamId) {
      relevantTeamId = lastLog.previousTeamId;
    } else if (lastLog.teamId) {
      relevantTeamId = lastLog.teamId;
    }

    if (!relevantTeamId || !isValidObjectId(relevantTeamId.toString())) {
      console.log(
        `[getLastTeamForKart] Could not determine a valid relevant team ID from the last log for kart ${kartId}. Log:`,
        lastLog
      );
      return null;
    }

    console.log(
      `[getLastTeamForKart] Found relevant team ID ${relevantTeamId} for kart ${kartId} from event type ${lastLog.eventType}`
    );

    // Fetch and return the actual team document details needed
    const team = await Team.findById(relevantTeamId)
      .select("_id name number") // Select only needed fields
      .lean();

    if (!team) {
      console.warn(
        `[getLastTeamForKart] Team document with ID ${relevantTeamId} not found, although referenced in logs for kart ${kartId}.`
      );
    }

    return team; // Return the lean team object or null if not found
  } catch (error) {
    console.error(
      `[getLastTeamForKart] Error finding last team for kart ${kartId}:`,
      error
    );
    return null; // Or throw error if you want the endpoint to return 500
  }
}
