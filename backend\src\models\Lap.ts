import { Schema, model, Types } from "mongoose";
import { Document } from 'mongoose'; // Import Document

// Interface extending Document
export interface ILap extends Document {
  // _id: string; // Changed
  _id: Types.ObjectId; // Use ObjectId type
  teamId: Types.ObjectId;
  kartId: Types.ObjectId;
  driverId: Types.ObjectId;
  lapTime: number; // milliseconds
  lapNumber: number;
  timestamp: Date; // Time lap was completed/recorded
  // createdAt?: Date; // Optional via timestamps
  // updatedAt?: Date; // Optional via timestamps
}

// Schema remains the same
const lapSchema = new Schema<ILap>({
  teamId: {
    type: Schema.Types.ObjectId,
    ref: "Team",
    required: [true, "Team ID is required for lap"],
    index: true,
  },
  kartId: {
    type: Schema.Types.ObjectId,
    ref: "Kart",
    required: [true, "Kart ID is required for lap"],
    index: true,
  },
  driverId: {
    type: Schema.Types.ObjectId,
    ref: "Driver",
    required: [true, "Driver ID is required for lap"],
    index: true,
  },
  lapTime: {
    type: Number,
    required: [true, "Lap time is required"],
    min: [0, "Lap time cannot be negative"],
  },
  lapNumber: {
    type: Number,
    required: [true, "Lap number is required"],
    min: [1, "Lap number must be positive"],
  },
  timestamp: {
    type: Date,
    default: Date.now,
    index: true,
  },
}, {
   timestamps: false
});

export const Lap = model<ILap>('Lap', lapSchema);
