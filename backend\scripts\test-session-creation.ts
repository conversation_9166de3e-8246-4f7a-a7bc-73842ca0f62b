#!/usr/bin/env ts-node

/**
 * Test script to verify session creation and pit row service
 */

import mongoose from 'mongoose';
import { ApexParserSimple } from '../src/services/apexParserSimple';
import { ApexSession, ApexTeam, ApexKart, ApexCompetitor } from '../src/models/ApexModels';

// Test configuration
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/raceplanner';

async function connectToDatabase() {
  try {
    await mongoose.connect(MONGODB_URI);
    console.log('✅ Connected to MongoDB');
  } catch (error) {
    console.error('❌ Failed to connect to MongoDB:', error);
    process.exit(1);
  }
}

async function cleanupTestData() {
  console.log('🧹 Cleaning up test data...');
  await ApexSession.deleteMany({ title1: { $regex: /Test Session/ } });
  await ApexTeam.deleteMany({ name: { $regex: /Test/ } });
  await ApexKart.deleteMany({ kartNumber: { $in: [19, 42, 14] } });
  await ApexCompetitor.deleteMany({ name: { $regex: /Test/ } });
}

async function testSessionCreation() {
  console.log('\n🔍 Testing Session Creation...\n');

  const parser = new ApexParserSimple({ enableLogging: true });

  // Test session creation with grid
  const gridMessage = `init|r|
title1||Test Session Creation
title2||Debug Test
track||Test Track
grid||<tbody><tr data-id="r0"><td data-id="c1" data-type="sta">N/A</td><td data-id="c2" data-type="rk">Rnk</td><td data-id="c3" data-type="no">Kart</td><td data-id="c4" data-type="nat">Nation</td><td data-id="c5" data-type="dr">Team</td><td data-id="c15" data-type="pit">Pits</td></tr><tr data-id="r17749"><td data-id="r17749c1">N/A</td><td data-id="r17749c2">1</td><td data-id="r17749c3">19</td><td data-id="r17749c4">N/A</td><td data-id="r17749c5">Test Team 1</td><td data-id="r17749c15">0</td></tr><tr data-id="r17766"><td data-id="r17766c1">N/A</td><td data-id="r17766c2">2</td><td data-id="r17766c3">42</td><td data-id="r17766c4">N/A</td><td data-id="r17766c5">Test Team 2</td><td data-id="r17766c15">0</td></tr></tbody>`;

  console.log('📊 Sending grid message...');
  await parser.parseMessage(gridMessage);

  // Wait for database operations to complete
  await new Promise(resolve => setTimeout(resolve, 2000));

  // Check what was created
  console.log('\n📊 Checking database results...');
  
  const sessions = await ApexSession.find({ title1: { $regex: /Test Session/ } }).lean();
  console.log(`Sessions created: ${sessions.length}`);
  
  if (sessions.length > 0) {
    const session = sessions[0];
    console.log(`✅ Session created: ${session._id} - ${session.title1}`);
    
    const teams = await ApexTeam.find({ sessionId: session._id }).lean();
    console.log(`Teams created: ${teams.length}`);
    
    const karts = await ApexKart.find({ sessionId: session._id }).lean();
    console.log(`Karts created: ${karts.length}`);
    
    const competitors = await ApexCompetitor.find({ sessionId: session._id }).lean();
    console.log(`Competitors created: ${competitors.length}`);
    
    if (teams.length > 0) {
      console.log('✅ Team details:', teams.map(t => ({ name: t.name, kartId: t.currentKartId })));
    }
    
    if (karts.length > 0) {
      console.log('✅ Kart details:', karts.map(k => ({ number: k.kartNumber, teamId: k.currentTeamId })));
    }
    
    if (competitors.length > 0) {
      console.log('✅ Competitor details:', competitors.map(c => ({ name: c.name, websocketId: c.websocketId })));
    }
    
    return {
      sessionId: session._id,
      sessionsCreated: sessions.length,
      teamsCreated: teams.length,
      kartsCreated: karts.length,
      competitorsCreated: competitors.length
    };
  } else {
    console.log('❌ No sessions were created');
    return {
      sessionId: null,
      sessionsCreated: 0,
      teamsCreated: 0,
      kartsCreated: 0,
      competitorsCreated: 0
    };
  }
}

async function main() {
  try {
    await connectToDatabase();
    await cleanupTestData();
    
    const results = await testSessionCreation();
    
    console.log('\n📊 Test Results Summary:');
    console.log(`   Session ID: ${results.sessionId}`);
    console.log(`   Sessions Created: ${results.sessionsCreated}`);
    console.log(`   Teams Created: ${results.teamsCreated}`);
    console.log(`   Karts Created: ${results.kartsCreated}`);
    console.log(`   Competitors Created: ${results.competitorsCreated}`);
    
    if (results.sessionsCreated > 0 && results.teamsCreated > 0) {
      console.log('\n✅ Session creation test passed!');
    } else {
      console.log('\n❌ Session creation test failed!');
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  } finally {
    await cleanupTestData();
    await mongoose.disconnect();
    console.log('🔌 Disconnected from MongoDB');
  }
}

// Run the test
if (require.main === module) {
  main().catch(console.error);
}
