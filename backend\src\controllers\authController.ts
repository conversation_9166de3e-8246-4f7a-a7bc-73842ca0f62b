import { Request, Response } from "express";
import jwt from "jsonwebtoken";
import { User, IUser } from "../models/User";
import { sendErrorResponse } from "../utils/controllerUtils";

// Generate JWT token
const generateToken = (userId: string) => {
  return jwt.sign({ id: userId }, process.env.JWT_SECRET || "your-secret-key", {
    expiresIn: "30d",
  });
};

// Register a new user
export const register = async (req: Request, res: Response) => {
  console.log('[Auth Controller] POST /api/auth/register - Attempting to register new user.');
  try {
    const { name, email, password } = req.body;

    // Check if user already exists
    const userExists = await User.findOne({ email });
    if (userExists) {
      return sendErrorResponse(res, "User with this email already exists", 400);
      console.warn(`[Auth Controller] Registration failed: Email already exists (${email}).`);
    }

    // Create new user
    const user = await User.create({
      name,
      email,
      password,
    });

    console.log(`[Auth Controller] User registered successfully with ID: ${user._id}`);
    // Return success without sending token
    res.status(201).json({
      success: true,
      message: "User registered successfully",
    });
  } catch (error) {
    console.error("Registration error:", error);
    sendErrorResponse(res, "Error during registration", 500, error);
  }
};

// Login user
export const login = async (req: Request, res: Response) => {
  console.log('[Auth Controller] POST /api/auth/login - Attempting user login.');
  try {
    const { email, password } = req.body;

    // Check for email and password
    if (!email || !password) {
      return sendErrorResponse(res, "Please provide email and password", 400);
    }
    console.log(`[Auth Controller] Login attempt for email: ${email}`);

    // Find user - explicitly include password for comparison
    const user = await User.findOne({ email }).select("+password") as IUser | null;
    
    // Check if user exists
    if (!user) {
      return sendErrorResponse(res, "Invalid credentials", 401);
      console.warn(`[Auth Controller] Login failed: User not found for email ${email}.`);
    }

    // Check if password matches
    const isMatch = await user.comparePassword(password);
    if (!isMatch) {
      console.warn(`[Auth Controller] Login failed: Invalid password for user ID ${user._id}.`);
      return sendErrorResponse(res, "Invalid credentials", 401);
    }

    // Generate token - use type assertion for _id
    const userId = user._id as unknown as string;
    const token = generateToken(userId);

    console.log(`[Auth Controller] User ID ${user._id} logged in successfully.`);
    // Send response
    res.status(200).json({
      success: true,
      token,
      user: {
        id: user._id,
        name: user.name,
        email: user.email,
      },
    });
  } catch (error) {
    console.error("Login error:", error);
    sendErrorResponse(res, "Error during login", 500, error);
  }
};

// Get current user (protected route)
export const getCurrentUser = async (req: Request, res: Response) => {
  console.log('[Auth Controller] GET /api/auth/me - Attempting to fetch current user.');
  try {
    // User should be attached to request by auth middleware
    const user = await User.findById((req as any).user.id) as IUser | null;
    
    if (!user) {
      console.warn(`[Auth Controller] Current user not found for ID: ${(req as any).user.id}`);
      return sendErrorResponse(res, "User not found", 404);
    }
    
    res.status(200).json({
      success: true,
      user: {
        id: user._id,
        name: user.name,
        email: user.email,
        preferences: user.preferences || {
          useApexDatabase: false,
          selectedSessionId: null,
          selectedTeamId: null,
        },
      },
    });
    console.log(`[Auth Controller] Fetched current user ID: ${user._id}`);
  } catch (error) {
    console.error("Get current user error:", error);
    sendErrorResponse(res, "Error retrieving user", 500, error);
  }
};

// Update user preferences
export const updateUserPreferences = async (req: Request, res: Response) => {
  console.log('[Auth Controller] PUT /api/auth/preferences - Attempting to update user preferences.');
  try {
    const { preferences } = req.body;

    if (!preferences || typeof preferences !== 'object') {
      return sendErrorResponse(res, "Valid preferences object is required", 400);
    }

    // User should be attached to request by auth middleware
    const userId = (req as any).user.id;

    const user = await User.findByIdAndUpdate(
      userId,
      {
        $set: {
          'preferences.useApexDatabase': preferences.useApexDatabase,
          'preferences.selectedSessionId': preferences.selectedSessionId,
          'preferences.selectedTeamId': preferences.selectedTeamId,
        }
      },
      { new: true }
    ) as IUser | null;

    if (!user) {
      console.warn(`[Auth Controller] User not found for preferences update: ${userId}`);
      return sendErrorResponse(res, "User not found", 404);
    }

    console.log(`[Auth Controller] Preferences updated for user: ${user.name}`);

    res.status(200).json({
      success: true,
      message: 'Preferences updated successfully',
      preferences: user.preferences,
    });

  } catch (error) {
    console.error('[Auth Controller] Update preferences error:', error);
    sendErrorResponse(res, "Failed to update preferences", 500, error);
  }
};
