# Verbose Pit Detection Logging

## Overview

Added comprehensive verbose logging to track pit field detection and identify why `apex_pitstops` isn't populating. The logging will help diagnose each step of the pit detection process.

## Verbose Logging Added

### ✅ **1. Pit Field Detection**

```typescript
case 'pit': // Pit column
  if (this.config.enableLogging) {
    console.log(`🔍 PIT FIELD DETECTED: competitorId=${competitorId}, data.type=${data.type}, data.value=${data.value}`);
  }
```

**What it shows**:
- When a pit field is detected
- The competitor ID being processed
- The data type (should be 'in' for counts or other for status)
- The actual value being processed

### ✅ **2. Pit Update Handler Entry**

```typescript
if (this.config.enableLogging) {
  console.log(`🏁 handlePitUpdate called: websocketId=${websocketId}, pitStatus=${pitStatus}`);
}
```

**What it shows**:
- When the pit update handler is called
- The websocket ID being processed
- The pit status value received

### ✅ **3. Competitor Mapping Check**

```typescript
if (!competitorObjectId) {
  if (this.config.enableLogging) {
    console.warn(`❌ No competitor mapping found for websocket ID: ${websocketId}`);
    console.warn(`📋 Available mappings (${this.websocketToCompetitorMap.size} total):`, Array.from(this.websocketToCompetitorMap.keys()));
    console.warn(`🔍 Pit status was: ${pitStatus}`);
  }
  return;
}

if (this.config.enableLogging) {
  console.log(`✅ Found competitor mapping: ${websocketId} -> ${competitorObjectId}`);
}
```

**What it shows**:
- Whether competitor mapping exists for the websocket ID
- All available mappings if mapping fails
- The pit status that was being processed
- Successful mapping confirmation

### ✅ **4. Competitor Database Lookup**

```typescript
const competitor = await ApexCompetitor.findById(competitorObjectId);
if (!competitor) {
  if (this.config.enableLogging) {
    console.warn(`❌ Competitor not found for ObjectId: ${competitorObjectId}`);
  }
  return;
}
```

**What it shows**:
- Whether the competitor exists in the database
- The ObjectId being searched for

### ✅ **5. Pit Status Type Analysis**

```typescript
if (this.config.enableLogging) {
  console.log(`🏁 Processing pit ${pitStatus} for competitor: ${competitor.name} (ID: ${competitor._id})`);
  console.log(`🔍 Pit status type check: isNumeric=${this.isPitStatusNumber(pitStatus)}, value="${pitStatus}"`);
}
```

**What it shows**:
- Which competitor is being processed
- Whether the pit status is detected as numeric
- The exact pit status value

### ✅ **6. Pit Status Routing**

```typescript
if (pitStatus === 'IN') {
  if (this.config.enableLogging) {
    console.log(`🏁 Handling pit entry (string): ${competitor.name}`);
  }
  await this.handlePitEntry(competitor, competitorObjectId);
} else if (pitStatus === 'OUT') {
  if (this.config.enableLogging) {
    console.log(`🏁 Handling pit exit (string): ${competitor.name}`);
  }
  await this.handlePitExit(competitor, competitorObjectId);
} else if (this.isPitStatusNumber(pitStatus)) {
  if (this.config.enableLogging) {
    console.log(`🏁 Handling numeric pit status: ${competitor.name} -> ${pitStatus} seconds`);
  }
  await this.handleNumericPitStatus(competitor, competitorObjectId, pitStatus);
} else {
  if (this.config.enableLogging) {
    console.warn(`❌ Unknown pit status: "${pitStatus}" for ${competitor.name}`);
    console.warn(`   Status type: ${typeof pitStatus}, length: ${pitStatus.length}`);
    console.warn(`   ASCII codes: ${Array.from(pitStatus).map(c => c.charCodeAt(0)).join(', ')}`);
  }
}
```

**What it shows**:
- Which pit status handler is being called
- Detailed analysis of unknown pit statuses (type, length, ASCII codes)

### ✅ **7. Active Pit Stop Check**

```typescript
if (this.config.enableLogging) {
  console.log(`🔍 Active pit stop check: ${activePitStop ? 'FOUND' : 'NOT FOUND'} for ${competitor.name}`);
  if (activePitStop) {
    console.log(`   Existing pit stop ID: ${activePitStop._id}, pitInTime: ${activePitStop.pitInTime}`);
  }
}
```

**What it shows**:
- Whether an active pit stop exists for the competitor
- Details of existing pit stops

### ✅ **8. Pit Entry Creation**

```typescript
if (this.config.enableLogging) {
  console.log(`🏁 Creating pit entry for: ${competitor.name} (ID: ${competitorObjectId})`);
  console.log(`   Session ID: ${this.currentSession._id}`);
  console.log(`   Kart ID: ${competitor.kartId}`);
}

// ... pit stop data creation ...

if (this.config.enableLogging) {
  console.log(`🔍 Pit stop data to create:`, JSON.stringify(pitStopData, null, 2));
}

const createdPitStop = await ApexPitStop.create(pitStopData);

if (this.config.enableLogging) {
  console.log(`✅ Pit entry recorded successfully: ${competitor.name}`);
  console.log(`   PitStop ID: ${createdPitStop._id}`);
  console.log(`   Race time: ${raceTimeAtPitIn}s`);
  console.log(`   Pit in time: ${createdPitStop.pitInTime}`);
}
```

**What it shows**:
- All data being used to create the pit stop
- The complete pit stop data object
- Confirmation of successful creation with details

## Expected Logging Output

### **When Pit Field is Detected**
```
🔍 PIT FIELD DETECTED: competitorId=17788, data.type=pit, data.value=6
🏁 Potential pit status update: 17788 -> 6 (type: pit)
🏁 handlePitUpdate called: websocketId=17788, pitStatus=6
✅ Found competitor mapping: 17788 -> 674a1b2c3d4e5f6789012345
🏁 Processing pit 6 for competitor: VANHAT KOIRAT (ID: 674a1b2c3d4e5f6789012345)
🔍 Pit status type check: isNumeric=true, value="6"
🏁 Handling numeric pit status: VANHAT KOIRAT -> 6 seconds
🔍 Checking for existing active pit stop...
🔍 Active pit stop check: NOT FOUND for VANHAT KOIRAT
🏁 Creating new pit entry for VANHAT KOIRAT (duration: 6s)
🏁 Creating pit entry for: VANHAT KOIRAT (ID: 674a1b2c3d4e5f6789012345)
   Session ID: 674a1b2c3d4e5f6789012340
   Kart ID: 674a1b2c3d4e5f6789012342
🔍 Pit stop data to create: {
  "sessionId": "674a1b2c3d4e5f6789012340",
  "competitorId": "674a1b2c3d4e5f6789012345",
  "kartId": "674a1b2c3d4e5f6789012342",
  "pitInTime": "2024-11-09T15:30:45.123Z",
  "raceTimeAtPitIn": 285,
  "pitCurrentDuration": 0,
  "reason": "Regular",
  "isActive": true
}
✅ Pit entry recorded successfully: VANHAT KOIRAT
   PitStop ID: 674a1b2c3d4e5f6789012350
   Race time: 285s
   Pit in time: 2024-11-09T15:30:45.123Z
```

### **When Pit Field is NOT Working**
```
🔍 PIT FIELD DETECTED: competitorId=17788, data.type=pit, data.value=6
⚠️ Pit field detected but missing data: competitorId=undefined, value=6
```
OR
```
🔍 PIT FIELD DETECTED: competitorId=17788, data.type=pit, data.value=6
🏁 Potential pit status update: 17788 -> 6 (type: pit)
🏁 handlePitUpdate called: websocketId=17788, pitStatus=6
❌ No competitor mapping found for websocket ID: 17788
📋 Available mappings (0 total): []
🔍 Pit status was: 6
```

## Test Script

Created `backend/scripts/test-pit-detection-verbose.ts` to test pit detection with verbose logging:

```bash
cd backend
npx ts-node scripts/test-pit-detection-verbose.ts
```

This script will:
1. Create a test session with competitors
2. Send pit status messages (both numeric and string)
3. Show all verbose logging output
4. Check if pit stops are created in the database

## Debugging Steps

### **1. Run Your Real Log**
Process your real log and look for these patterns in the output:

### **2. Check for Missing Competitor Mapping**
Look for:
```
❌ No competitor mapping found for websocket ID: 17788
```

### **3. Check for Field Type Issues**
Look for:
```
🔍 PIT FIELD DETECTED: competitorId=17788, data.type=???, data.value=6
```

### **4. Check for Pit Status Recognition**
Look for:
```
❌ Unknown pit status: "6" for VANHAT KOIRAT
```

### **5. Check for Database Creation Issues**
Look for successful creation:
```
✅ Pit entry recorded successfully: VANHAT KOIRAT
```

## Common Issues to Look For

### **Issue 1: No Competitor Mapping**
```
❌ No competitor mapping found for websocket ID: 17788
📋 Available mappings (0 total): []
```
**Solution**: Grid parsing failed or competitor creation failed

### **Issue 2: Wrong Data Type**
```
🔍 PIT FIELD DETECTED: competitorId=17788, data.type=in, data.value=6
```
**Solution**: Field type detection is routing to pit count instead of pit status

### **Issue 3: Pit Status Not Recognized**
```
❌ Unknown pit status: "6" for VANHAT KOIRAT
   Status type: string, length: 1
   ASCII codes: 54
```
**Solution**: Numeric detection logic issue

### **Issue 4: Database Creation Failure**
```
🏁 Creating pit entry for: VANHAT KOIRAT
[ERROR] Error handling pit entry: ...
```
**Solution**: Database schema or connection issue

Run your real log with this verbose logging and share the output - it will show exactly where the pit detection is failing!
