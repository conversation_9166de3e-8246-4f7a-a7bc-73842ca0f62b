#!/usr/bin/env node

/**
 * Test script to verify the validation fixes for ApexKart and ApexLap
 * Tests the specific issues that were causing validation errors
 */

const mongoose = require('mongoose');

// Simple schemas for testing (matching the actual models)
const ApexKartSchema = new mongoose.Schema({
  sessionId: { type: mongoose.Schema.Types.ObjectId, required: true, index: true },
  number: { type: Number, required: true },
  kartNumber: { type: Number, required: true, index: true },
  speed: { type: Number, default: 3 },
  teamId: { type: String, index: true },
  currentTeamId: { type: mongoose.Schema.Types.ObjectId },
  currentRowId: { type: mongoose.Schema.Types.ObjectId },
  currentDriverId: { type: String, required: true }, // This was missing!
  status: { type: String, enum: ['on_track', 'in_pits', 'out'], default: 'on_track' },
  isActive: { type: Boolean, default: true }
}, {
  timestamps: true,
  collection: 'apex_karts'
});

const ApexLapSchema = new mongoose.Schema({
  sessionId: { type: mongoose.Schema.Types.ObjectId, required: true, index: true },
  competitorId: { type: String, required: true, index: true }, // This was missing!
  teamId: { type: String, required: true, index: true },
  kartNumber: { type: Number, required: true },
  lapTime: { type: Number, required: true }, // This was expecting Number, not String!
  lapTimeFormatted: { type: String },
  timestamp: { type: Date, default: Date.now }
}, {
  timestamps: true,
  collection: 'apex_laps'
});

const ApexKart = mongoose.model('ApexKart', ApexKartSchema);
const ApexLap = mongoose.model('ApexLap', ApexLapSchema);

// Test lap time conversion function
function convertLapTimeToMs(lapTimeStr) {
  if (!lapTimeStr || lapTimeStr === '') return null;

  try {
    const timeStr = lapTimeStr.trim();
    
    // Format: "1:11.328" (minutes:seconds.milliseconds)
    if (timeStr.includes(':') && timeStr.includes('.')) {
      const parts = timeStr.split(':');
      if (parts.length === 2 && parts[0] && parts[1]) {
        const minutes = parseInt(parts[0]);
        const secondsParts = parts[1].split('.');
        if (secondsParts.length === 2 && secondsParts[0] && secondsParts[1]) {
          const seconds = parseInt(secondsParts[0]);
          const milliseconds = parseInt(secondsParts[1].padEnd(3, '0').substring(0, 3));
          
          return (minutes * 60 * 1000) + (seconds * 1000) + milliseconds;
        }
      }
    }
    
    // Format: "71.328" (seconds.milliseconds)
    if (timeStr.includes('.') && !timeStr.includes(':')) {
      const parts = timeStr.split('.');
      if (parts.length === 2 && parts[0] && parts[1]) {
        const seconds = parseInt(parts[0]);
        const milliseconds = parseInt(parts[1].padEnd(3, '0').substring(0, 3));
        
        return (seconds * 1000) + milliseconds;
      }
    }
    
    // Format: just seconds as string
    const numericValue = parseFloat(timeStr);
    if (!isNaN(numericValue)) {
      return Math.round(numericValue * 1000);
    }
    
    return null;
  } catch (error) {
    console.warn(`Error converting lap time "${lapTimeStr}":`, error);
    return null;
  }
}

// Test ApexKart creation
async function testApexKartCreation() {
  console.log('\n🧪 Testing ApexKart creation with required fields...');
  
  try {
    const sessionId = new mongoose.Types.ObjectId();
    
    // Test 1: Create kart with all required fields (should succeed)
    console.log('📝 Test 1: Creating kart with all required fields...');
    const kartData = {
      sessionId,
      number: 1,
      kartNumber: 1,
      speed: 3,
      teamId: '45393',
      currentTeamId: null,
      currentRowId: null,
      currentDriverId: '45393', // Required field - this was missing before!
      status: 'on_track',
      isActive: true
    };
    
    const kart = await ApexKart.create(kartData);
    console.log(`✅ Kart created successfully: ${kart._id}`);
    console.log(`   currentDriverId: ${kart.currentDriverId}`);
    
    // Test 2: Try to create kart without currentDriverId (should fail)
    console.log('\n📝 Test 2: Creating kart without currentDriverId (should fail)...');
    try {
      const invalidKart = await ApexKart.create({
        sessionId,
        number: 2,
        kartNumber: 2,
        teamId: '45394'
        // Missing currentDriverId
      });
      console.log('❌ This should not have succeeded!');
    } catch (validationError) {
      console.log('✅ Validation error as expected:');
      console.log(`   ${validationError.message}`);
    }
    
    // Cleanup
    await ApexKart.deleteMany({ sessionId });
    
  } catch (error) {
    console.error('❌ ApexKart test failed:', error);
  }
}

// Test ApexLap creation
async function testApexLapCreation() {
  console.log('\n🧪 Testing ApexLap creation with required fields...');
  
  try {
    const sessionId = new mongoose.Types.ObjectId();
    
    // Test 1: Create lap with all required fields (should succeed)
    console.log('📝 Test 1: Creating lap with all required fields...');
    
    // Test lap time conversion
    const originalLapTime = '1:11.328';
    const lapTimeMs = convertLapTimeToMs(originalLapTime);
    console.log(`   Lap time conversion: "${originalLapTime}" → ${lapTimeMs}ms`);
    
    const lapData = {
      sessionId,
      competitorId: '45393', // Required field - this was missing before!
      teamId: '45393',
      kartNumber: 1,
      lapTime: lapTimeMs, // Required Number field - was String before!
      lapTimeFormatted: originalLapTime,
      timestamp: new Date()
    };
    
    const lap = await ApexLap.create(lapData);
    console.log(`✅ Lap created successfully: ${lap._id}`);
    console.log(`   competitorId: ${lap.competitorId}`);
    console.log(`   lapTime: ${lap.lapTime}ms`);
    console.log(`   lapTimeFormatted: ${lap.lapTimeFormatted}`);
    
    // Test 2: Try to create lap without competitorId (should fail)
    console.log('\n📝 Test 2: Creating lap without competitorId (should fail)...');
    try {
      const invalidLap = await ApexLap.create({
        sessionId,
        teamId: '45394',
        kartNumber: 2,
        lapTime: 71328
        // Missing competitorId
      });
      console.log('❌ This should not have succeeded!');
    } catch (validationError) {
      console.log('✅ Validation error as expected:');
      console.log(`   ${validationError.message}`);
    }
    
    // Test 3: Try to create lap with string lapTime (should fail)
    console.log('\n📝 Test 3: Creating lap with string lapTime (should fail)...');
    try {
      const invalidLap = await ApexLap.create({
        sessionId,
        competitorId: '45395',
        teamId: '45395',
        kartNumber: 3,
        lapTime: '1:11.328' // String instead of Number
      });
      console.log('❌ This should not have succeeded!');
    } catch (validationError) {
      console.log('✅ Validation error as expected:');
      console.log(`   ${validationError.message}`);
    }
    
    // Cleanup
    await ApexLap.deleteMany({ sessionId });
    
  } catch (error) {
    console.error('❌ ApexLap test failed:', error);
  }
}

// Test lap time conversion function
async function testLapTimeConversion() {
  console.log('\n🧪 Testing lap time conversion function...');
  
  const testCases = [
    { input: '1:11.328', expected: 71328 },
    { input: '71.328', expected: 71328 },
    { input: '2:05.123', expected: 125123 },
    { input: '59.999', expected: 59999 },
    { input: '0:30.500', expected: 30500 },
    { input: 'invalid', expected: null },
    { input: '', expected: null },
    { input: '1:11:328', expected: null } // Invalid format
  ];
  
  for (const testCase of testCases) {
    const result = convertLapTimeToMs(testCase.input);
    const status = result === testCase.expected ? '✅' : '❌';
    console.log(`   ${status} "${testCase.input}" → ${result} (expected: ${testCase.expected})`);
  }
}

// Main test execution
async function runValidationTests() {
  console.log('🚀 Validation Fixes Test Suite');
  console.log('===============================');
  console.log('Testing: ApexKart currentDriverId + ApexLap competitorId/lapTime fixes');
  
  try {
    // Connect to database
    const mongoUri = process.env.MONGODB_URI || 'mongodb://localhost:27017/race-planner-test';
    await mongoose.connect(mongoUri);
    console.log('✅ Connected to database');
    
    // Run tests
    await testLapTimeConversion();
    await testApexKartCreation();
    await testApexLapCreation();
    
    console.log('\n🎉 All validation tests completed!');
    console.log('\n📝 Summary:');
    console.log('   ✅ ApexKart now requires currentDriverId field');
    console.log('   ✅ ApexLap now requires competitorId field');
    console.log('   ✅ ApexLap lapTime must be Number (milliseconds)');
    console.log('   ✅ Lap time conversion function works correctly');
    console.log('   ✅ Validation errors are properly handled');
    
  } catch (error) {
    console.error('❌ Test suite failed:', error);
  } finally {
    await mongoose.disconnect();
    console.log('👋 Disconnected from database');
  }
}

// Run the test suite
if (require.main === module) {
  runValidationTests().catch(console.error);
}

module.exports = { 
  testApexKartCreation, 
  testApexLapCreation, 
  testLapTimeConversion,
  convertLapTimeToMs
};
