import React from 'react';
import { IonText } from '@ionic/react';
import { 
  formatSecondsToTimeString,
  formatRaceTime,
  formatDuration,
  TimeFormat
} from '../utils/timeUtils';

type DisplayFormat = TimeFormat | 'duration' | 'race';

interface TimeDisplayProps {
  seconds: number;
  format: DisplayFormat;
  color?: string;
  showPositiveSign?: boolean;
  className?: string;
  size?: 'small' | 'default' | 'large';
}

/**
 * Enhanced component for displaying time values consistently throughout the application
 */
const TimeDisplay: React.FC<TimeDisplayProps> = ({ 
  seconds, 
  format, 
  color, 
  showPositiveSign = true,
  className = '',
  size = 'default'
}) => {
  // Format the time based on the specified format
  const getFormattedTime = (): string => {
    switch (format) {
      case 'hh:mm':
      case 'mm:ss':
      case 'hh:mm:ss':
      case 'mm:ss.fff':
        return formatSecondsToTimeString(seconds, format);
      case 'duration':
        return formatDuration(seconds);
      case 'race':
        return formatRaceTime(seconds, showPositiveSign);
      default:
        return seconds.toString();
    }
  };

  // Determine CSS class based on size
  const sizeClass = size === 'small' ? 'time-small' : size === 'large' ? 'time-large' : '';
  const combinedClassName = `${className} ${sizeClass}`.trim();

  return (
    <IonText color={color} className={combinedClassName}>
      {getFormattedTime()}
    </IonText>
  );
};

export default TimeDisplay;
