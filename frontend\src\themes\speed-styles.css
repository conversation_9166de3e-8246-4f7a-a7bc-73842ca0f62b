/* --- Speed Color Variables (Keep from original) --- */
:root {
  --speed-color-superfast-rgb: var(--ion-color-tertiary-rgb); /* purple */
  --speed-color-success-rgb: var(--ion-color-success-rgb);
  --speed-color-warning-rgb: var(--ion-color-warning-rgb);
  --speed-color-danger-rgb: var(--ion-color-danger-rgb);
  --speed-color-medium-rgb: var(--ion-color-medium-rgb);

  --speed-color-superfast: var(--ion-color-tertiary);
  --speed-color-success: var(--ion-color-success);
  --speed-color-warning: var(--ion-color-warning);
  --speed-color-danger: var(--ion-color-danger);
  --speed-color-medium: var(--ion-color-medium);

  /* Contrasting text colors */
  --speed-contrast-superfast: white;
  --speed-contrast-success: var(--ion-color-success-contrast);
  --speed-contrast-warning: var(--ion-color-warning-contrast);
  --speed-contrast-danger: var(--ion-color-danger-contrast);
  --speed-contrast-medium: var(--ion-color-medium-contrast);
}

/* Speed Column Styling (Keep styles from original) */
.speed-col {
  /* Style the content within the IonCol */
  display: flex;
  justify-content: center; /* Center the indicator within the column */
  align-items: center;
  padding-right: 8px; /* Add padding matching header */
}
.speed-indicator {
  width: 90%;
  max-width: 100px;
  padding: 4px 6px;
  border-radius: 4px;
  text-align: center;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.speed-label {
  font-size: 0.9em;
  font-weight: 500;
  color: white; /* Default */
}

/* --- Global Modal Speed Chip Styles (If applicable) --- */
/* Prefixing with a general class might be safer than just ion-modal */
.global-speed-modal ion-modal .speed-chip, /* Example prefix */
  ion-modal .speed-chip {
  /* Or keep as is if always used this way */
  margin-right: 10px;
  height: 28px;
  font-size: 0.9em;
  padding: 0 8px;
  --border-width: 1px;
}
ion-modal .speed-chip ion-icon {
  display: none;
}

ion-modal .speed-chip-superfast {
  --color: var(--speed-color-superfast);
  --border-color: var(--speed-color-superfast);
}
ion-modal .speed-chip-success {
  --color: var(--speed-color-success);
  --border-color: var(--speed-color-success);
}
ion-modal .speed-chip-warning {
  --color: var(--speed-color-warning);
  --border-color: var(--speed-color-warning);
}
ion-modal .speed-chip-danger {
  --color: var(--speed-color-danger);
  --border-color: var(--speed-color-danger);
}
ion-modal .speed-chip-medium {
  --color: var(--speed-color-medium);
  --border-color: var(--speed-color-medium);
}

/* Apply background and text colors */
.speed-bg-superfast { background-color: var(--speed-color-superfast); }
.speed-bg-superfast .speed-label { color: var(--speed-contrast-superfast); }

.speed-bg-success { background-color: var(--speed-color-success); }
.speed-bg-success .speed-label { color: var(--speed-contrast-success); }

.speed-bg-warning { background-color: var(--speed-color-warning); }
.speed-bg-warning .speed-label { color: var(--speed-contrast-warning); }

.speed-bg-danger { background-color: var(--speed-color-danger); }
.speed-bg-danger .speed-label { color: var(--speed-contrast-danger); }

.speed-bg-medium { background-color: var(--speed-color-medium); }
.speed-bg-medium .speed-label { color: var(--speed-contrast-medium); }

/* --- Background Tint Styles (from common.css) --- */
/* Apply these directly to elements like IonItem for a subtle background */
.status-tint-fast { /* Renamed for clarity */
  --background: var(--ion-color-success-tint);
}
.status-tint-slow { /* Renamed for clarity */
  --background: var(--ion-color-danger-tint);
}
.status-tint-mid { /* Renamed for clarity */
  --background: var(--ion-color-warning-tint);
}
.status-tint-hotlap { /* Renamed for clarity */
  --background: var(--ion-color-tertiary-tint);
}
.status-tint-unknown { /* Renamed for clarity */
  --background: var(--ion-color-light-tint); /* Or medium-tint? */
}

.speed-button-container {
  display: flex;
  flex-direction: column;
  gap: 2px; /* Spacing between buttons */
}

.speed-button {
  --color: white; /* Ensure text is readable on colored backgrounds */
  text-transform: none; /* Keep label casing */
  text-shadow: 10px;
  transition: opacity 0.2s ease-in-out, transform 0.1s ease-in-out;
}

/* Apply background colors using existing classes */
.speed-button.speed-bg-superfast { --background: var(--ion-color-superfast); }
.speed-button.speed-bg-success { --background: var(--ion-color-success); }
.speed-button.speed-bg-warning { --background: var(--ion-color-warning); }
.speed-button.speed-bg-danger { --background: var(--ion-color-danger); }
.speed-button.speed-bg-medium { --background: var(--ion-color-medium); }

/* Style for the currently selected speed */
.speed-button.current-speed {
  /* Add a border or other indicator if desired */
  border: 10px solid var(--ion-color-secondary); 
}

/* Optional: Add hover/active states */
.speed-button:not([disabled]):hover {
  opacity: 0.9;
}
.speed-button:not([disabled]):active {
  transform: scale(0.98);
}
