import { Schema, model, Document } from "mongoose";
import argon2 from "argon2";

export interface IUser extends Document {
  name: string;
  email: string;
  password: string;
  preferences: {
    useApexDatabase: boolean;
    selectedSessionId: string | null;
    selectedTeamId: string | null;
  };
  comparePassword(candidatePassword: string): Promise<boolean>;
  createdAt: Date;
  updatedAt: Date;
}

const userSchema = new Schema<IUser>(
  {
    name: {
      type: String,
      required: [true, "Name is required"],
      trim: true,
    },
    email: {
      type: String,
      required: [true, "Email is required"],
      unique: true,
      trim: true,
      lowercase: true,
      match: [/^\S+@\S+\.\S+$/, "Please use a valid email address"],
    },
    password: {
      type: String,
      required: [true, "Password is required"],
      minlength: [6, "Password must be at least 6 characters"],
      select: false, // Don't include password in query results by default
    },
    preferences: {
      useApexDatabase: {
        type: Boolean,
        default: false,
      },
      selectedSessionId: {
        type: String,
        default: null,
      },
      selectedTeamId: {
        type: String,
        default: null,
      },
    },
  },
  {
    timestamps: true,
  }
);

// Hash password before saving
userSchema.pre("save", async function (next) {
  // Only hash the password if it's modified (or new)
  if (!this.isModified("password")) return next();
  console.log(`[User Model] Hashing password for user ID: ${this._id}`);

  try {
    // Hash password with argon2
    this.password = await argon2.hash(this.password);
    console.log(`[User Model] Password hashed successfully for user ID: ${this._id}`);
    next();
  } catch (error) {
    next(error as Error);
  }
});

// Method to compare passwords
userSchema.methods.comparePassword = async function (candidatePassword: string) {
  try {
    console.log(`[User Model] Comparing password for user ID: ${this._id}`);
    return await argon2.verify(this.password, candidatePassword);
  } catch (error) {
    console.error(`[User Model] Error comparing password for user ID ${this._id}:`, error);
    return false;
  }
};

export const User = model<IUser>("User", userSchema);