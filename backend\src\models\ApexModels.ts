import mongoose, { Document, Schema } from 'mongoose';

// Optimized Apex Session Model
export interface IApexSession extends Document {
  title1: string;
  title2: string;
  track: string;
  isActive: boolean;
  gridData?: any; // Keep minimal grid data if needed
  sessionData?: Record<string, any>; // Keep minimal session data if needed
}

const ApexSessionSchema = new Schema<IApexSession>({
  title1: { type: String, default: '' },
  title2: { type: String, default: '' },
  track: { type: String, default: '' },
  isActive: { type: Boolean, default: true, index: true },
  gridData: { type: Schema.Types.Mixed }, // Minimal grid data
  sessionData: { type: Schema.Types.Mixed, default: {} } // Minimal session data
}, {
  timestamps: true, // Provides createdAt and updatedAt
  collection: 'apex_sessions'
});

// Optimized Apex Team Model
export interface IApexTeam extends Document {
  sessionId: mongoose.Types.ObjectId;
  name: string;
  currentKartId?: mongoose.Types.ObjectId; // Reference to current kart (ObjectId)
  // pastKarts removed - can be queried by teamId in ApexKart collection
  // pits removed - can be queried by competitorId in ApexPitStop collection
  drivers: string[];
  nationality: string;
  bestLapTime?: number;
  bestLapTimeFormatted?: string;
  lastLapTime?: number;
  lastLapTimeFormatted?: string;
  position?: number;
  status: 'on_track' | 'in_pit' | 'active';
  isActive: boolean;
}

const ApexTeamSchema = new Schema<IApexTeam>({
  sessionId: { type: Schema.Types.ObjectId, ref: 'ApexSession', required: true, index: true },
  name: { type: String, required: true },
  currentKartId: { type: Schema.Types.ObjectId, ref: 'ApexKart', default: null },
  // pastKarts removed - can be queried by currentTeamId in ApexKart collection
  // pits removed - can be queried by competitorId in ApexPitStop collection
  drivers: [{ type: String }],
  nationality: { type: String, default: 'Unknown' },
  bestLapTime: { type: Number },
  bestLapTimeFormatted: { type: String },
  lastLapTime: { type: Number },
  lastLapTimeFormatted: { type: String },
  position: { type: Number },
  status: {
    type: String,
    enum: ['on_track', 'in_pit', 'active'],
    default: 'on_track'
  },
  isActive: { type: Boolean, default: true }
}, {
  timestamps: true,
  collection: 'apex_teams'
});

// Optimized Apex Kart Model
export interface IApexKart extends Document {
  sessionId: mongoose.Types.ObjectId;
  kartNumber: number; // Keep kart number for identification
  speed?: number;
  currentTeamId?: mongoose.Types.ObjectId; // Reference to current team (ObjectId)
  currentRowId?: mongoose.Types.ObjectId; // Reference to current pit row (ObjectId)
  status: 'on_track' | 'in_pit_row' | 'maintenance' | 'available';
  lastLapTimeFormatted?: string;
  bestLapTimeFormatted?: string;
  isActive: boolean;
}

const ApexKartSchema = new Schema<IApexKart>({
  sessionId: { type: Schema.Types.ObjectId, ref: 'ApexSession', required: true, index: true },
  kartNumber: { type: Number, required: true, index: true },
  speed: { type: Number, default: 3 },
  currentTeamId: { type: Schema.Types.ObjectId, ref: 'ApexTeam', default: null },
  currentRowId: { type: Schema.Types.ObjectId, ref: 'Row', default: null },
  status: {
    type: String,
    enum: ['on_track', 'in_pit_row', 'maintenance', 'available'],
    default: 'available',
    required: true,
  },
  lastLapTimeFormatted: { type: String },
  bestLapTimeFormatted: { type: String },
  isActive: { type: Boolean, default: true }
}, {
  timestamps: true,
  collection: 'apex_karts'
});

// Compound index for session and kart number
ApexKartSchema.index({ sessionId: 1, kartNumber: 1 }, { unique: true });

// Optimized Apex Competitor Model (uses only ObjectId, no string competitorId)
export interface IApexCompetitor extends Document {
  sessionId: mongoose.Types.ObjectId;
  teamId: mongoose.Types.ObjectId; // Reference to team (ObjectId)
  kartId: mongoose.Types.ObjectId; // Reference to kart (ObjectId)
  websocketId: string; // Original websocket ID for reference (e.g., "17742")
  name: string;
  nationality: string;
  drivers: string[];
  isActive: boolean;
}

const ApexCompetitorSchema = new Schema<IApexCompetitor>({
  sessionId: { type: Schema.Types.ObjectId, ref: 'ApexSession', required: true, index: true },
  teamId: { type: Schema.Types.ObjectId, ref: 'ApexTeam', required: true, index: true },
  kartId: { type: Schema.Types.ObjectId, ref: 'ApexKart', required: true, index: true },
  websocketId: { type: String, required: true, index: true }, // For websocket message mapping
  name: { type: String, required: true },
  nationality: { type: String, default: 'Unknown' },
  drivers: [{ type: String }],
  isActive: { type: Boolean, default: true }
}, {
  timestamps: true,
  collection: 'apex_competitors'
});

// Compound index for session and websocket ID
ApexCompetitorSchema.index({ sessionId: 1, websocketId: 1 }, { unique: true });

// Optimized Apex Lap Model
export interface IApexLap extends Document {
  sessionId: mongoose.Types.ObjectId;
  competitorId: mongoose.Types.ObjectId; // Reference to competitor (ObjectId)
  kartId: mongoose.Types.ObjectId; // Reference to kart (ObjectId)
  lapNumber: number;
  lapTime: number; // in milliseconds
  // lapTimeFormatted removed - can be calculated by frontend
  sector1?: number;
  sector2?: number;
  sector3?: number;
  // isBestLap removed - can be calculated by frontend
  // isPersonalBest removed - can be calculated by frontend
  // timestamp removed - use createdAt from timestamps
}

const ApexLapSchema = new Schema<IApexLap>({
  sessionId: { type: Schema.Types.ObjectId, ref: 'ApexSession', required: true, index: true },
  competitorId: { type: Schema.Types.ObjectId, ref: 'ApexCompetitor', required: true, index: true },
  kartId: { type: Schema.Types.ObjectId, ref: 'ApexKart', required: true, index: true },
  lapNumber: { type: Number, required: true }, // Proper lap number tracking
  lapTime: { type: Number, required: true }, // milliseconds
  // lapTimeFormatted removed - frontend can format
  sector1: { type: Number },
  sector2: { type: Number },
  sector3: { type: Number }
  // isBestLap removed - frontend can calculate
  // isPersonalBest removed - frontend can calculate
  // timestamp removed - use createdAt from timestamps
}, {
  timestamps: { createdAt: true, updatedAt: false }, // Only createdAt needed
  collection: 'apex_laps'
});

// Compound index for session, competitor and creation time
ApexLapSchema.index({ sessionId: 1, competitorId: 1, createdAt: 1 });

// Optimized Apex Pit Stop Model
export interface IApexPitStop extends Document {
  sessionId: mongoose.Types.ObjectId;
  competitorId: mongoose.Types.ObjectId; // Reference to competitor (ObjectId)
  kartId: mongoose.Types.ObjectId; // Reference to kart (ObjectId)
  pitInTime: Date;
  pitOutTime?: Date;
  pitDuration?: number; // in seconds (total duration when completed)
  pitCurrentDuration?: number; // in seconds (current duration for active pit stops)
  pitTotalDuration?: number; // in seconds (cumulative total calculated at pit out)
  raceTimeAtPitIn?: number; // in seconds (total race time when entering pit)
  raceTimeAtPitOut?: number; // in seconds (total race time when exiting pit)
}

const ApexPitStopSchema = new Schema<IApexPitStop>({
  sessionId: { type: Schema.Types.ObjectId, ref: 'ApexSession', required: true, index: true },
  competitorId: { type: Schema.Types.ObjectId, ref: 'ApexCompetitor', required: true, index: true },
  kartId: { type: Schema.Types.ObjectId, ref: 'ApexKart', required: true, index: true },
  pitInTime: { type: Date, required: true },
  pitOutTime: { type: Date },
  pitDuration: { type: Number }, // Total duration when completed
  pitCurrentDuration: { type: Number }, // Current duration for active pit stops
  pitTotalDuration: { type: Number }, // Cumulative total calculated at pit out
  raceTimeAtPitIn: { type: Number }, // Total race time when entering pit
  raceTimeAtPitOut: { type: Number }, // Total race time when exiting pit
}, {
  timestamps: true,
  collection: 'apex_pitstops'
});

// Compound index for session, competitor and pit in time
ApexPitStopSchema.index({ sessionId: 1, competitorId: 1, pitInTime: 1 });

// Apex Kart Swap Model - Track kart swaps during pit stops
export interface IApexKartSwap extends Document {
  sessionId: mongoose.Types.ObjectId;
  teamId: mongoose.Types.ObjectId; // Team that performed the swap
  pitStopId: mongoose.Types.ObjectId; // Pit stop during which swap occurred
  oldKartId: mongoose.Types.ObjectId; // Kart being swapped out
  newKartId: mongoose.Types.ObjectId; // Kart being swapped in
  pitRowId?: mongoose.Types.ObjectId; // Optional reference to pit row where swap occurred
  swapTime: Date; // When the swap occurred
  raceTimeAtSwap?: number; // Race time when swap occurred (seconds)
}

const ApexKartSwapSchema = new Schema<IApexKartSwap>({
  sessionId: { type: Schema.Types.ObjectId, ref: 'ApexSession', required: true, index: true },
  teamId: { type: Schema.Types.ObjectId, ref: 'ApexTeam', required: true, index: true },
  pitStopId: { type: Schema.Types.ObjectId, ref: 'ApexPitStop', required: true, index: true },
  oldKartId: { type: Schema.Types.ObjectId, ref: 'ApexKart', required: true, index: true },
  newKartId: { type: Schema.Types.ObjectId, ref: 'ApexKart', required: true, index: true },
  pitRowId: { type: Schema.Types.ObjectId, ref: 'PitRow', required: false }, // Optional reference to pit row
  swapTime: { type: Date, default: Date.now, required: true },
  raceTimeAtSwap: { type: Number, required: false }, // Race time when swap occurred (seconds)
}, {
  timestamps: true,
  collection: 'apex_kart_swaps'
});

// Indexes for efficient queries
ApexKartSwapSchema.index({ sessionId: 1, teamId: 1 });
ApexKartSwapSchema.index({ pitStopId: 1 });
ApexKartSwapSchema.index({ oldKartId: 1 });
ApexKartSwapSchema.index({ newKartId: 1 });
ApexKartSwapSchema.index({ swapTime: -1 });

// Apex Live Data Model (for real-time updates)
export interface IApexLiveData extends Document {
  sessionId: mongoose.Types.ObjectId;
  competitorId: mongoose.Types.ObjectId; // Reference to competitor (ObjectId)
  position: number;
  gap: string;
  interval: string;
  lastLapTime: string;
  bestLapTime: string;
  sector1: string;
  sector2: string;
  sector3: string;
  status: string;
  onTrackTime: string;
  pitCount: number;
  lastUpdated: Date;
  createdAt: Date;
  updatedAt: Date;
}
// Export optimized models
export const ApexSession = mongoose.model<IApexSession>('ApexSession', ApexSessionSchema);
export const ApexTeam = mongoose.model<IApexTeam>('ApexTeam', ApexTeamSchema);
export const ApexKart = mongoose.model<IApexKart>('ApexKart', ApexKartSchema);
export const ApexCompetitor = mongoose.model<IApexCompetitor>('ApexCompetitor', ApexCompetitorSchema);
export const ApexLap = mongoose.model<IApexLap>('ApexLap', ApexLapSchema);
export const ApexPitStop = mongoose.model<IApexPitStop>('ApexPitStop', ApexPitStopSchema);
export const ApexKartSwap = mongoose.model<IApexKartSwap>('ApexKartSwap', ApexKartSwapSchema);
