// controllers/lapController.ts
import { Request, Response } from 'express';
import { Lap } from '../models/Lap';
import { sendErrorResponse } from '../utils/controllerUtils';

// Create a new lap record
export const createLap = async (req: Request, res: Response) => {
  console.log('[Lap Controller] POST /api/laps - Attempting to create a new lap.');
  try {
    const { teamId, kartId, driverId, lapTime, lapNumber } = req.body;

    const newLap = new Lap({
      teamId,
      kartId,
      driverId,
      lapTime,
      lapNumber,
    });

    const savedLap = await newLap.save();
    console.log(`[Lap Controller] Lap created with ID: ${savedLap._id}`);
    res.status(201).json(savedLap);
  } catch (error: unknown) {
    sendErrorResponse(res, "Error creating lap", 500, error);
}
};

// Get all laps
export const getAllLaps = async (req: Request, res: Response) => {
  console.log('[Lap Controller] GET /api/laps - Attempting to fetch all laps.');
  try {
    const laps = await Lap.find()
      .populate('teamId')    // Populate team info
      .populate('kartId')    // Populate kart info
      .populate('driverId')  // Populate driver info
      .exec();
    res.status(200).json(laps);
  } catch (error) {
    console.error('Error retrieving laps:', error);
    res.status(500).json({ message: 'Error retrieving laps', error });
  }
};

// Get a specific lap by ID
export const getLapById = async (req: Request, res: Response): Promise<void> => {
  console.log(`[Lap Controller] GET /api/laps/${req.params.id} - Attempting to fetch lap by ID.`);
  try {
    const lap = await Lap.findById(req.params.id)
      .populate('teamId')
      .populate('kartId')
      .populate('driverId');

    if (!lap) {
      sendErrorResponse(res, 'Lap not found', 404);
      console.warn(`[Lap Controller] Lap with ID ${req.params.id} not found.`);
      return; // *** Add explicit return ***
    }

    res.status(200).json(lap);
  } catch (error) {
    console.error('Error retrieving lap:', error);
    res.status(500).json({ message: 'Error retrieving lap', error });
  }
};

// Update a lap record by ID
export const updateLap = async (req: Request, res: Response): Promise<void> => {
  console.log(`[Lap Controller] PUT /api/laps/${req.params.id} - Attempting to update lap by ID.`);
  try {
    const { lapTime, lapNumber } = req.body;

    const updatedLap = await Lap.findByIdAndUpdate(
      req.params.id,
      { lapTime, lapNumber },
      { new: true }
    );

    if (!updatedLap) {
      console.warn(`[Lap Controller] Lap with ID ${req.params.id} not found for update.`);
      sendErrorResponse(res, 'Lap not found', 404);
      return; // *** Add explicit return ***
    }

    res.status(200).json(updatedLap);
  } catch (error) {
    console.error('Error updating lap:', error);
    res.status(500).json({ message: 'Error updating lap', error });
  }
};

// Delete a lap record by ID
export const deleteLap = async (req: Request, res: Response): Promise<void>  => {
  console.log(`[Lap Controller] DELETE /api/laps/${req.params.id} - Attempting to delete lap by ID.`);
  try {
    const deletedLap = await Lap.findByIdAndDelete(req.params.id);

    if (!deletedLap) {
      sendErrorResponse(res, 'Lap not found', 404);
      console.warn(`[Lap Controller] Lap with ID ${req.params.id} not found for deletion.`);
      return; // *** Add explicit return ***
    }

    res.status(200).json({ message: 'Lap deleted successfully' });
  } catch (error) {
    console.error('Error deleting lap:', error);
    res.status(500).json({ message: 'Error deleting lap', error });
  }
};
