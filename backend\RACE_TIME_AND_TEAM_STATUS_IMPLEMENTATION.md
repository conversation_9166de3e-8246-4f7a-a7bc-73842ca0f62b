# Race Time and Team Status Implementation

## Overview

This document outlines the comprehensive implementation of race time tracking from `dyn1` messages, pit current duration from `otr` field, and team status tracking for frontend use.

## Changes Implemented

### ✅ **1. Race Time Tracking from dyn1 Messages**

**Problem**: `getCurrentRaceTime()` was calculating time from session start, not using actual race time data.

**Solution**: Implemented proper race time tracking using `dyn1` messages that contain milliseconds left in the race.

#### **Added Race Time Properties**
```typescript
export class ApexParserSimple extends EventEmitter {
  private raceTimeLeftMs: number = 0; // Race time left in milliseconds from dyn1
  private sessionStartTime: Date = new Date(); // When session started
  private raceDurationMs: number = 0; // Total race duration calculated from first dyn1
}
```

#### **dyn1 Message Handling**
```typescript
/**
 * Handle race time updates from dyn1 messages
 * dyn1 contains milliseconds left in the race
 */
private async handleRaceTimeUpdate(timeValue: string): Promise<void> {
  // Parse time value - could be in format "HH:MM:SS" or milliseconds
  let timeLeftMs = 0;
  
  if (timeValue.includes(':')) {
    // Format: "HH:MM:SS" or "MM:SS"
    const parts = timeValue.split(':').map(p => parseInt(p) || 0);
    if (parts.length === 3) {
      // HH:MM:SS
      timeLeftMs = (parts[0] * 3600 + parts[1] * 60 + parts[2]) * 1000;
    } else if (parts.length === 2) {
      // MM:SS
      timeLeftMs = (parts[0] * 60 + parts[1]) * 1000;
    }
  } else {
    // Assume milliseconds
    timeLeftMs = parseInt(timeValue) || 0;
  }

  // Update race time tracking
  this.raceTimeLeftMs = timeLeftMs;
  
  // Calculate total race duration from first dyn1 message
  if (this.raceDurationMs === 0 && timeLeftMs > 0) {
    this.raceDurationMs = timeLeftMs;
  }
}
```

#### **Updated getCurrentRaceTime Method**
```typescript
/**
 * Get current race time in seconds from dyn1 data
 * Race time = total race duration - time left
 */
private getCurrentRaceTime(): number {
  if (this.raceDurationMs === 0 || this.raceTimeLeftMs === 0) {
    // Fallback to session time if no dyn1 data available
    if (!this.currentSession) return 0;
    const sessionStartTime = this.currentSession.createdAt || new Date();
    const currentTime = new Date();
    const raceTimeMs = currentTime.getTime() - sessionStartTime.getTime();
    return Math.floor(raceTimeMs / 1000);
  }
  
  // Calculate race time from dyn1 data
  const raceTimeMs = this.raceDurationMs - this.raceTimeLeftMs;
  return Math.floor(raceTimeMs / 1000); // Return seconds
}
```

### ✅ **2. Pit Current Duration from otr Field**

**Problem**: `pitCurrentDuration` was not being updated from the `otr` field when competitors are in pit.

**Solution**: Enhanced `otr` field handling to update `pitCurrentDuration` for active pit stops.

#### **Enhanced otr Field Handling**
```typescript
case 'otr': // On track time column
  if (competitorId && data.value) {
    if (data.type === 'to' || data.type === 'in') {
      // Time on track update
      await this.handleTimeOnTrackUpdate(competitorId, data.value);
    } else {
      // Check if this is pit duration update (when competitor is in pit)
      await this.handlePitDurationUpdate(competitorId, data.value);
    }
  }
  break;
```

#### **Enhanced Pit Duration Update**
```typescript
/**
 * Handle pit duration updates from otr field
 * This updates pitCurrentDuration for active pit stops
 */
private async handlePitDurationUpdate(websocketId: string, durationValue: string): Promise<void> {
  // Parse duration (format like "00." or "30." for seconds)
  const durationSeconds = this.parsePitDuration(durationValue);
  
  // Find active pit stop for this competitor
  const activePitStop = await ApexPitStop.findOne({
    sessionId: this.currentSession._id,
    competitorId: competitorObjectId,
    pitOutTime: { $exists: false }
  }).sort({ pitInTime: -1 });

  if (activePitStop) {
    // Update current duration from otr field
    await ApexPitStop.updateOne(
      { _id: activePitStop._id },
      { pitCurrentDuration: durationSeconds }
    );

    // Update team status to in_pit
    await this.updateTeamStatus(competitorObjectId, 'in_pit');
  } else if (durationSeconds > 0) {
    // No active pit stop but we have duration - create pit entry
    await this.handlePitEntry(competitor, competitorObjectId);
    
    // Update the newly created pit stop with current duration
    const newActivePitStop = await ApexPitStop.findOne({...});
    if (newActivePitStop) {
      await ApexPitStop.updateOne(
        { _id: newActivePitStop._id },
        { pitCurrentDuration: durationSeconds }
      );
    }
  } else if (durationSeconds === 0) {
    // Duration is 0 - competitor might be exiting pit
    await this.updateTeamStatus(competitorObjectId, 'on_track');
  }
}
```

### ✅ **3. Team Status Tracking**

**Problem**: Teams didn't have status information for frontend to show if they're on track or in pit.

**Solution**: Added team status tracking with automatic updates during pit events.

#### **Updated ApexTeam Schema**
```typescript
export interface IApexTeam extends Document {
  sessionId: mongoose.Types.ObjectId;
  name: string;
  currentKartId?: mongoose.Types.ObjectId;
  drivers: string[];
  nationality: string;
  status: 'on_track' | 'in_pit' | 'active'; // ✅ Team status for frontend
  isActive: boolean;
}

const ApexTeamSchema = new Schema<IApexTeam>({
  // ... other fields
  status: { 
    type: String, 
    enum: ['on_track', 'in_pit', 'active'], 
    default: 'on_track' 
  },
  // ... other fields
});
```

#### **Team Status Update Method**
```typescript
/**
 * Update team status (on_track or in_pit)
 */
private async updateTeamStatus(competitorObjectId: mongoose.Types.ObjectId, status: 'on_track' | 'in_pit'): Promise<void> {
  try {
    // Find competitor to get team info
    const competitor = await ApexCompetitor.findById(competitorObjectId);
    if (!competitor) return;

    // Update team status
    await ApexTeam.updateOne(
      {
        sessionId: this.currentSession._id,
        _id: competitor.teamId
      },
      {
        status: status,
        updatedAt: new Date()
      }
    );

    if (this.config.enableLogging) {
      console.log(`🏁 Updated team status: ${competitor.name} -> ${status}`);
    }
  } catch (error) {
    console.error('Error updating team status:', error);
  }
}
```

#### **Automatic Status Updates**
```typescript
// Pit Entry
const createdPitStop = await ApexPitStop.create(pitStopData);
await this.updateTeamStatus(competitorObjectId, 'in_pit'); // ✅ Set to in_pit

// Pit Exit
await ApexPitStop.updateOne({ _id: activePitStop._id }, { ... });
await this.updateTeamStatus(competitorObjectId, 'on_track'); // ✅ Set to on_track

// otr Duration Update
if (activePitStop) {
  await this.updateTeamStatus(competitorObjectId, 'in_pit'); // ✅ Confirm in_pit
} else if (durationSeconds === 0) {
  await this.updateTeamStatus(competitorObjectId, 'on_track'); // ✅ Set to on_track
}
```

## Expected Behavior

### **Race Time Tracking**
```
⏱️ Race duration set: 01:30:00 (5400000ms)
⏱️ Race time left: 01:29:45 (5385000ms)
⏱️ Race time left: 01:29:30 (5370000ms)
```

**Pit Stop with Race Time**:
```javascript
{
  pitInTime: "2024-11-09T15:30:45.000Z",
  raceTimeAtPitIn: 900, // 15 minutes into race
  pitOutTime: "2024-11-09T15:31:15.000Z",
  raceTimeAtPitOut: 930, // 15.5 minutes into race
  pitDuration: 30
}
```

### **Pit Duration from otr**
```
r17788c14|otr|01.  → pitCurrentDuration: 1
r17788c14|otr|05.  → pitCurrentDuration: 5
r17788c14|otr|10.  → pitCurrentDuration: 10
r17788c14|otr|00.  → pitCurrentDuration: 0 (pit exit)
```

### **Team Status Updates**
```javascript
// Team enters pit
{
  _id: ObjectId("..."),
  name: "VANHAT KOIRAT",
  status: "in_pit", // ✅ Updated automatically
  isActive: true
}

// Team exits pit
{
  _id: ObjectId("..."),
  name: "VANHAT KOIRAT", 
  status: "on_track", // ✅ Updated automatically
  isActive: true
}
```

## Frontend Usage

### **Team Status Display**
```typescript
// Frontend can now show team status
const teams = await fetch('/api/apex/sessions/:sessionId/teams');
teams.forEach(team => {
  if (team.status === 'in_pit') {
    // Show pit icon
  } else if (team.status === 'on_track') {
    // Show racing icon
  }
});
```

### **Real-time Pit Duration**
```typescript
// Frontend can show live pit duration
const activePitStops = await fetch('/api/apex/sessions/:sessionId/pitstops?active=true');
activePitStops.forEach(pit => {
  console.log(`${pit.competitorName}: ${pit.pitCurrentDuration}s in pit`);
});
```

### **Race Time Context**
```typescript
// Frontend can show race time context for pit stops
const pitStops = await fetch('/api/apex/sessions/:sessionId/pitstops');
pitStops.forEach(pit => {
  const raceTimeIn = formatTime(pit.raceTimeAtPitIn); // "15:00"
  const raceTimeOut = formatTime(pit.raceTimeAtPitOut); // "15:30"
  console.log(`Pit stop at ${raceTimeIn} - ${raceTimeOut}`);
});
```

## Benefits

### ✅ **Accurate Race Time**
- Race time calculated from actual race data (`dyn1`)
- Proper race duration tracking
- Accurate pit stop timing context

### ✅ **Real-time Pit Tracking**
- Live pit duration updates from `otr` field
- Automatic pit entry creation when duration appears
- Proper pit exit detection

### ✅ **Frontend-Ready Status**
- Team status automatically maintained
- Easy frontend integration for status display
- Real-time status updates during pit events

### ✅ **Comprehensive Logging**
- Race time tracking logged
- Team status changes logged
- Pit duration updates logged

The system now provides accurate race time tracking, real-time pit duration updates, and team status information that the frontend can use to display comprehensive race information!
