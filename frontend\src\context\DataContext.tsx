import React, { createContext, useState, useContext, useEffect } from 'react';
import apiService from '../services/apiService';

// Define types
interface Session {
  _id: string;
  title1: string;
  title2: string;
  track: string;
  raceId: string;
  isActive: boolean;
  createdAt: string;
}

interface Team {
  _id: string;
  teamId: string;
  name: string;
  kartNumber: number;
  nationality: string;
  status: string;
  sessionId: string;
}

// Define context type
interface DataContextType {
  // Apex database toggle
  useApexDatabase: boolean;
  setUseApexDatabase: (use: boolean) => void;
  
  // Session selection
  selectedSessionId: string | null;
  setSelectedSessionId: (sessionId: string | null) => void;
  availableSessions: Session[];
  selectedSession: Session | null;
  
  // Team selection (for race strategy)
  selectedTeamId: string | null;
  setSelectedTeamId: (teamId: string | null) => void;
  availableTeams: Team[];
  selectedTeam: Team | null;
  
  // Loading states
  isLoadingSessions: boolean;
  isLoadingTeams: boolean;
  
  // Methods
  refreshSessions: () => Promise<void>;
  refreshTeams: () => Promise<void>;
  
  // Error handling
  error: string | null;
  clearError: () => void;
}

// Create context with default values
const DataContext = createContext<DataContextType>({
  useApexDatabase: false,
  setUseApexDatabase: () => {},
  selectedSessionId: null,
  setSelectedSessionId: () => {},
  availableSessions: [],
  selectedSession: null,
  selectedTeamId: null,
  setSelectedTeamId: () => {},
  availableTeams: [],
  selectedTeam: null,
  isLoadingSessions: false,
  isLoadingTeams: false,
  refreshSessions: async () => {},
  refreshTeams: async () => {},
  error: null,
  clearError: () => {},
});

// Custom hook to use data context
export const useDataContext = () => useContext(DataContext);

// Provider component
export const DataProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  // Apex database toggle (loaded from user preferences)
  const [useApexDatabase, setUseApexDatabase] = useState<boolean>(false);

  // Session state (loaded from user preferences)
  const [selectedSessionId, setSelectedSessionId] = useState<string | null>(null);
  const [availableSessions, setAvailableSessions] = useState<Session[]>([]);
  const [isLoadingSessions, setIsLoadingSessions] = useState(false);

  // Team state (loaded from user preferences)
  const [selectedTeamId, setSelectedTeamId] = useState<string | null>(null);
  const [availableTeams, setAvailableTeams] = useState<Team[]>([]);
  const [isLoadingTeams, setIsLoadingTeams] = useState(false);

  // Error state
  const [error, setError] = useState<string | null>(null);

  // Load user preferences on mount
  useEffect(() => {
    loadUserPreferences();
  }, []);

  // Load preferences from database
  const loadUserPreferences = async () => {
    try {
      const token = localStorage.getItem('token');
      if (!token) return; // Not logged in

      const response = await apiService.auth.getCurrentUser();
      const preferences = response.user.preferences;

      if (preferences) {
        setUseApexDatabase(preferences.useApexDatabase || false);
        setSelectedSessionId(preferences.selectedSessionId || null);
        setSelectedTeamId(preferences.selectedTeamId || null);
        console.log('📊 Loaded user preferences:', preferences);
      }
    } catch (error) {
      console.error('Error loading user preferences:', error);
      // Don't set error state for preferences loading failure
    }
  };

  // Save preferences to database
  const saveUserPreferences = async (newPreferences: {
    useApexDatabase: boolean;
    selectedSessionId: string | null;
    selectedTeamId: string | null;
  }) => {
    try {
      const token = localStorage.getItem('token');
      if (!token) return; // Not logged in

      await apiService.auth.updatePreferences(newPreferences);
      console.log('💾 Saved user preferences:', newPreferences);
    } catch (error) {
      console.error('Error saving user preferences:', error);
      // Don't throw error, just log it
    }
  };

  // Derived state
  const selectedSession = availableSessions.find(s => s._id === selectedSessionId) || null;
  const selectedTeam = availableTeams.find(t => t._id === selectedTeamId) || null;

  // Refresh sessions
  const refreshSessions = async () => {
    if (!useApexDatabase) return;

    setIsLoadingSessions(true);
    setError(null);

    try {
      const sessions = await apiService.apex.getSessions();
      setAvailableSessions(sessions || []);

      // If no session is selected, select the most recent one
      if (!selectedSessionId && sessions && sessions.length > 0) {
        setSelectedSessionId(sessions[0]._id);
      }
    } catch (err: unknown) {
      console.error('Error fetching sessions:', err);
      setError('Failed to load sessions');
    } finally {
      setIsLoadingSessions(false);
    }
  };

  // Refresh teams for selected session
  const refreshTeams = async () => {
    if (!useApexDatabase || !selectedSessionId) {
      setAvailableTeams([]);
      return;
    }

    setIsLoadingTeams(true);
    setError(null);

    try {
      const teams = await apiService.apex.getTeams(selectedSessionId);
      setAvailableTeams(teams || []);

      // Clear selected team if it's not in the new list
      if (selectedTeamId && teams) {
        const teamExists = teams.some((team: Team) => team._id === selectedTeamId);
        if (!teamExists) {
          setSelectedTeamId(null);
        }
      }
    } catch (err: unknown) {
      console.error('Error fetching teams:', err);
      setError('Failed to load teams');
      setAvailableTeams([]);
    } finally {
      setIsLoadingTeams(false);
    }
  };

  // Load sessions when apex database is enabled
  useEffect(() => {
    if (useApexDatabase) {
      refreshSessions();
    } else {
      setAvailableSessions([]);
      setSelectedSessionId(null);
    }
  }, [useApexDatabase]);

  // Load teams when session changes
  useEffect(() => {
    refreshTeams();
  }, [selectedSessionId, useApexDatabase]);

  // Clear error
  const clearError = () => {
    setError(null);
  };

  // Handle apex database toggle
  const handleSetUseApexDatabase = (use: boolean) => {
    setUseApexDatabase(use);
    const newSessionId = use ? selectedSessionId : null;
    const newTeamId = use ? selectedTeamId : null;

    if (!use) {
      // Clear selections when disabling apex database
      setSelectedSessionId(null);
      setSelectedTeamId(null);
      setAvailableSessions([]);
      setAvailableTeams([]);
    }

    // Save preferences to database
    saveUserPreferences({
      useApexDatabase: use,
      selectedSessionId: newSessionId,
      selectedTeamId: newTeamId,
    });
  };

  // Handle session selection
  const handleSetSelectedSessionId = (sessionId: string | null) => {
    setSelectedSessionId(sessionId);
    // Clear team selection when session changes
    setSelectedTeamId(null);

    // Save preferences to database
    saveUserPreferences({
      useApexDatabase,
      selectedSessionId: sessionId,
      selectedTeamId: null,
    });
  };

  // Handle team selection
  const handleSetSelectedTeamId = (teamId: string | null) => {
    setSelectedTeamId(teamId);

    // Save preferences to database
    saveUserPreferences({
      useApexDatabase,
      selectedSessionId,
      selectedTeamId: teamId,
    });
  };

  return (
    <DataContext.Provider
      value={{
        useApexDatabase,
        setUseApexDatabase: handleSetUseApexDatabase,
        selectedSessionId,
        setSelectedSessionId: handleSetSelectedSessionId,
        availableSessions,
        selectedSession,
        selectedTeamId,
        setSelectedTeamId: handleSetSelectedTeamId,
        availableTeams,
        selectedTeam,
        isLoadingSessions,
        isLoadingTeams,
        refreshSessions,
        refreshTeams,
        error,
        clearError,
      }}
    >
      {children}
    </DataContext.Provider>
  );
};
