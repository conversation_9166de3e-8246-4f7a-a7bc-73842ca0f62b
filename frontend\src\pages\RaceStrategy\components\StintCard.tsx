import React, { useState } from 'react';
import {
  IonCard,
  IonIcon,
  IonBadge,
  IonToast
} from '@ionic/react';
import {
  timeOutline,
  timerOutline,
  speedometerOutline,
  trophyOutline,
} from 'ionicons/icons';
import { StintInfo } from '../types';
import { displaySecondsAsHHMMSS, displaySecondsAsMMSS } from '../utils/timeFormatters';
import StintDetailsModal from './StintDetailsModal';
import './StintCard.css';

interface StintCardProps {
  stint: StintInfo;
  isActive: boolean;
  isDone: boolean;
  isLastStint: boolean;
  isExtraStint: boolean;
  onStintUpdated: (stint: StintInfo) => Promise<void>;
  totalRaceTime: number;
  stints?: StintInfo[];
}

const StintCard: React.FC<StintCardProps> = ({
  stint,
  isActive,
  isDone,
  isLastStint,
  isExtraStint,
  onStintUpdated,
  totalRaceTime,
  stints = []
}) => {
  const [showModal, setShowModal] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string>('');
  const [showErrorToast, setShowErrorToast] = useState(false);

  const formatTime = (seconds: number) => {
    return displaySecondsAsHHMMSS(seconds);
  };

  // Check if stint is a bridge/transition stint
  const isBridgeStint = (): boolean => {
    // Find the first extra stint number
    const firstExtraStintNumber = stints?.find(s => s.isExtraStint)?.stintNumber || Number.MAX_SAFE_INTEGER;

    // A bridge stint is the one right before the first extra stint
    return !stint.isExtraStint && stint.stintNumber === firstExtraStintNumber - 1;
  };

  return (
    <>
      <IonCard
        className={`stint-card-compact 
          ${isActive ? 'active-stint' : ''} 
          ${(stint.isCompleted || isDone) ? 'completed-stint' : ''} 
          ${isExtraStint ? 'extra-stint' : ''} 
          ${isBridgeStint() ? 'bridge-stint' : ''}`}
        onClick={() => setShowModal(true)}
      >
        <div className="stint-card-header-compact">
          <div className="stint-number-compact">
            <span>Stint {stint.stintNumber}</span>
            {(stint.isCompleted || isDone) && (
              <IonBadge color="success" className="stint-status-badge-compact">
                DONE
              </IonBadge>
            )}
          </div>
        </div>

        <div className="stint-card-content-compact">
          <div className="stint-time-info-compact">
            <div className="time-item-compact">
              <IonIcon icon={timeOutline} />
              <span>{formatTime(stint.startTime)}</span>
            </div>

            <div className="time-separator-compact">→</div>

            <div className="time-item-compact">
              <IonIcon icon={timeOutline} />
              <span>{formatTime(stint.endTime)}</span>
            </div>
          </div>

          <div className="stint-stats-compact">
            {stint.actualAvgLapTime && (
              <div className="stat-item-compact">
                <IonIcon icon={speedometerOutline} />
                <span>{displaySecondsAsMMSS(stint.actualAvgLapTime)}</span>
              </div>
            )}

            {stint.bestLapTime && (
              <div className="stat-item-compact">
                <IonIcon icon={trophyOutline} />
                <span>{displaySecondsAsMMSS(stint.bestLapTime)}</span>
              </div>
            )}
          </div>

          {/* Only show pit time for non-last stints */}
          {
            <div className="pit-time-compact">
              <div className="pit-time-content">
                <div className="pit-time-row">
                  <IonIcon icon={timeOutline} className="pit-icon" />
                  <span>Duration: <span style={{ fontWeight: 'bold' }}>{displaySecondsAsMMSS(stint.duration)}</span></span>
                </div>
                {!isLastStint && (
                  <div className="pit-time-row">
                    <IonIcon icon={timerOutline} className="pit-icon" />
                    <span>Pit in: <span style={{ fontWeight: 'bold' }}>{displaySecondsAsHHMMSS(Math.max(0, totalRaceTime - (stint.endTime || 0)))}</span></span>
                  </div>
                )}
              </div>
            </div>
          }
        </div>
      </IonCard>

      {/* Use the new StintDetailsModal component */}
      <StintDetailsModal
        isOpen={showModal}
        onClose={() => setShowModal(false)}
        stint={stint}
        isActive={isActive}
        isDone={isDone}
        isLastStint={isLastStint}
        isExtraStint={isExtraStint}
        stints={stints}
        onStintUpdated={onStintUpdated}
        setErrorMessage={setErrorMessage}
        setShowErrorToast={setShowErrorToast}
      />

      {/* Error Toast */}
      <IonToast
        isOpen={showErrorToast}
        onDidDismiss={() => setShowErrorToast(false)}
        message={errorMessage}
        duration={3000}
        color="danger"
        position="bottom"
      />
    </>
  );
};

export default StintCard;

