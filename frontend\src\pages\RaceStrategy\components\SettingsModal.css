/* Settings Modal Styles */
.settings-modal-content {
  --background: var(--ion-color-light);
}

/* Strategy Info Card */
.strategy-info-card {
  margin-bottom: 24px;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

.strategy-info-card ion-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background: linear-gradient(to right, rgba(var(--ion-color-primary-rgb), 0.05), rgba(var(--ion-color-primary-rgb), 0.15));
}

.strategy-info-card ion-card-title {
  font-size: 1.3rem;
  font-weight: 600;
  margin: 0;
}

.race-status-badge {
  padding: 6px 10px;
  border-radius: 12px;
  font-weight: 500;
  font-size: 0.7rem;
}

.strategy-info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid rgba(var(--ion-color-medium-rgb), 0.1);
}

.strategy-info-item:last-child {
  border-bottom: none;
}

/* No Strategy Message */
.no-strategy-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 32px 16px;
  margin-bottom: 24px;
  background-color: rgba(var(--ion-color-medium-rgb), 0.1);
  border-radius: 12px;
  text-align: center;
}

.no-strategy-message ion-icon {
  font-size: 48px;
  color: var(--ion-color-medium);
  margin-bottom: 16px;
}

/* Action List */
.settings-action-list {
  border-radius: 12px;
  overflow: hidden;
  margin-bottom: 24px;
  background: var(--ion-color-light);
}

.settings-action-item {
  --padding-start: 16px;
  --padding-end: 16px;
  --padding-top: 16px;
  --padding-bottom: 16px;
  --background: var(--ion-color-light);
  --background-activated: rgba(var(--ion-color-primary-rgb), 0.1);
  --background-hover: rgba(var(--ion-color-primary-rgb), 0.05);
  --ripple-color: rgba(var(--ion-color-primary-rgb), 0.1);
  transition: background-color 0.2s;
}

.settings-action-item ion-icon {
  font-size: 24px;
  margin-right: 16px;
}

.settings-action-item h2 {
  font-size: 1.1rem;
  font-weight: 500;
  margin: 0 0 4px 0;
}

.settings-action-item p {
  font-size: 0.85rem;
  color: var(--ion-color-medium);
  margin: 0;
}

.settings-action-item.disabled-item {
  opacity: 0.7;
}

/* Help Text */
.settings-help-text {
  display: flex;
  align-items: flex-start;
  padding: 16px;
  background-color: rgba(var(--ion-color-warning-rgb), 0.1);
  border-radius: 12px;
  border-left: 4px solid var(--ion-color-warning);
}

.settings-help-text ion-icon {
  font-size: 24px;
  color: var(--ion-color-warning);
  margin-right: 12px;
  flex-shrink: 0;
}

.settings-help-text p {
  margin: 0;
  font-size: 0.9rem;
  color: var(--ion-color-medium);
  line-height: 1.4;
}

/* Ripple Effect */
.ripple-parent {
  position: relative;
  overflow: hidden;
}