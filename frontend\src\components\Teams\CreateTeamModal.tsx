// src/components/Teams/CreateTeamModal.tsx
import React, { useState } from 'react';
import {
    IonModal,
    IonHeader,
    IonToolbar,
    IonTitle,
    IonButtons,
    IonButton,
    IonIcon,
    IonContent,
    IonList,
    IonItem,
    IonLabel,
    IonInput,
    IonNote,
    IonSpinner,
} from '@ionic/react';
import { close } from 'ionicons/icons';
import { Team } from '../../types'; // Adjust path if needed

interface CreateTeamModalProps {
    isOpen: boolean;
    onDidDismiss: () => void;
    onCreateTeam: (name: string, number?: number) => Promise<Team | null>; // Returns created team or null on error
    isMutating: boolean;
}

export const CreateTeamModal: React.FC<CreateTeamModalProps> = ({
    isOpen,
    onDidDismiss,
    onCreateTeam,
    isMutating,
}) => {
    const [newTeamName, setNewTeamName] = useState<string>('');
    const [newTeamNumber, setNewTeamNumber] = useState<string>('');

    const handleInternalCreate = async () => {
        const trimmedNumberStr = newTeamNumber.trim();
        const teamNum = trimmedNumberStr ? parseInt(trimmedNumberStr, 10) : undefined;

        // Basic validation (more specific validation can be added in onCreateTeam)
        if (!newTeamName.trim()) {
            // Consider showing a local validation message or rely on parent's toast
            return;
        }
        if (trimmedNumberStr && (isNaN(teamNum as number) || (teamNum as number) <= 0)) {
            // Rely on parent's toast for detailed errors
            return;
        }

        const createdTeam = await onCreateTeam(newTeamName.trim(), teamNum);
        if (createdTeam) {
            // Reset form on success before closing
            setNewTeamName('');
            setNewTeamNumber('');
            onDidDismiss(); // Close modal via dismiss
        }
        // Error handling is done by the parent via toast
    };

    const handleDismiss = () => {
        // Reset form when dismissing without saving
        setNewTeamName('');
        setNewTeamNumber('');
        onDidDismiss();
    };

    return (
        <IonModal isOpen={isOpen} onDidDismiss={handleDismiss}>
            <IonHeader>
                <IonToolbar>
                    <IonTitle>Create New Team</IonTitle>
                    <IonButtons slot="end">
                        <IonButton onClick={handleDismiss} disabled={isMutating}>
                            <IonIcon slot="icon-only" icon={close} />
                        </IonButton>
                    </IonButtons>
                </IonToolbar>
            </IonHeader>
            <IonContent className="ion-padding">
                <IonList lines="full">
                    <IonItem>
                        <IonLabel position="stacked">Team Name *</IonLabel>
                        <IonInput
                            value={newTeamName}
                            onIonInput={e => setNewTeamName(e.detail.value ?? '')}
                            placeholder="Enter team name"
                            required
                            disabled={isMutating}
                            clearInput
                        />
                    </IonItem>
                    <IonItem>
                        <IonLabel position="stacked">Team Number (Optional)</IonLabel>
                        <IonInput
                            type="number"
                            value={newTeamNumber}
                            onIonInput={e => setNewTeamNumber(e.detail.value ?? '')}
                            placeholder="Assigns/Creates Kart #"
                            min="1"
                            disabled={isMutating}
                            clearInput
                        />
                        <IonNote slot="helper">If provided, attempts to create/assign Kart with this number.</IonNote>
                    </IonItem>
                </IonList>
                <IonButton
                    expand="block"
                    onClick={handleInternalCreate}
                    disabled={isMutating || !newTeamName.trim()}
                    style={{ marginTop: '1.5rem' }}
                >
                    {isMutating ? <IonSpinner name="crescent" /> : 'Create Team'}
                </IonButton>
            </IonContent>
        </IonModal>
    );
};
