"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.parseGridData = void 0;
const parseGridData = (grid) => {
    const id = Date.now(); // Static ID, could be dynamic depending on your logic
    const drivers = {}; // Driver data storage
    const header = { types: {}, labels: {} };
    // Regex to capture each row with a `data-id` attribute
    const rowsRegex = /<tr[^>]*data-id="(r\d+)"[^>]*>(.*?)<\/tr>/gs;
    let rowMatch;
    while ((rowMatch = rowsRegex.exec(grid)) !== null) {
        const rowId = rowMatch[1]; // Extract the row ID (e.g., "r0", "r3")
        let driver = null; // Initialize driver only if needed
        // Regex to match all <td> or nested <div>/<p> with data-id attribute inside the row
        const cellRegex = /<td[^>]*data-id="(r?\d*c\d+|c\d+)"[^>]*>(.*?)<\/td>|<(?:div|p)[^>]*data-id="(r?\d*c\d+|c\d+)"[^>]*>(.*?)<\/(?:div|p)>/gs;
        let cellMatch;
        // Process each cell in the row
        while ((cellMatch = cellRegex.exec(rowMatch[2])) !== null) {
            const cellId = cellMatch[1] || cellMatch[3]; // Either <td> or nested <div>/<p> ID
            const cleanCellId = cellId.replace(/^r?\d*/, ""); // Remove row number part of the ID
            const cellContent = (cellMatch[2] || cellMatch[4] || "")
                .replace(/<[^>]+>/g, "")
                .trim(); // Clean cell content by stripping HTML tags
            if (rowId === "r0") {
                // For the header row (r0), we need to capture the labels and types
                const headerTypeMatch = /data-type="([^"]*)"/.exec(cellMatch[0]);
                let headerType = headerTypeMatch ? headerTypeMatch[1] : "";
                // If the data-type is empty, we will assign a custom headerType based on cell content
                if (!headerType) {
                    // Dynamically generate header types based on the content of the cell
                    headerType = cellContent.toLowerCase().replace(/\s+/g, ""); // Clean label to form the type
                }
                if (headerType) {
                    header.types[cleanCellId] = headerType; // Store the type for the column
                }
                header.labels[cleanCellId] = cellContent; // Store the label for the column
            }
            else {
                // Initialize the driver if it hasn't been initialized already
                if (!driver) {
                    driver = {
                        gridId: id,
                        grp: { type: "", value: "" },
                        sta: { type: "", value: "" },
                        rk: { type: "", value: "" },
                        no: { type: "", value: "" },
                        dr: { type: "", value: "" },
                        s1: { type: "", value: "" },
                        s2: { type: "", value: "" },
                        s3: { type: "", value: "" },
                        sp1: { type: "", value: "" },
                        llp: { type: "", value: "" },
                        blp: { type: "", value: "" },
                        gap: { type: "", value: "" },
                        tlp: { type: "", value: "" },
                        nat: { type: "", value: "" },
                        otr: { type: "", value: "" },
                        Laps: [],
                        Pits: [],
                        pastKartIds: [],
                        currentKartId: 0, // Added to store kart ID
                    };
                }
                // Map cell data to the driver object based on the header types
                const headerType = header.types[cleanCellId];
                if (headerType) {
                    driver[headerType] = { type: headerType, value: cellContent };
                }
                else {
                    // If headerType is missing, create a new type name based on cell content
                    const dynamicType = cellContent.toLowerCase().replace(/\s+/g, ""); // Clean content for type
                    driver[dynamicType] = { type: dynamicType, value: cellContent };
                }
            }
        }
        // Only add the driver if it's not the header row (rowId !== "r0") and driver is initialized
        if (rowId !== "r0" && driver) {
            drivers[rowId] = driver;
        }
    }
    // Return parsed drivers and header labels along with the static ID (0 in this case)
    const header_labels = header.labels;
    const header_types = header.types;
    console.log(header);
    return { drivers, header_labels, header_types, id };
};
exports.parseGridData = parseGridData;
//# sourceMappingURL=parseGrid.js.map