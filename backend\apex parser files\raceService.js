appApex.service("raceService",
    ["brightnessService",
        "$filter",
        function (brightnessService, $filter) {
            factory = {};

            factory.raceLayout = null;

            factory.raceTitle = "kRace";

            factory.lightGreen = false;

            factory.driverModel = function (dto) {

                var model = {
                    "position": dto ? dto.position : "",
                    "gridPosition": dto? dto.gridPosition: "", // da popolare con il parametro position preso al caricamento
                    "isPositionUp": false,
                    "isPositionDown": false,
                    "name": dto ? dto.name : "",
                    "kart": dto ? dto.kart : "",
                    "s1": dto ? dto.s1 : "",
                    "s2": dto ? dto.s2 : "",
                    "s1Old": dto ? dto.s1 : "",
                    "s2Old": dto ? dto.s2 : "",
                    "s1BestTime": dto ? dto.s1BestTime : "",
                    "s2BestTime": dto ? dto.s2BestTime : "",
                    "s1PersonalBestTime": dto ? dto.s1PersonalBestTime : "",
                    "s2PersonalBestTime": dto ? dto.s2PersonalBestTime : "",
                    "lastTime": dto ? dto.lastTime : "",
                    "lastTimeLabel": dto ? dto.lastTimeLabel : "",
                    "bestTime": dto ? dto.bestTime : "",
                    "bestTimeLabel": dto ? dto.bestTimeLabel : "",
                    "totalTime": dto ? dto.totalTime : parseFloat(0),
                    "isIntervalReduced": false,
                    "isGapReduced": false,
                    "gap": dto ? dto.gap : "",
                    "interval": dto ? dto.interval : "",
                    "lap": dto ? dto.lap : "",
                    "pits": dto ? dto.pits : "",
                    "isBestTime": false,
                    "isPersonalBestTime": false,
                    "bestLapAnimation": "",
                    "dataId": dto ? dto.dataId : "", //r1 oppure r2
                    "isRaceEnded": dto ? dto.isRaceEnded : false,
                    "isLocked": false, // bloccato in grigla virtuale
                    "isPositionChange": false, //la posizione è cambiata rispetto al giro precedente,
                    "lastLaps": [],//Indica gira,
                    "isInPit": false,
                    "isRun": false, //bandiera verde,
                    "timeOnTrack": "",
                    "showOutPit": false
                }

                return model;

            }

            factory.lastLapModel = function (label, time) {
                var model = {
                    "lastLapLabel": label,
                    "lastLapTime": time
                }

                return model;
            }

            factory.updateLastLaps = function (driverModel) {
                if (!driverModel)
                    return;

                var lastLaps = driverModel.lastLaps;
                if (lastLaps.length == 4) {
                    lastLaps.shift();
                }

                var lastLapModel = factory.lastLapModel(driverModel.lastTimeLabel, driverModel.lastTime);
                lastLaps.push(lastLapModel);

                driverModel.lastLaps = lastLaps;
            }

            factory.getDriverModelFromGrid = function (dataId, grid) {
                if (!dataId || !grid)
                    return;

                var result = null;

                angular.forEach(grid, function (item) {
                    if (item.dataId == dataId)
                        result = item;
                });

                return result;
            }

            factory.buildDataId = function (item) {
                if (!item)
                    return "";

                // if(item.length == 2 && item.startsWith("r"))
                //     return item;

                // var subLimiter = 2;
                // if(item.length > 4 && item.startsWith("r"))
                //     subLimiter = 3;
                // var result = item.substring(0, subLimiter);
                var result = item.split("c");

                return result[0];

            }

            factory.formatLapTime = function (lapTime) {
                //1:03.400
                if (!lapTime)
                    return;

                var result = 0;

                if (lapTime.includes(":")) {
                    var split = lapTime.split(':'),
                        minutes = +split[0],
                        seconds = +split[1];
                    result = result + parseFloat((minutes * 60 + seconds).toFixed(3));
                }
                else {
                    result = result + parseFloat(parseFloat(lapTime).toFixed(3));
                }
                return result;
            }

            factory.isInRaceLayout = function (item, raceLayoutKey) {
                if (!item)
                    return "";

                if (item.length == 2 && item == raceLayoutKey)
                    return true;

                // var subLimiter = 2;                
                // var upLimiter = 4;
                //if(item.startsWith("r")){

                // if(item.length == 5){//r12c4
                //     subLimiter = 3;
                //     upLimiter = 5;
                // }
                // else if(item.length == 6){//r12c13
                //     subLimiter = 3;
                //     upLimiter = 6;
                // }
                //}


                //var col = item.substring(subLimiter, upLimiter).toLowerCase();
                var row = item.substr(item.indexOf('r'), item.indexOf('c'))
                var col = item.substr(item.indexOf('c'), item.length)
                // console.log("Row: "+ row)
                // console.log("Col: "+ col)

                if (col == raceLayoutKey)
                    return true;
                else
                    return false;
            }

            //Typeinfo = Ti, rowNameInfo == r2c5
            factory.isPosition = function (typeInfo, rowNameInfo) {
                if (!rowNameInfo) {
                    //console.log("Errore durante la lettura della posizione")
                    return false;
                }

                typeInfo = typeInfo.toLowerCase();
            
                if(typeInfo == "#")
                    return true;

                if ((typeInfo == "") && factory.isInRaceLayout(rowNameInfo, factory.raceLayout.rk)) {
                    return true;
                }
                else {
                    return false;
                }
            }
            factory.isGridPosition = function (typeInfo, rowNameInfo) {
                if (!rowNameInfo) {
                    //console.log("Errore durante la lettura della posizione")
                    return false;
                }

                typeInfo = typeInfo.toLowerCase();

                if ((typeInfo == "rk") && factory.isInRaceLayout(rowNameInfo, factory.raceLayout.rk)) {
                    return true;
                }
                else {
                    return false;
                }
            }
            factory.isKart = function (typeInfo, rowNameInfo) {
                if (!typeInfo || !rowNameInfo) {
                    //console.log("Errore durante la lettura del numero del kart")
                    return false;
                }

                typeInfo = typeInfo.toLowerCase();

                if (typeInfo == "no" && factory.isInRaceLayout(rowNameInfo, factory.raceLayout.no)) {
                    return true;
                }
                else {
                    return false;
                }
            }
            factory.isName = function (typeInfo, rowNameInfo) {
                if (!typeInfo || !rowNameInfo) {
                    //console.log("Errore durante la lettura del nome del pilota")
                    return false;
                }

                typeInfo = typeInfo.toLowerCase();

                if (typeInfo == "dr" && factory.isInRaceLayout(rowNameInfo, factory.raceLayout.dr)) {
                    return true;
                }
                else {
                    return false;
                }
            }

            factory.isS1 = function (typeInfo, rowNameInfo) {
                if (!typeInfo || !rowNameInfo) {
                    //console.log("Errore durante la lettura dell'intertempo s1")
                    return false;
                }

                typeInfo = typeInfo.toLowerCase();

                if ((typeInfo == "ti" || typeInfo == "tn" || typeInfo == "tb" || typeInfo == "ib") && factory.isInRaceLayout(rowNameInfo, factory.raceLayout.s1)) {
                    return true;
                }
                else {
                    return false;
                }
            }

            factory.isS1BestTime = function (typeInfo, rowNameInfo) {
                if (!typeInfo || !rowNameInfo) {
                    //console.log("Errore durante la lettura dell'intertempo s1")
                    return false;
                }

                typeInfo = typeInfo.toLowerCase();

                //if((typeInfo == "ib" || typeInfo == "tb") && factory.isInRaceLayout(rowNameInfo, factory.raceLayout.s1)){
                if ((typeInfo == "tb") && factory.isInRaceLayout(rowNameInfo, factory.raceLayout.s1)) {
                    return true;
                }
                else {
                    return false;
                }
            }
            factory.isS1PersonalBestTime = function (typeInfo, rowNameInfo) {
                if (!typeInfo || !rowNameInfo) {
                    //console.log("Errore durante la lettura dell'intertempo s1")
                    return false;
                }

                typeInfo = typeInfo.toLowerCase();

                if (typeInfo == "ti" && factory.isInRaceLayout(rowNameInfo, factory.raceLayout.s1)) {
                    return true;
                }
                else {
                    return false;
                }
            }

            factory.isS2 = function (typeInfo, rowNameInfo) {
                if (!typeInfo || !rowNameInfo) {
                    //console.log("Errore durante la lettura dell'intertempo s2")
                    return false;
                }

                typeInfo = typeInfo.toLowerCase();

                if ((typeInfo == "ti" || typeInfo == "tn" || typeInfo == "tb" || typeInfo == "ib") && factory.isInRaceLayout(rowNameInfo, factory.raceLayout.s2)) {
                    return true;
                }
                else {
                    return false;
                }
            }
            factory.isS2BestTime = function (typeInfo, rowNameInfo) {
                if (!typeInfo || !rowNameInfo) {
                    //console.log("Errore durante la lettura dell'intertempo s1")
                    return false;
                }

                typeInfo = typeInfo.toLowerCase();

                //if((typeInfo == "ib" || typeInfo == "tb") && factory.isInRaceLayout(rowNameInfo, factory.raceLayout.s2)){
                if ((typeInfo == "tb") && factory.isInRaceLayout(rowNameInfo, factory.raceLayout.s2)) {
                    return true;
                }
                else {
                    return false;
                }
            }
            factory.isS2PersonalBestTime = function (typeInfo, rowNameInfo) {
                if (!typeInfo || !rowNameInfo) {
                    //console.log("Errore durante la lettura dell'intertempo s1")
                    return false;
                }

                typeInfo = typeInfo.toLowerCase();

                if (typeInfo == "ti" && factory.isInRaceLayout(rowNameInfo, factory.raceLayout.s2)) {
                    return true;
                }
                else {
                    return false;
                }
            }
            factory.isLastTime = function (typeInfo, rowNameInfo) {
                if (!typeInfo || !rowNameInfo) {
                    //console.log("Errore durante la lettura dell'ultimo giro")
                    return false;
                }

                typeInfo = typeInfo.toLowerCase();

                if ((typeInfo == "ti" || typeInfo == "tn" || typeInfo == "tb") && factory.isInRaceLayout(rowNameInfo, factory.raceLayout.llp)) {
                    return true;
                }
                else {
                    return false;
                }
            }
            factory.isBestTime = function (typeInfo, rowNameInfo) {
                if (!typeInfo || !rowNameInfo) {
                    //console.log("Errore durante la lettura del miglior giro")
                    return false;
                }

                typeInfo = typeInfo.toLowerCase();

                if ((typeInfo == "ti") && factory.isInRaceLayout(rowNameInfo, factory.raceLayout.llp)
                    ||
                    (typeInfo == "ib") && factory.isInRaceLayout(rowNameInfo, factory.raceLayout.blp)
                    ||
                    (typeInfo == "in") && factory.isInRaceLayout(rowNameInfo, factory.raceLayout.blp)
                ) {
                    return true;
                }
                else {
                    return false;
                }
            }
            factory.isBestTimeInRace = function (typeInfo, rowNameInfo) {
                if (!typeInfo || !rowNameInfo) {
                    //console.log("Errore durante la lettura del miglior giro in gara")
                    return false;
                }

                typeInfo = typeInfo.toLowerCase();

                if (typeInfo == "tb" && factory.isInRaceLayout(rowNameInfo, factory.raceLayout.llp)
                    ||
                    // typeInfo == "in" && factory.isInRaceLayout(rowNameInfo, factory.raceLayout.blp)
                    // ||
                    typeInfo == "tb" && factory.isInRaceLayout(rowNameInfo, factory.raceLayout.blp)) {
                    return true;
                }
                else {
                    return false;

                }
            }
            factory.isInterval = function (typeInfo, rowNameInfo) {
                if (!typeInfo || !rowNameInfo) {
                    //console.log("Errore durante la lettura del gap da chi ti precede")
                    return false;
                }

                typeInfo = typeInfo.toLowerCase();

                if (typeInfo == "in" && factory.isInRaceLayout(rowNameInfo, factory.raceLayout.int)) {
                    return true;
                }
                else {
                    return false;
                }
            }
            factory.isGap = function (typeInfo, rowNameInfo) {
                if (!typeInfo || !rowNameInfo) {
                    //console.log("Errore durante la lettura del gap dalla testa della corsa")
                    return false;
                }

                typeInfo = typeInfo.toLowerCase();

                if ((typeInfo == "ib" || typeInfo == "in") && factory.isInRaceLayout(rowNameInfo, factory.raceLayout.gap)) {
                    return true;
                }
                else {
                    return false;
                }
            }
            factory.isGiro = function (typeInfo, rowNameInfo) {
                if (!typeInfo || !rowNameInfo) {
                    //console.log("Errore durante la lettura del numero giro corrente")
                    return false;
                }

                typeInfo = typeInfo.toLowerCase();

                if (typeInfo == "in" && factory.isInRaceLayout(rowNameInfo, factory.raceLayout.tlp)) {
                    return true;
                }
                else {
                    return false;
                }
            }
            factory.isPit = function (typeInfo, rowNameInfo) {
                if (!typeInfo || !rowNameInfo) {
                    //console.log("Errore durante la lettura del numero di pit effettuate")
                    return false;
                }

                typeInfo = typeInfo.toLowerCase();

                if (typeInfo == "in" && factory.isInRaceLayout(rowNameInfo, factory.raceLayout.pit)) {
                    return true;
                }
                else {
                    return false;
                }
            }

            factory.isTimeOnTrack = function (typeInfo, rowNameInfo) {
                if (!typeInfo || !rowNameInfo) {
                    //console.log("Errore durante la lettura del numero di pit effettuate")
                    return false;
                }

                typeInfo = typeInfo.toLowerCase();

                if ((typeInfo == "to" || typeInfo == "in") && factory.isInRaceLayout(rowNameInfo, factory.raceLayout.otr)) {
                    return true;
                }
                else {
                    return false;
                }
            }

            factory.isPitIn = function (typeInfo, rowNameInfo) {
                if (!typeInfo || !rowNameInfo) {
                    //console.log("Errore durante la lettura del numero di pit effettuate")
                    return false;
                }

                typeInfo = typeInfo.toLowerCase();

                if (typeInfo == "si" && factory.isInRaceLayout(rowNameInfo, factory.raceLayout.sta)) {
                    return true;
                }
            }
            factory.isPitOut = function (typeInfo, rowNameInfo) {
                if (!typeInfo || !rowNameInfo) {
                    //console.log("Errore durante la lettura del numero di pit effettuate")
                    return false;
                }

                typeInfo = typeInfo.toLowerCase();

                if (typeInfo == "so" && factory.isInRaceLayout(rowNameInfo, factory.raceLayout.sta)) { //bandiera out azzurra              
                    return true;
                }
                else if (typeInfo == "in" && factory.isInRaceLayout(rowNameInfo, factory.raceLayout.sta)) { //sr bandiera verde 
                    return true;
                }
            }
            factory.isRun = function (typeInfo, rowNameInfo) {
                if (!typeInfo || !rowNameInfo) {
                    //console.log("Errore durante la lettura del numero di pit effettuate")
                    return false;
                }

                typeInfo = typeInfo.toLowerCase();

                if (typeInfo == "sr") {
                    return true;
                }
            }
            factory.isRaceEnded = function (typeInfo, rowNameInfo, valueInfo) {
                if (!typeInfo) {
                    //console.log("Errore durante il calcolo se la gara è finita per il pilota attuale")
                    return false;
                }

                typeInfo = typeInfo.toLowerCase();

                if (typeInfo == "lf" || typeInfo == "sf") {
                    return true;
                }
                else {
                    return false;
                }
            }

            factory.isLightGreen = function (typeInfo) {
                if (!typeInfo) {
                    //console.log("Errore durante il calcolo della luce del semaforo")
                    return false;
                }

                typeInfo = typeInfo.toLowerCase();

                if (typeInfo == "lg") {
                    return true;
                }
                else if (typeInfo == "lf" || typeInfo == "lr") {
                    return false;
                }
            }

            factory.isLightRed = function (typeInfo) {
                if (!typeInfo) {
                    //console.log("Errore durante il calcolo della luce del semaforo")
                    return false;
                }

                typeInfo = typeInfo.toLowerCase();

                if (typeInfo == "lr") {
                    return true;
                }
                else if (typeInfo == "lf" || typeInfo == "lg") {
                    return false;
                }
            }

            factory.isPositionUp = function (typeInfo) {
                if (!typeInfo) {
                    //console.log("Errore durante il calcolo della luce del semaforo")
                    return false;
                }

                typeInfo = typeInfo.toLowerCase();

                if (typeInfo == "su") {
                    return true;
                }
            }

            factory.isPositionDown = function (typeInfo) {
                if (!typeInfo) {
                    //console.log("Errore durante il calcolo della luce del semaforo")
                    return false;
                }

                typeInfo = typeInfo.toLowerCase();

                if (typeInfo == "sd") {
                    return true;
                }
            }

            factory.populateDriverModel = function (driverModel, rowNameInfo, typeInfo, valueInfo, dataId, drivers) {
                if (!driverModel) {
                    //console.log("Errore durante populateDriverModel: DriverModel is null!")           
                    return;
                }

                if (dataId) {
                    driverModel.dataId = dataId;
                }

                if (factory.isRaceEnded(typeInfo, rowNameInfo, valueInfo)) {
                    driverModel.isRaceEnded = true;
                }

                if (factory.isPosition(typeInfo, rowNameInfo) && valueInfo) {
                    //Se la posizione è cambiata, aggiorno il flag
                    if (valueInfo != driverModel.position) {
                        driverModel.isPositionChange = true;
                    }
                    else {
                        driverModel.isPositionChange = false;
                    }

                    //Aggiorno la posizione
                    driverModel.position = valueInfo ? parseInt(valueInfo) : driverModel.position;
                    //driverModel.gridPosition = valueInfo ? parseInt(valueInfo) : driverModel.gridPosition;

                    if (driverModel.position == 1) {
                        driverModel.gap = "";
                        driverModel.interval = "";
                    }
                }
                else if (factory.isGridPosition(typeInfo, rowNameInfo) && valueInfo) {
                    // //Se la posizione è cambiata, aggiorno il flag
                    // if (valueInfo != driverModel.position) {
                    //     driverModel.isPositionChange = true;
                    // }
                    // else {
                    //     driverModel.isPositionChange = false;
                    // }

                    //Aggiorno la posizione di start in griglia
                    driverModel.gridPosition = valueInfo ? parseInt(valueInfo) : driverModel.gridPosition;
                    if(driverModel.isRaceEnded || factory.lightGreen)
                    driverModel.position = valueInfo ? parseInt(valueInfo) : driverModel.position;

                    // if (driverModel.position == 1) {
                    //     driverModel.gap = "";
                    //     driverModel.interval = "";
                    // }
                }
                else if (factory.isKart(typeInfo, rowNameInfo) && valueInfo) {
                    driverModel.kart = valueInfo;
                }

                else if (factory.isName(typeInfo, rowNameInfo) && valueInfo) {
                    driverModel.name = valueInfo;
                }
                else if (factory.isPitIn(typeInfo, rowNameInfo)) {
                    driverModel.isInPit = true;
                }
                else if (factory.isPitOut(typeInfo, rowNameInfo)) {
                    driverModel.isInPit = false;
                }
                else if (factory.isTimeOnTrack(typeInfo, rowNameInfo)) {
                    driverModel.timeOnTrack = valueInfo;
                }

                else if (factory.isRun(typeInfo, rowNameInfo)) {
                    driverModel.isRun = true;
                    driverModel.isInPit = false;
                    setTimeout(function () {
                        driverModel.isRun = false;
                    }, 7000)
                }

                else if (factory.isS1(typeInfo, rowNameInfo) && valueInfo) {
                    driverModel.s1Old = driverModel.s1;
                    driverModel.s1 = valueInfo;
                    driverModel.s2 = "";
                    if (factory.isS1BestTime(typeInfo, rowNameInfo)) {
                        driverModel.isS1BestTime = true
                    }
                    else {
                        driverModel.isS1BestTime = false
                    }
                    if (factory.isS1PersonalBestTime(typeInfo, rowNameInfo)) {
                        driverModel.isS1PersonalBestTime = true
                    }
                    else {
                        driverModel.isS1PersonalBestTime = false
                    }
                }

                else if (factory.isS2(typeInfo, rowNameInfo)) {
                    driverModel.s2Old = driverModel.s2;
                    driverModel.s2 = valueInfo;
                    if (factory.isS2BestTime(typeInfo, rowNameInfo)) {
                        driverModel.isS2BestTime = true
                    }
                    else {
                        driverModel.isS2BestTime = false
                    }
                    if (factory.isS2PersonalBestTime(typeInfo, rowNameInfo)) {
                        driverModel.isS2PersonalBestTime = true
                    }
                    else {
                        driverModel.isS2PersonalBestTime = false
                    }
                }
                else if (factory.isGiro(typeInfo, rowNameInfo) && valueInfo) {
                    driverModel.lap = valueInfo;
                }
                else if (factory.isLastTime(typeInfo, rowNameInfo) && valueInfo) {
                    driverModel.lastTime = factory.clearLapTime(valueInfo);
                    driverModel.lastTimeLabel = valueInfo;
                    driverModel.isPersonalBestTime = false;

                    factory.updateLastLaps(driverModel);
                    //factory.startLapLine(driverModel.lastTime, driverModel.name);
                    // driverModel.isBestTime = false;
                }

                if (factory.isBestTime(typeInfo, rowNameInfo) && valueInfo) {
                    driverModel.bestTime = factory.clearLapTime(valueInfo);
                    driverModel.bestTimeLabel = valueInfo;
                    if (driverModel.lastTime == driverModel.bestTime)
                        driverModel.isPersonalBestTime = true;
                    driverModel.bestLapAnimation = "";
                }

                if (factory.isBestTimeInRace(typeInfo, rowNameInfo) && valueInfo) {
                    var isBestTime = true;
                    angular.forEach(drivers, function (item) {
                        if (parseFloat(valueInfo) >= parseFloat(item.bestTime))
                            isBestTime = false;
                    });
                    driverModel.bestTime = factory.clearLapTime(valueInfo);
                    if (driverModel.lastTime == driverModel.bestTime)
                        driverModel.isPersonalBestTime = true;
                    driverModel.isBestTime = isBestTime;
                    driverModel.bestLapAnimation = "";
                }

                else if (factory.isInterval(typeInfo, rowNameInfo) && valueInfo) {
                    driverModel.interval = valueInfo;
                }

                else if (factory.isGap(typeInfo, rowNameInfo) && valueInfo) {
                    if (!valueInfo.startsWith("G")) {
                        driverModel.gap = valueInfo;
                        //console.log("ho aggiornato il gap da: "+ driverModel.gap + " a: "+ valueInfo)
                        //console.log("typeInfo: "+ typeInfo + " rowNameInfo: "+ rowNameInfo)
                    }

                }

                else if (factory.isPit(typeInfo, rowNameInfo)) {
                    driverModel.pits = valueInfo;
                }
                else if (factory.isPositionUp(typeInfo, rowNameInfo)) {
                    driverModel.isPositionUp = true;
                    setTimeout(function () {
                        driverModel.isPositionUp = false;
                    }, 7000)
                }
                else if (factory.isPositionDown(typeInfo, rowNameInfo)) {
                    driverModel.isPositionDown = true;
                    setTimeout(function () {
                        driverModel.isPositionDown = false;
                    }, 7000)
                }
            }

            factory.isScreenFlash = function (selectedDriver, actualDriver) {
                if (!selectedDriver || !actualDriver || selectedDriver == -1)
                    return false;

                if (selectedDriver.dataId && selectedDriver.dataId == actualDriver.dataId && actualDriver.isBestTime) {
                    brightnessService.flashScreen();
                }
            }


            factory.clearLapTime = function (lapTime) {
                if (!lapTime)
                    return lapTime;

                if (typeof lapTime == "number")
                    return lapTime

                var result = lapTime;

                if (lapTime.includes(":")) {
                    var split = lapTime.split(":");
                    var minutes = parseFloat(split[0]);
                    var seconds = parseFloat(split[1]);

                    result = parseFloat((minutes * 60 + seconds).toFixed(3));
                }
                else {
                    result = parseFloat(lapTime);
                }
                return result;
            }

            factory.clearBestTimes = function (drivers) {
                if (!drivers) {
                    return;
                }

                var bestTimes = [];

                angular.forEach(drivers, function (item) {
                    if (item.bestTime) {
                        item.bestTime = factory.clearLapTime(item.bestTime)
                        bestTimes.push(parseFloat(item.bestTime))
                    }
                });

                var bestTime = Math.min.apply(null, bestTimes);

                angular.forEach(drivers, function (item) {
                    if (parseFloat(item.bestTime) == bestTime)
                        item.isBestTime = true
                    else
                        item.isBestTime = false;
                });

            }

                     
            factory.parseMillisecondsIntoReadableTime = function(milliseconds){
                //Get hours from milliseconds
                var hours = milliseconds / (1000*60*60);
                var absoluteHours = Math.floor(hours);
                var h = absoluteHours > 9 ? absoluteHours : '0' + absoluteHours;
              
                //Get remainder from hours and convert to minutes
                var minutes = (hours - absoluteHours) * 60;
                var absoluteMinutes = Math.floor(minutes);
                var m = absoluteMinutes > 9 ? absoluteMinutes : '0' +  absoluteMinutes;
              
                //Get remainder from minutes and convert to seconds
                var seconds = (minutes - absoluteMinutes) * 60;
                var absoluteSeconds = Math.floor(seconds);
                var s = absoluteSeconds > 9 ? absoluteSeconds : '0' + absoluteSeconds;
              
              
                return h + ':' + m + ':' + s;
            }

            factory.startLapLine = function (durationPar, driverName) {
                // //id="{{raceDriver.pos}}lap-line"
                // var duration = parseFloat(durationPar) * 1000;

                // var id = "#" + driverName + "lap-line"
                // var idTime = "#" + driverName + "driver-time"
                // $(id).stop(true, true).animate();
                // $(id).css('width', '1%');
                // $(idTime).fadeOut(100).fadeIn(250);
                // //$(id).addClass("animated bounceInRight");
                // $(id).animate({ width: '100%' }, duration, function () {
                //     //$(id).fadeOut('slow');
                //     //$(id).removeClass("animated bounceInRight");
                //     $(id).css('width', '100%');

                // });
            }

            factory.calculateGaps = function (drivers) {
                if (!drivers || drivers.length == 0) {
                    return;
                }

                var driverCopy = $filter('orderBy')(drivers, ['position', 'gridPosition'])

                angular.forEach(driverCopy, function (item, index) {
                    var position = item.position;
                    if (position == 1)
                        return;
                    var isGapReduced = false;
                    var isIntervalReduced = false;

                    //calcolo se il gap dal primo si è ridotto
                    if (index > 0 && item.lastTime < driverCopy[0].lastTime) {
                        isGapReduced = true;
                        //  console.log("il gap si è ridotto")
                    }

                    if (index > 0 && item.lastTime < driverCopy[index - 1].lastTime) {
                        isIntervalReduced = true;
                    }

                    //if(item.lastTime < driverCopy[index-1].lastTime)

                    //set interval
                    //item.interval = (item.lastTime - driverCopy[index-1].lastTime).toFixed(3)

                    item.isGapReduced = isGapReduced;
                    item.isIntervalReduced = isIntervalReduced;
                });
            }


            return factory;
        }]);