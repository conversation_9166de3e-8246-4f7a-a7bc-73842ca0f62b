#!/usr/bin/env node

/**
 * Test script to verify that competitor 45405 gets created correctly
 */

const fs = require('fs');
const path = require('path');

// Mock database operations
const mockModels = {
  ApexSession: {
    create: async (data) => ({ _id: Date.now(), ...data }),
    findById: async (id) => ({ _id: id, title1: 'Test Session' })
  },
  ApexTeam: {
    find: async () => [],
    insertMany: async (teams) => teams.map((team, i) => ({ _id: `team_${i}`, ...team }))
  },
  ApexKart: {
    find: async () => [],
    insertMany: async (karts) => karts.map((kart, i) => ({ _id: `kart_${i}`, ...kart }))
  },
  ApexCompetitor: {
    find: async () => [],
    insertMany: async (competitors) => {
      console.log(`📋 Creating ${competitors.length} competitors:`);
      competitors.forEach(comp => {
        console.log(`   - ${comp.websocketId} (${comp.name}) - Kart ${comp.kartId}`);
      });
      return competitors.map((comp, i) => ({ _id: `comp_${i}`, ...comp }));
    }
  }
};

// Simple grid parser
function parseGridData(grid) {
  const id = Date.now();
  const drivers = {};
  const header = { types: {}, labels: {} };

  if (!grid) {
    return { drivers, header_labels: header.labels, header_types: header.types, id };
  }

  const rowsRegex = /<tr[^>]*data-id="(r\d+)"[^>]*>(.*?)<\/tr>/gs;
  let rowMatch;

  while ((rowMatch = rowsRegex.exec(grid)) !== null) {
    const rowId = rowMatch[1];
    let driver = null;

    const cellRegex = /<td[^>]*data-id="(r?\d*c\d+|c\d+)"[^>]*>(.*?)<\/td>|<(?:div|p)[^>]*data-id="(r?\d*c\d+|c\d+)"[^>]*>(.*?)<\/(?:div|p)>/gs;
    let cellMatch;

    while ((cellMatch = cellRegex.exec(rowMatch[2])) !== null) {
      const cellId = cellMatch[1] || cellMatch[3];
      if (!cellId) continue;

      const cleanCellId = cellId.replace(/^r?\d*/, "");
      const cellContent = (cellMatch[2] || cellMatch[4] || "")
        .replace(/<[^>]+>/g, "")
        .trim();

      if (rowId === "r0") {
        const headerTypeMatch = /data-type="([^"]*)"/.exec(cellMatch[0]);
        let headerType = headerTypeMatch ? headerTypeMatch[1] : "";

        if (!headerType) {
          headerType = cellContent.toLowerCase().replace(/\s+/g, "");
        }

        if (headerType) {
          header.types[cleanCellId] = headerType;
        }
        header.labels[cleanCellId] = cellContent;
      } else {
        if (!driver) {
          driver = {
            gridId: id,
            no: { type: "", value: "" },
            dr: { type: "", value: "" },
            nat: { type: "", value: "" }
          };
        }

        const headerType = header.types[cleanCellId];
        if (headerType) {
          driver[headerType] = { type: headerType, value: cellContent };
        }
      }
    }

    if (rowId !== "r0" && driver) {
      drivers[rowId] = driver;
    }
  }

  return { drivers, header_labels: header.labels, header_types: header.types, id };
}

// Simplified parser class
class TestApexParser {
  constructor() {
    this.currentSession = null;
    this.gridData = null;
  }

  async parseMessage(messageContent) {
    const cleanMessage = messageContent.replace(/^\[.*?\]\s*/, '');
    
    if (cleanMessage.includes('grid||')) {
      const gridMatch = cleanMessage.match(/grid\|\|([^]*?)(?=\n|$)/);
      if (gridMatch) {
        await this.handleGridMessage({ grid: { value: gridMatch[1] } });
      }
    }
  }

  async handleGridMessage(messageData) {
    console.log('📊 Processing grid message...');
    
    if (messageData.grid) {
      this.gridData = parseGridData(messageData.grid.value);
      console.log(`   Parsed ${Object.keys(this.gridData.drivers).length} drivers from grid`);

      // Debug: Show all parsed drivers
      console.log('   Parsed drivers:');
      for (const [driverId, driverData] of Object.entries(this.gridData.drivers)) {
        const competitorId = driverId.replace('r', '');
        const teamName = driverData.dr?.value || `Team ${competitorId}`;
        const kartNumber = parseInt(driverData.no?.value || '0') || 0;
        console.log(`     ${competitorId}: ${teamName} (Kart ${kartNumber})`);
      }
    }

    // Create session
    this.currentSession = await mockModels.ApexSession.create({
      title1: 'Test Session',
      title2: 'Competitor 45405 Test'
    });

    // Create entities from grid
    if (this.gridData && this.currentSession) {
      await this.createEntitiesFromGrid();
    }
  }

  async createEntitiesFromGrid() {
    const sessionId = this.currentSession._id;
    
    const teamsToCreate = [];
    const kartsToCreate = [];
    const competitorsToCreate = [];

    // First pass: Create teams and karts
    for (const [driverId, driverData] of Object.entries(this.gridData.drivers)) {
      const competitorId = driverId.replace('r', '');
      const teamName = driverData.dr?.value || `Team ${competitorId}`;
      const kartNumber = parseInt(driverData.no?.value || '0') || 0;

      if (kartNumber > 0) {
        teamsToCreate.push({
          sessionId,
          name: teamName,
          currentKartId: null,
          pastKarts: [],
          pits: []
        });

        kartsToCreate.push({
          sessionId,
          kartNumber,
          speed: 4,
          currentTeamId: null,
          status: 'available'
        });
      }
    }

    // Create teams and karts
    const createdTeams = await mockModels.ApexTeam.insertMany(teamsToCreate);
    const createdKarts = await mockModels.ApexKart.insertMany(kartsToCreate);

    // Map ALL teams and karts (this is the fix!)
    const teamsByName = new Map();
    const kartsByNumber = new Map();

    createdTeams.forEach(team => {
      teamsByName.set(team.name, team);
    });

    createdKarts.forEach(kart => {
      kartsByNumber.set(kart.kartNumber, kart);
    });

    console.log(`📋 Team mapping: ${teamsByName.size} teams, Kart mapping: ${kartsByNumber.size} karts`);

    // Second pass: Create competitors
    for (const [driverId, driverData] of Object.entries(this.gridData.drivers)) {
      const competitorId = driverId.replace('r', '');
      const teamName = driverData.dr?.value || `Team ${competitorId}`;
      const kartNumber = parseInt(driverData.no?.value || '0') || 0;

      // Special debugging for competitor 45405
      if (competitorId === '45405') {
        console.log(`🔍 Debugging competitor 45405:`);
        console.log(`   Team name: "${teamName}"`);
        console.log(`   Kart number: ${kartNumber}`);
        console.log(`   Team found: ${!!teamsByName.get(teamName)}`);
        console.log(`   Kart found: ${!!kartsByNumber.get(kartNumber)}`);
      }

      if (kartNumber > 0) {
        const team = teamsByName.get(teamName);
        const kart = kartsByNumber.get(kartNumber);

        if (team && kart) {
          competitorsToCreate.push({
            sessionId,
            websocketId: competitorId,
            teamId: team._id,
            kartId: kart._id,
            name: teamName,
            nationality: driverData.nat?.value || '',
            drivers: [teamName],
            isActive: true
          });

          if (competitorId === '45405') {
            console.log(`✅ Competitor 45405 will be created successfully`);
          }
        } else {
          if (competitorId === '45405') {
            console.warn(`⚠️ Could not find team "${teamName}" or kart ${kartNumber} for competitor ${competitorId}`);
            console.warn(`   Team found: ${!!team}, Kart found: ${!!kart}`);
            if (!team) console.warn(`   Available teams: ${Array.from(teamsByName.keys()).slice(0, 5).join(', ')}...`);
            if (!kart) console.warn(`   Available kart numbers: ${Array.from(kartsByNumber.keys()).slice(0, 10).join(', ')}...`);
          }
        }
      }
    }

    // Create competitors
    if (competitorsToCreate.length > 0) {
      await mockModels.ApexCompetitor.insertMany(competitorsToCreate);
      console.log(`✅ Created ${competitorsToCreate.length} competitors`);
      
      // Check if 45405 was created
      const competitor45405 = competitorsToCreate.find(c => c.websocketId === '45405');
      if (competitor45405) {
        console.log(`🎉 SUCCESS: Competitor 45405 (${competitor45405.name}) was created with kart ${competitor45405.kartId}`);
      } else {
        console.log(`❌ FAILURE: Competitor 45405 was NOT created`);
      }
    } else {
      console.log(`❌ No competitors were created`);
    }
  }
}

// Main test function
async function testCompetitor45405() {
  console.log('🧪 Testing Competitor 45405 Creation');
  console.log('=====================================');

  try {
    // Read the master vitoria log file
    const logPath = path.join(__dirname, '..', 'apex parser files', 'master vitoria.txt');
    const logContent = fs.readFileSync(logPath, 'utf8');
    const lines = logContent.split('\n').filter(line => line.trim().length > 0);

    console.log(`📖 Read log file: ${logContent.length} characters, ${lines.length} lines`);

    // Find grid message
    const gridMessages = lines.filter(line => line.includes('grid||'));
    console.log(`📊 Found ${gridMessages.length} grid messages`);

    if (gridMessages.length === 0) {
      console.log('❌ No grid messages found');
      return;
    }

    // Show info about each grid message
    gridMessages.forEach((msg, i) => {
      const gridMatch = msg.match(/grid\|\|([^]*?)(?=\n|$)/);
      if (gridMatch) {
        const gridContent = gridMatch[1];
        const driverMatches = gridContent.match(/data-id="r\d+"/g) || [];
        console.log(`   Grid ${i + 1}: ${gridContent.length} chars, ${driverMatches.length} drivers`);
      }
    });

    // Create parser and test with the largest grid message
    const parser = new TestApexParser();

    // Find the grid message with the most content
    let bestGridMessage = gridMessages[0];
    let maxDrivers = 0;

    for (const msg of gridMessages) {
      const gridMatch = msg.match(/grid\|\|([^]*?)(?=\n|$)/);
      if (gridMatch) {
        const gridContent = gridMatch[1];
        const driverMatches = gridContent.match(/data-id="r\d+"/g) || [];
        if (driverMatches.length > maxDrivers) {
          maxDrivers = driverMatches.length;
          bestGridMessage = msg;
        }
      }
    }

    console.log(`\n🔄 Processing grid message with ${maxDrivers} drivers...`);
    await parser.parseMessage(bestGridMessage);

    console.log('\n✅ Test completed!');

  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

// Run the test
testCompetitor45405();
