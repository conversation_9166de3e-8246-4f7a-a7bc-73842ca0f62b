const mongoose = require('mongoose');

const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/raceplanner';

async function testRaceTimer() {
  try {
    await mongoose.connect(MONGODB_URI);
    console.log('✅ Connected to MongoDB');

    // Import models
    const { ApexSession, ApexPitStop } = require('../dist/models/ApexModels');
    const { ApexParserSimple } = require('../dist/services/apexParserSimple');

    // Clean up
    await ApexSession.deleteMany({ title1: { $regex: /Timer Test/ } });

    console.log('\n🔍 Testing Race Timer Implementation...\n');

    const parser = new ApexParserSimple({ enableLogging: true });

    // Create session with grid
    const gridMessage = `init|r|
title1||Timer Test Session
title2||Race Timer Test
track||Test Track
grid||<tbody><tr data-id="r0"><td data-id="c1" data-type="sta">N/A</td><td data-id="c2" data-type="rk">Rnk</td><td data-id="c3" data-type="no">Kart</td><td data-id="c5" data-type="dr">Team</td><td data-id="c14" data-type="otr">On track</td><td data-id="c15" data-type="pit">Pits</td></tr><tr data-id="r17788"><td data-id="r17788c1">N/A</td><td data-id="r17788c2">1</td><td data-id="r17788c3">99</td><td data-id="r17788c5">TIMER TEST TEAM</td><td data-id="r17788c14">00:00:00</td><td data-id="r17788c15">0</td></tr></tbody>`;

    console.log('📊 Creating session...');
    await parser.parseMessage(gridMessage);
    await new Promise(resolve => setTimeout(resolve, 1000));

    console.log('\n⏱️ Testing race timer with dyn1 messages...');
    
    // Set initial race duration (30 seconds for quick test)
    console.log('--- Setting race duration: 30 seconds ---');
    await parser.parseMessage('dyn1|text|30000'); // 30 seconds
    await new Promise(resolve => setTimeout(resolve, 500));

    // Check initial race time
    console.log(`Initial race time: ${parser.getCurrentRaceTime()}s`);
    console.log(`Initial time left: ${parser.getCurrentRaceTimeLeft()}ms`);

    // Wait 2 seconds and check timer
    console.log('\n--- Waiting 2 seconds for timer to count down ---');
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    console.log(`After 2s - race time: ${parser.getCurrentRaceTime()}s`);
    console.log(`After 2s - time left: ${parser.getCurrentRaceTimeLeft()}ms`);

    // Send another dyn1 message (should be ~28 seconds left)
    console.log('\n--- Sending dyn1 update: 28 seconds left ---');
    await parser.parseMessage('dyn1|text|28000'); // 28 seconds left
    await new Promise(resolve => setTimeout(resolve, 500));

    console.log(`After dyn1 update - race time: ${parser.getCurrentRaceTime()}s`);
    console.log(`After dyn1 update - time left: ${parser.getCurrentRaceTimeLeft()}ms`);

    // Test pit stop with timer-based race time
    console.log('\n🏁 Testing pit stop with timer-based race time...');
    await parser.parseMessage('r17788c15|in|1'); // Pit entry
    await new Promise(resolve => setTimeout(resolve, 500));

    // Wait 1 second and send otr update
    await new Promise(resolve => setTimeout(resolve, 1000));
    console.log('\n--- Sending otr update after 1 second in pit ---');
    await parser.parseMessage('r17788c14|otr|05.'); // 5 seconds pit duration
    await new Promise(resolve => setTimeout(resolve, 500));

    // Check pit stop data
    console.log('\n📊 Checking pit stop data...');
    const sessions = await ApexSession.find({ title1: { $regex: /Timer Test/ } }).lean();
    if (sessions.length > 0) {
      const session = sessions[0];
      const pitStops = await ApexPitStop.find({ sessionId: session._id }).lean();
      
      if (pitStops.length > 0) {
        const pit = pitStops[0];
        console.log('\n📊 Pit Stop Results:');
        console.log(`   Race Time At Pit In: ${pit.raceTimeAtPitIn}s`);
        console.log(`   Pit Current Duration: ${pit.pitCurrentDuration}s`);
        
        // Verify race time is reasonable (should be around 3-4 seconds)
        const raceTimeWorking = pit.raceTimeAtPitIn && pit.raceTimeAtPitIn > 0 && pit.raceTimeAtPitIn < 10;
        const otrWorking = pit.pitCurrentDuration && pit.pitCurrentDuration === 5;
        
        console.log('\n🔍 Timer Test Results:');
        console.log(`   Race time calculation: ${raceTimeWorking ? '✅ WORKING' : '❌ NOT WORKING'} (${pit.raceTimeAtPitIn}s)`);
        console.log(`   OTR pit duration: ${otrWorking ? '✅ WORKING' : '❌ NOT WORKING'} (${pit.pitCurrentDuration}s)`);
        
        if (raceTimeWorking && otrWorking) {
          console.log('\n✅ Race timer implementation is working correctly!');
        } else {
          console.log('\n❌ Race timer implementation has issues!');
        }
      } else {
        console.log('❌ No pit stops created');
      }
    } else {
      console.log('❌ No sessions found');
    }

    // Test timer for a few more seconds
    console.log('\n⏱️ Testing timer for 3 more seconds...');
    for (let i = 1; i <= 3; i++) {
      await new Promise(resolve => setTimeout(resolve, 1000));
      console.log(`${i}s: race time = ${parser.getCurrentRaceTime()}s, time left = ${Math.floor(parser.getCurrentRaceTimeLeft() / 1000)}s`);
    }

    // Clean up timer
    parser.cleanup();

    // Clean up database
    await ApexSession.deleteMany({ title1: { $regex: /Timer Test/ } });

  } catch (error) {
    console.error('❌ Test failed:', error);
  } finally {
    await mongoose.disconnect();
    console.log('\n🔌 Disconnected from MongoDB');
  }
}

testRaceTimer().catch(console.error);
