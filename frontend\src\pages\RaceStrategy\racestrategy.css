/* RaceStrategy.css */

.race-strategy-container {
  display: flex;
  flex-direction: column;
  padding: 0;
  height: 100%;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 2rem;
}

.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  text-align: center;
}

/* Race Header Styles */
.race-header-compact {
  background-color: var(--ion-color-light);
  border-radius: 8px;
  margin: 10px;
  padding: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  cursor: pointer;
}

.race-header-top {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.race-title {
  display: flex;
  align-items: center;
  gap: 8px;
}

.race-title h2 {
  margin: 0;
  font-size: 1.2rem;
  font-weight: 600;
}

.race-status-badge {
  font-size: 0.7rem;
  padding: 4px 8px;
}

.race-type {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 0.9rem;
  color: var(--ion-color-medium);
}

.race-time-display {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 8px 0;
}

.race-time-current {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 1.2rem;
  font-weight: 600;
}

.race-time-remaining {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 0.9rem;
  color: var(--ion-color-medium);
}

.race-progress-bar {
  height: 6px;
  border-radius: 3px;
  margin: 8px 0;
}

.race-stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
  gap: 10px;
  margin-top: 10px;
}

.race-stat {
  text-align: center;
  background-color: var(--ion-color-light-shade);
  border-radius: 6px;
  padding: 6px;
}

.stat-label {
  font-size: 0.7rem;
  color: var(--ion-color-medium);
  margin-bottom: 2px;
}

.stat-value {
  font-size: 0.9rem;
  font-weight: 600;
}

/* Stint List Styles */
.stint-list-container {
  padding: 10px;
  overflow-y: auto;
  flex-grow: 1;
}

/* Race Details Styles */
.race-details-container {
  padding: 10px;
}

.race-rules-section {
  background-color: var(--ion-color-light);
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 16px;
}

.race-rules-section h4 {
  margin-top: 0;
  margin-bottom: 12px;
  font-size: 1rem;
  color: var(--ion-color-dark);
}

.rule-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 10px;
}

.rule-item {
  display: flex;
  flex-direction: column;
}

.rule-label {
  font-size: 0.8rem;
  color: var(--ion-color-medium);
  margin-bottom: 2px;
}

.rule-value {
  font-size: 0.9rem;
  font-weight: 500;
}

/* Strategy Management Modal Styles */
.strategy-details-container {
  padding: 16px;
}

.strategy-header {
  margin-bottom: 20px;
}

.strategy-header h2 {
  margin: 0 0 8px 0;
  font-size: 1.4rem;
  font-weight: 600;
}

.race-type-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 1rem;
  color: var(--ion-color-medium);
}

.rules-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
  margin: 16px 0;
}

.rule-item {
  display: flex;
  align-items: center;
  gap: 12px;
}

.rule-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  background-color: var(--ion-color-light);
  border-radius: 50%;
}

.rule-content {
  display: flex;
  flex-direction: column;
}

.rule-label {
  font-size: 0.8rem;
  color: var(--ion-color-medium);
}

.rule-value {
  font-size: 1rem;
  font-weight: 500;
}
