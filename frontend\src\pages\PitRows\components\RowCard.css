.card-container {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-evenly;
  gap: 1rem;
  padding: 1rem;
}

.card-wrapper {
  flex: 1 1 250px;
  max-width: 300px;
  min-width: 200px;
  display: flex;
  flex-direction: column;
}

.pit-row {
  position: relative;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  margin: 0;
  overflow: hidden;
}

.pit-row:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.pit-row ion-card-content {
  padding: 0.5rem;
}

.card-content-wrapper {
  background-color: rgba(var(--ion-color-light-rgb), 0.5);
  padding: 10px;
  border-radius: 5px;
}

.card-header {
  margin-bottom: 1rem;
}

.card-header ion-card-title {
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--ion-color-dark);
}

.card-header ion-card-subtitle {
  font-size: 0.9rem;
  color: var(--ion-color-medium);
}

.card-body {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.info-item ion-icon {
  font-size: 1.2rem;
  color: var(--ion-color-primary);
}

.info-item ion-card-subtitle {
  margin: 0;
  font-size: 0.9rem;
}

@media (max-width: 576px) {
  .card-wrapper {
    flex-basis: 100%;
    max-width: none;
  }
}