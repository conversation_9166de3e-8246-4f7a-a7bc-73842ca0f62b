# Comprehensive Test Log Documentation

## Overview

The `comprehensive-test-log.txt` file contains a complete sample log that demonstrates all key scenarios found in both the **24h Serres** and **Master Vittoria** log formats. This log is designed to test all aspects of the apex parser system.

## Log Structure

### Session 1: 24h Serres Format (10:00:00 - 10:08:55)

#### Grid Creation
```
[2024-11-09T10:00:00.000Z] init|r|
title1||EKO 4H of Serres - Sample Race
title2||Final
track||Karting Track (1200m)
grid||<tbody>...17 columns including pit column c15...</tbody>
```

**Teams Created:**
- `r17749` - FSB RACING (Kart 19)
- `r17766` - RAMUS RACING (Kart 42) 
- `r17774` - SKR JUNIOR (Kart 14)

**Grid Columns (17 total):**
- `c1: sta` - Status
- `c2: rk` - Rank
- `c3: no` - Kart Number
- `c4: nat` - Nationality
- `c5: dr` - Team/Driver
- `c6: s1` - Sector 1
- `c7: s2` - Sector 2
- `c8: s3` - Sector 3
- `c9: llp` - Last Lap Time
- `c10: gap` - Gap
- `c11: int` - Interval
- `c12: blp` - Best Lap Time
- `c13: tlp` - Total Laps
- `c14: otr` - On Track Time
- `c15: pit` - **Pit Status** 🏁
- `c16: pittime` - Pit Time
- `c17: penalty` - Penalty

#### Lap Time Progression
```
[10:01:15] r17749c9|tn|1:10.062  # FSB first lap
[10:01:18] r17766c9|tn|1:10.245  # RAMUS first lap
[10:01:22] r17774c9|tn|1:11.123  # SKR first lap

[10:02:25] r17749c9|tn|1:09.845  # FSB improves (new best)
           r17749c12|ib|1:09.845  # Best lap marker

[10:03:38] r17766c9|tn|1:09.987  # RAMUS improves (new best)
           r17766c12|ib|1:09.987  # Best lap marker
```

#### Pit Stop Scenarios

**FSB Racing Pit Stop (Lap 3-4):**
```
[10:04:45] r17749c15|pit|IN      # Pit entry
           r17749c1|sta|PIT       # Status update

[10:05:15] r17749c15|pit|OUT     # Pit exit (30 seconds)
           r17749c1|sta|ON TRACK  # Status update
           r17749c16|pt|00:00:30  # Pit time recorded
```

**SKR Junior Pit Stop (Lap 5-6):**
```
[10:07:15] r17774c15|pit|IN      # Pit entry
           r17774c1|sta|PIT       # Status update

[10:07:45] r17774c15|pit|OUT     # Pit exit (30 seconds)
           r17774c1|sta|ON TRACK  # Status update
           r17774c16|pt|00:00:30  # Pit time recorded
```

### Session 2: Master Vittoria Format (10:15:00 - 10:25:00)

#### New Session with Different Grid Format
```
[2024-11-09T10:15:00.000Z] init|p|
title1||Master Vittoria Championship
title2||Corsa 3
track||Pista Orario Full (1400m)
grid||<tbody>...15 columns with different layout...</tbody>
```

**Teams Created:**
- `r3` - KART 2 (Kart 2)
- `r45405` - GIARRATANA (Kart 5)
- `r45393` - ROSSI TEAM (Kart 8)

**Grid Columns (15 total):**
- `c1: nat` - Nationality
- `c2: sta` - Status
- `c3: rk` - Rank
- `c4: no` - Kart Number
- `c5: grp` - Group
- `c6: dr` - Driver/Team
- `c7: s1` - Sector 1
- `c8: s2` - Sector 2
- `c9: llp` - Last Lap Time
- `c10: blp` - Best Lap Time
- `c11: int` - Interval
- `c12: gap` - Gap
- `c13: tlp` - Total Laps
- `c14: otr` - On Track Time
- `c15: pit` - **Pit Status** 🏁

#### Lap Time Progression
```
[10:16:15] r3c9|tn|1:08.234      # KART 2 first lap
[10:16:18] r45405c9|tn|1:08.456  # GIARRATANA first lap
[10:16:22] r45393c9|tn|1:08.789  # ROSSI first lap

[10:17:23] r3c9|tn|1:07.987      # KART 2 improves (new best)
           r3c10|ib|1:07.987      # Best lap marker
```

#### Pit Stop Scenarios

**GIARRATANA Pit Stop (Lap 2-3):**
```
[10:18:34] r45405c15|pit|IN      # Pit entry
           r45405c2|sta|PIT       # Status update

[10:19:04] r45405c15|pit|OUT     # Pit exit (30 seconds)
           r45405c2|sta|ON TRACK  # Status update
```

**ROSSI TEAM Pit Stop (Lap 4-5):**
```
[10:20:54] r45393c15|pit|IN      # Pit entry
           r45393c2|sta|PIT       # Status update

[10:21:24] r45393c15|pit|OUT     # Pit exit (30 seconds)
           r45393c2|sta|ON TRACK  # Status update
```

## Key Testing Scenarios

### ✅ Grid Creation
- **Two different grid formats** (17 vs 15 columns)
- **Different column mappings** between sessions
- **Proper team/kart/competitor creation**

### ✅ Lap Time Handling
- **Sequential lap numbering** (1, 2, 3, 4...)
- **Best lap detection** with `ib` markers
- **Lap time format**: `MM:SS.mmm` (e.g., `1:09.845`)
- **Lap counting** with `in` increments

### ✅ Pit Stop Events
- **Pit entry**: `pit|IN` with status change to `PIT`
- **Pit exit**: `pit|OUT` with status change to `ON TRACK`
- **Duration tracking**: 30-second pit stops
- **Multiple teams** having pit stops
- **Different timing** of pit stops

### ✅ Session Management
- **Session transitions** with different grid formats
- **Time progression** with `dyn1` timer updates
- **Race commentary** with `com` messages
- **Weather conditions** and track info

### ✅ Status Updates
- **Driver status changes** (`PIT`, `ON TRACK`)
- **On-track time tracking** with `to` updates
- **Pit time recording** with `pt` values

## Message Format Patterns

### Initialization Messages
```
init|r|          # Start new session
title1||{title}  # Race title
title2||{subtitle} # Race subtitle
track||{track}   # Track name
grid||{html}     # Grid data (HTML table)
```

### Lap Time Messages
```
r{id}c9|tn|{time}   # Last lap time
r{id}c12|ib|{time}  # Best lap time
r{id}c13|in|{count} # Lap count increment
```

### Pit Stop Messages
```
r{id}c15|pit|IN     # Pit entry
r{id}c15|pit|OUT    # Pit exit
r{id}c1|sta|PIT     # Status to pit
r{id}c1|sta|ON TRACK # Status to track
```

### Timer Messages
```
dyn1|text|{time}    # Session timer
r{id}c14|to|{time}  # On track time
r{id}c16|pt|{time}  # Pit time
```

## Expected Database Results

### Sessions Created: 2
1. **EKO 4H of Serres** - 17-column format
2. **Master Vittoria Championship** - 15-column format

### Teams Created: 6
1. FSB RACING (Kart 19)
2. RAMUS RACING (Kart 42)
3. SKR JUNIOR (Kart 14)
4. KART 2 (Kart 2)
5. GIARRATANA (Kart 5)
6. ROSSI TEAM (Kart 8)

### Laps Recorded: ~30
- Each team completes 4-6 laps
- Proper lap number sequencing
- Best lap times recorded

### Pit Stops Created: 4
1. FSB RACING: 30-second pit stop
2. SKR JUNIOR: 30-second pit stop
3. GIARRATANA: 30-second pit stop
4. ROSSI TEAM: 30-second pit stop

## Usage Instructions

### Load the Log
```bash
# Copy the file to your log replay system
cp backend/sample-logs/comprehensive-test-log.txt /path/to/upload/

# Or use the content directly in the replay interface
```

### Test Scenarios
1. **Load the log** in the replay system
2. **Start replay** at normal speed (1.0x)
3. **Monitor database** for object creation
4. **Check pit stops** are properly recorded
5. **Verify lap counting** is sequential
6. **Confirm session transitions** work correctly

### Verification Queries
```javascript
// Check sessions created
db.apex_sessions.find({}).count() // Should be 2

// Check teams created  
db.apex_teams.find({}).count() // Should be 6

// Check pit stops recorded
db.apex_pitstops.find({}).count() // Should be 4

// Check lap progression
db.apex_laps.find({}).sort({createdAt: 1}) // Should show sequential laps
```

This comprehensive test log ensures all major parser functionality is tested with realistic data patterns from both log formats.
