// src/pages/PitRows/components/PitLaneModal.tsx
import React, { useState } from "react";
import {
  IonModal,
  IonHeader,
  IonToolbar,
  IonTitle,
  IonButton,
  IonIcon,
  IonButtons,
  IonContent,
  IonLabel,
  IonSegment,
  IonSegmentButton,
  IonFooter,
  IonText,
  IonNote,
  IonGrid,
  IonRow,
  IonCol,
  IonCard,
  IonCardHeader,
  IonCardTitle,
  IonRippleEffect,
  IonCardSubtitle, // Added for secondary text like name/speed label
} from '@ionic/react';
import { closeCircleOutline, helpCircleOutline, carSportOutline } from "ionicons/icons";

// Import shared types
import { Kart, Team } from "../../../types";

// Import shared styles
import "../../../themes/list-styles.css";
import "../../../themes/speed-styles.css";
// Use TeamGrid styles (includes modal list styles AND card styles)
import "./PitLaneModal.css"
import { buildOutline } from 'ionicons/icons'; // Icon for maintenance

interface PitLaneModalProps {
  isOpen: boolean;
  onClose: () => void;
  teams: Team[];
  pitLaneKarts: Kart[];
  maintenanceKarts: Kart[];
  availableKarts: Kart[];
  onTeamSelect: (team: Team | null) => void;
  onPitKartSelect: (kart: Kart | null) => void;
  // New props
  isTargetRowOccupied: boolean;
  targetKartId: string | null | undefined; // ID of kart currently in the row
  onMoveToMaintenance: (kartId: string) => void;
  onEmptyRow: (kartId: string) => void;
  targetRowNumber?: number;
}

// --- Speed Levels and Helper (from TeamGrid) ---
const speedLevels: { [key: number]: { label: string; value: number; color: string } } = {
    0: { label: 'Super Fast', value: 0, color: 'superfast' },
    1: { label: 'Fast', value: 1, color: 'success' },
    2: { label: 'Average', value: 2, color: 'warning' },
    3: { label: 'Slow', value: 3, color: 'danger' },
    4: { label: 'Unknown', value: 4, color: 'medium' },
};

const getSpeedBgClass = (speedValue: number | undefined | null): string => {
    const level = speedLevels[speedValue ?? 4];
    return level ? `speed-bg-${level.color}` : 'speed-bg-medium';
};
// --- End Speed Helper ---

type ActiveSegmentType = 'teams' | 'pitlane' | 'maintenance' | 'available';

const PitLaneModal: React.FC<PitLaneModalProps> = ({
  isOpen,
  onClose,
  teams,
  pitLaneKarts,
  maintenanceKarts,
  availableKarts,
  onTeamSelect,
  onPitKartSelect,
  isTargetRowOccupied,
  targetKartId,
  onMoveToMaintenance,
  onEmptyRow,
  targetRowNumber,
}) => {
  const [activeSegment, setActiveSegment] = useState<ActiveSegmentType>('teams');

  // --- Render Grid of Kart Cards ---
  const renderKartCards = (karts: Kart[], listType: ActiveSegmentType) => (
    <IonGrid>
      <IonRow>
        {karts.length === 0 && (
          <IonCol size="12" className="ion-text-center ion-padding">
              <IonText color="medium">No karts currently in {listType} status.</IonText>
          </IonCol>
        )}
        {karts.map((kart) => {
          const kartSpeedBgClass = getSpeedBgClass(kart.speed);
          const kartSpeedLabel = speedLevels[kart.speed ?? 4]?.label ?? 'Unknown';
          return (
            <IonCol key={kart._id} size="6" size-sm="4">
              <IonCard
                button
                // Apply speed class directly to the card
                className={`team-card ion-activatable ripple-parent ${kartSpeedBgClass}`}
                onClick={() => onPitKartSelect(kart)}
              >
                {/* Ensure header class allows text contrast */}
                <IonCardHeader className="team-card-header-custom pit-lane-kart-card-header">
                  {/* Kart Number as Title */}
                  <IonCardTitle className="pit-lane-card-title">#{kart.number}</IonCardTitle>
                  {/* Speed Label as Subtitle */}
                  <IonCardSubtitle className="pit-lane-card-subtitle">{kartSpeedLabel}</IonCardSubtitle>
                </IonCardHeader>
                <IonRippleEffect />
              </IonCard>
            </IonCol>
          );
        })}
      </IonRow>
    </IonGrid>
  );

  // --- Render Grid of Team Cards ---
  const renderTeamCards = (teamList: Team[]) => (
    <IonGrid>
       <IonRow>
         {teamList.length === 0 && (
           <IonCol size="12" className="ion-text-center ion-padding">
              <IonText color="medium">No teams found.</IonText>
           </IonCol>
         )}
         {teamList.map((team) => {
           const currentKart = typeof team.currentKartId === 'object' && team.currentKartId !== null
              ? team.currentKartId as Kart
              : null;
           // Determine background based on assigned kart speed, default to medium/unknown if no kart
           const cardSpeedBgClass = currentKart ? getSpeedBgClass(currentKart.speed) : 'speed-bg-medium';

           return (
             <IonCol key={team._id} size="6" size-sm="4">
                <IonCard
                  button
                  // Apply speed class directly to the card
                  className={`team-card ion-activatable ripple-parent ${cardSpeedBgClass}`}
                  onClick={() => onTeamSelect(team)}
                >
                  {/* Ensure header class allows text contrast */}
                  <IonCardHeader className="team-card-header-custom pit-lane-team-card-header">
                    {/* Team Number as Title */}
                    <IonCardTitle className="pit-lane-card-title">#{team.number}</IonCardTitle>
                    {/* Team Name as Subtitle */}
                    <IonCardSubtitle className="pit-lane-card-subtitle">{team.name}</IonCardSubtitle>
                    {/* Optional: Indicate assigned kart subtly if needed */}
                    {/* {currentKart && <IonNote className="pit-lane-card-kart-note">Kart: #{currentKart.number}</IonNote>} */}
                  </IonCardHeader>
                  <IonRippleEffect />
                </IonCard>
             </IonCol>
           );
         })}
       </IonRow>
    </IonGrid>
  );

  // --- Action Handlers ---
  const handleMoveToMaintenanceClick = () => {
    if (targetKartId) {
        onMoveToMaintenance(targetKartId);
        onClose(); // Close modal after action
    }
  };

  const handleEmptyRowClick = () => {
    if (targetKartId) {
        onEmptyRow(targetKartId);
        onClose(); // Close modal after action
    }
  };

  return (
    // Use the class defined in TeamGrid.css for info modals
    <IonModal isOpen={isOpen} onDidDismiss={onClose} className="team-info-modal">
      <IonHeader>
        <IonToolbar color="primary">
          <IonTitle>
            {isTargetRowOccupied
              ? `Actions for Row ${targetRowNumber ?? '?'}`
              : `Select Kart/Team for Row ${targetRowNumber ?? '?'}`
             }
          </IonTitle>
          <IonButtons slot="primary">
            <IonButton fill="clear" onClick={onClose}>
              <IonIcon icon={closeCircleOutline} />
            </IonButton>
          </IonButtons>
        </IonToolbar>
      </IonHeader>

      {/* Segment Buttons */}
      <IonToolbar>
        <IonSegment
            value={activeSegment}
            onIonChange={(e) => setActiveSegment(e.detail.value as ActiveSegmentType)}
            scrollable
            // Disable segments if row is occupied and action is not swap?
        >
          <IonSegmentButton value="teams">
            <IonLabel>Teams</IonLabel>
          </IonSegmentButton> {/* Keep teams for swap */}
          {/* <IonSegmentButton value="pitlane"> <IonLabel>Pit Lane</IonLabel> </IonSegmentButton> */}
          <IonSegmentButton value="maintenance">
            <IonLabel>Maintenance</IonLabel>
          </IonSegmentButton>
          <IonSegmentButton value="available">
            <IonLabel>Available</IonLabel>
          </IonSegmentButton>
        </IonSegment>
      </IonToolbar>

      {/* Content based on active segment - Now using card renderers */}
      {/* Add padding to content area for cards */}
      <IonContent className="ion-padding">
        {activeSegment === 'teams' && renderTeamCards(teams)}
        {/* {activeSegment === 'pitlane' && renderKartCards(pitLaneKarts, 'pitlane')} */}
        {activeSegment === 'maintenance' && renderKartCards(maintenanceKarts, 'maintenance')}
        {activeSegment === 'available' && renderKartCards(availableKarts, 'available')}
      </IonContent>

      {/* Footer Buttons - Conditionally Rendered */}
      <IonFooter>
        {isTargetRowOccupied && targetKartId && (
          <IonToolbar>
            <IonButtons slot="primary">
              <IonButton color="warning" onClick={handleMoveToMaintenanceClick}>
                <IonIcon slot="start" icon={buildOutline} />
                To Maintenance
              </IonButton>
            </IonButtons>
            <IonButtons slot="secondary">
              <IonButton color="danger" onClick={handleEmptyRowClick}>
                <IonIcon slot="start" icon={closeCircleOutline} />
                Empty Row
              </IonButton>
            </IonButtons>
          </IonToolbar>
        )}
      </IonFooter>

    </IonModal>
  );
};

export default PitLaneModal;
