// src/components/Teams/teamUtils.ts

// --- Speed Levels and Helper ---
export const speedLevels: { [key: number]: { label: string; value: number; color: string } } = {
    0: { label: 'Super Fast', value: 0, color: 'superfast' },
    1: { label: 'Fast', value: 1, color: 'success' },
    2: { label: 'Average', value: 2, color: 'warning' },
    3: { label: 'Slow', value: 3, color: 'danger' },
    4: { label: 'Unknown', value: 4, color: 'medium' },
};

export const getSpeedBgClass = (speedValue: number | undefined | null): string => {
    const level = speedLevels[speedValue ?? 4];
    return level ? `speed-bg-${level.color}` : 'speed-bg-medium';
};

// --- Format Date Helper ---
export const formatDate = (dateString: string | Date | undefined): string => {
    if (!dateString) return 'N/A';
    try {
        return new Date(dateString).toLocaleString(undefined, {
            dateStyle: 'short', timeStyle: 'short',
        });
    } catch (e) { return 'Invalid Date'; }
};
