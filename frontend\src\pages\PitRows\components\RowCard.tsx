import React, { useState, useEffect } from "react"; // Import useEffect
import {
    IonCard,
    IonCardContent,
    IonCardSubtitle,
    IonCardTitle,
    IonRippleEffect,
    IonSpinner, // Import Spinner for loading state
    IonText,    // Import IonText for displaying messages
} from "@ionic/react";
import ColorIndicator from "./ColorIndicator";
//import "./common.css"
import "./RowCard.css"
import "../../../themes/speed-styles.css"

// --- Import Shared Interfaces ---
import { Kart, Row, Team } from "../../../types";
// --- Import API Client (adjust path as needed) ---
import axios, { AxiosError } from 'axios';
const API_BASE_URL = import.meta.env.VITE_API_URL;

// --- Props for the main container component ---
interface RowCardContainerProps {
    rows: Row[];
    handleCardClick: (row: Row) => void;
}

// --- Props for the individual card component ---
interface SingleRowCardProps {
    row: Row;
    handleCardClick: (row: Row) => void;
}

// --- Speed Definitions (Keep as is) ---
const speedLevels: { [key: number]: { label: string; value: number; color: string } } = {
    0: { label: 'Super Fast', value: 0, color: 'superfast' },
    1: { label: 'Fast', value: 1, color: 'success' },
    2: { label: 'Average', value: 2, color: 'warning' },
    3: { label: 'Slow', value: 3, color: 'danger' },
    4: { label: 'Unknown', value: 4, color: 'medium' },
};

const getSpeedBgClass = (speedValue: number | undefined | null): string => {
    const level = speedLevels[speedValue ?? 4];
    return level ? `speed-bg-${level.color}` : 'speed-bg-medium';
};
// --- End Speed Definitions ---

// --- Type for the last team data ---
type LastTeamInfo = Pick<Team, '_id' | 'name' | 'number'> | null;

// --- Individual Row Card Component ---
const SingleRowCard: React.FC<SingleRowCardProps> = ({ row, handleCardClick }) => {
    const [lastTeam, setLastTeam] = useState<LastTeamInfo>(null);
    const [isLoadingLastTeam, setIsLoadingLastTeam] = useState<boolean>(false);
    const [lastTeamError, setLastTeamError] = useState<string | null>(null);

    // Type assertion or check if populated
    const currentKart = row.currentKartId as Kart | undefined | null; // Allow null
    const isOccupied = !!currentKart;
    const kartNumber = currentKart?.number;
    const kartSpeed = currentKart?.speed;

    // Fetch last team when kartId changes
    useEffect(() => {
        // Reset state if kart changes or becomes null
        setLastTeam(null);
        setLastTeamError(null);
        setIsLoadingLastTeam(false);

        if (currentKart?._id) {
            const fetchLastTeam = async () => {
                setIsLoadingLastTeam(true);
                setLastTeamError(null);
                try {
                    //console.log(`[Row ${row.rowNumber}] Fetching last team for Kart ${currentKart._id}`);
                    const response = await axios.get<LastTeamInfo>(`${API_BASE_URL}/karts/${currentKart._id}/last-team`);
                    setLastTeam(response.data);
                    //console.log(`[Row ${row.rowNumber}] Last team found:`, response.data);
                } catch (err: any) {
                    if (err.response && err.response.status === 404) {
                        console.log(`[Row ${row.rowNumber}] No team history found for kart ${currentKart._id}`);
                        setLastTeam(null); // Explicitly set to null on 404
                    } else {
                        console.error(`[Row ${row.rowNumber}] Error fetching last team for kart ${currentKart._id}:`, err);
                        setLastTeamError("Failed to load history");
                        setLastTeam(null); // Clear on error
                    }
                } finally {
                    setIsLoadingLastTeam(false);
                }
            };

            fetchLastTeam();
        }
        // Dependency: Re-run if the kart ID in the row changes
    }, [currentKart?._id, row.rowNumber]); // Added row.rowNumber for logging clarity

    // Determine card content
    const title = isOccupied ? `#${kartNumber}` : `Empty`;
    // Subtitle might need adjustment based on how team info is populated on Kart
    // Assuming currentKart.currentTeamId is populated if assigned to a team (which it isn't if it's in a row)
    // Let's keep the subtitle simple for now or use the fetched last team?
    const subtitle = isOccupied ? `Last Team:` : 'Assign Kart'; // Changed subtitle logic
    const pitTime = isOccupied ? '--:--:--' : '--:--:--'; // Placeholder
    const content = isOccupied ? 'Avg: --:--.---' : 'Avg: --:--.---'; // Placeholder

    const cardSpeedBgClass = getSpeedBgClass(kartSpeed);

    return (
        <div className="card-wrapper" key={row._id}>
            <ColorIndicator color={row.color} />
            <IonCard
                className={`pit-row ${isOccupied ? cardSpeedBgClass : 'speed-bg-empty'} ion-activatable ripple-parent`} // Add empty class
                onClick={() => handleCardClick(row)}
            >
                <IonCardContent>
                    <div className="card-content-wrapper">
                        <div className="card-header">
                            <IonCardTitle>{title}</IonCardTitle>
                            {/* Display Last Team Info in Subtitle Area */}
                            <IonCardSubtitle className="last-team-subtitle">
                                {isOccupied ? (
                                    isLoadingLastTeam ? (
                                        <IonSpinner name="dots" color="medium" style={{ height: '1em', width: '2em' }}/>
                                    ) : lastTeamError ? (
                                        <IonText color="danger" style={{ fontSize: '0.9em' }}>{lastTeamError}</IonText>
                                    ) : lastTeam ? (
                                        `Last Team: ${lastTeam.name}` // (${lastTeam.name}) - Add name if desired
                                    ) : (
                                        <IonText color="medium" style={{ fontSize: '0.9em' }}>No history</IonText>
                                    )
                                ) : (
                                    'Assign Kart' // Subtitle when empty
                                )}
                            </IonCardSubtitle>
                        </div>
                        <div className="card-body">
                            <div className="info-item">
                                <IonCardSubtitle>Pit: {pitTime}</IonCardSubtitle>
                            </div>
                            <div className="info-item">
                                <IonCardSubtitle>{content}</IonCardSubtitle>
                            </div>
                        </div>
                    </div>
                </IonCardContent>
                <IonRippleEffect></IonRippleEffect>
            </IonCard>
        </div>
    );
};


// --- Main Container Component (Previously RowCard) ---
// Renders the list of SingleRowCard components
const RowCardList: React.FC<RowCardContainerProps> = ({ rows = [], handleCardClick }) => {
    return (
        <div className="card-container">
            {rows.map((row) => (
                <SingleRowCard
                    key={row._id} // Key is now on the SingleRowCard wrapper
                    row={row}
                    handleCardClick={handleCardClick}
                />
            ))}
        </div>
    );
};

// Export the container component as the default
export default RowCardList;