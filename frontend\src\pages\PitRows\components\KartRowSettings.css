/* d:\Desktop\ManulilacRaceplanner\frontend\src\pages\PitRows\components\KartRowSettings.css */

.row-setting-item {
  --inner-padding-end: 8px; /* Reduce padding to fit buttons */
}

.row-number-label {
  font-weight: bold;
  min-width: 70px; /* Ensure consistent width */
  margin-right: 16px;
}

.color-select {
/* Style the IonSelect component itself */
flex-grow: 1;
max-width: calc(100% - 120px); /* Adjust based on label and button width */
margin-right: 8px;
/* Add a pseudo-element for the selected color indicator */
position: relative;
padding-left: 30px; /* Make space for the indicator */
--padding-start: 30px; /* Ionic variable for padding */
}

.color-select::before {
/* Style the selected color indicator */
content: '';
position: absolute;
left: 8px; /* Position indicator within the padding */
top: 50%;
transform: translateY(-50%);
width: 18px; /* Size of the indicator */
height: 18px;
border-radius: 4px; /* Make it a rounded square */
background-color: #ccc; /* Default color */
border: 1px solid rgba(0, 0, 0, 0.2);
}

/* Apply background color to the indicator based on the select's class */
/* Using hardcoded hex values as requested */
.color-select.row-bg-white::before { background-color: #FFFFFF; border: 1px solid #ccc; } /* Add border for visibility */
.color-select.row-bg-black::before { background-color: #000000; }
.color-select.row-bg-red::before { background-color: #FF0000; }
.color-select.row-bg-green::before { background-color: #008000; }
.color-select.row-bg-blue::before { background-color: #0000FF; }
.color-select.row-bg-yellow::before { background-color: #FFFF00; }


/* Style the options within the dropdown */
.select-option-color-block {
--padding-start: 8px; /* Adjust padding */
--padding-end: 8px;
--inner-padding-start: 0;
--inner-padding-end: 0;
min-height: 36px; /* Ensure options have height */
/* The background color is applied by the row-bg-* class */
/* Explicitly set --background for the option itself */
}

/* Hide the default text node inside the option */
.select-option-color-block .sc-ion-select-option-md, /* Adjust selectors based on inspection if needed */
.select-option-color-block .sc-ion-select-option-ios {
/* You might need to inspect the shadow DOM to find the exact inner element holding text */
/* Alternatively, setting font-size: 0; might work */
color: transparent; /* Hide text */
}

/* Apply background colors to the options using --background */
/* Using hardcoded hex values as requested */
.select-option-color-block.row-bg-white { --background: #FFFFFF; border-bottom: 1px solid #eee; } /* Add border for visibility */
.select-option-color-block.row-bg-black { --background: #000000; }
.select-option-color-block.row-bg-red { --background: #FF0000; }
.select-option-color-block.row-bg-green { --background: #008000; }
.select-option-color-block.row-bg-blue { --background: #0000FF; }
.select-option-color-block.row-bg-yellow { --background: #FFFF00; }

/* Add custom color variables if needed in :root in a global CSS file */
/*
:root {
  --ion-color-orange: #ff9f43;
  --ion-color-orange-rgb: 255,159,67;
  --ion-color-orange-contrast: #ffffff;
  --ion-color-orange-contrast-rgb: 255,255,255;
  --ion-color-orange-shade: #e08c3b;
  --ion-color-orange-tint: #ffa956;
}
.ion-color-orange {
  --ion-color-base: var(--ion-color-orange);
  --ion-color-base-rgb: var(--ion-color-orange-rgb);
  --ion-color-contrast: var(--ion-color-orange-contrast);
  --ion-color-contrast-rgb: var(--ion-color-orange-contrast-rgb);
  --ion-color-shade: var(--ion-color-orange-shade);
  --ion-color-tint: var(--ion-color-orange-tint);
}
*/
