/**
 * WebSocket service specifically for Live Timing page
 * Connects to the existing websocket server and listens for apex parser events
 */

interface WebSocketMessage {
  type: string;
  [key: string]: any;
}

interface LapRecordedEvent {
  competitorId: string;
  teamId: string;
  kartNumber: number;
  lapNumber: number;
  lapTime: number;
  lapTimeFormatted: string;
  isBestLap: boolean;
  isPersonalBest: boolean;
  timestamp: Date;
}

interface ApexDataEvent {
  content: string;
  originalMessage: string;
  parsedMessage: {
    type: string;
    data: Record<string, { type: string; value: string }>;
  };
  lineNumber: number;
  timestamp: string;
}

type EventHandler = (data: any) => void;

class LiveTimingWebSocket {
  private ws: WebSocket | null = null;
  private eventHandlers = new Map<string, EventHandler[]>();
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000;
  private isConnecting = false;
  private shouldReconnect = true;

  constructor() {
    this.eventHandlers.set('lapRecorded', []);
    this.eventHandlers.set('apexData', []);
    this.eventHandlers.set('sessionCreated', []);
    this.eventHandlers.set('teamCreated', []);
    this.eventHandlers.set('pitStopRecorded', []);
    this.eventHandlers.set('connected', []);
    this.eventHandlers.set('disconnected', []);
    this.eventHandlers.set('error', []);
  }

  /**
   * Connect to the websocket server
   */
  connect(): void {
    if (this.isConnecting || (this.ws && this.ws.readyState === WebSocket.CONNECTING)) {
      return;
    }

    this.isConnecting = true;
    const wsUrl = 'ws://localhost:5000'; // Same as LogReplay page

    try {
      this.ws = new WebSocket(wsUrl);

      this.ws.onopen = () => {
        console.log('🔌 Live Timing WebSocket connected');
        this.isConnecting = false;
        this.reconnectAttempts = 0;
        this.reconnectDelay = 1000;
        this.emit('connected');
      };

      this.ws.onmessage = (event) => {
        try {
          const message: WebSocketMessage = JSON.parse(event.data);
          this.handleMessage(message);
        } catch (error) {
          console.error('Error parsing WebSocket message:', error);
        }
      };

      this.ws.onclose = (event) => {
        console.log('🔌 Live Timing WebSocket disconnected:', event.code, event.reason);
        this.isConnecting = false;
        this.ws = null;
        this.emit('disconnected');

        if (this.shouldReconnect && event.code !== 1000) {
          this.attemptReconnect();
        }
      };

      this.ws.onerror = (error) => {
        console.error('❌ Live Timing WebSocket error:', error);
        this.isConnecting = false;
        this.emit('error', error);
      };

    } catch (error) {
      console.error('❌ Failed to create WebSocket connection:', error);
      this.isConnecting = false;
      this.emit('error', error);
    }
  }

  /**
   * Disconnect from websocket
   */
  disconnect(): void {
    this.shouldReconnect = false;
    
    if (this.ws) {
      this.ws.close(1000, 'Manual disconnect');
      this.ws = null;
    }
  }

  /**
   * Check if connected
   */
  isConnected(): boolean {
    return this.ws !== null && this.ws.readyState === WebSocket.OPEN;
  }

  /**
   * Add event listener
   */
  on(event: string, handler: EventHandler): void {
    if (!this.eventHandlers.has(event)) {
      this.eventHandlers.set(event, []);
    }
    this.eventHandlers.get(event)!.push(handler);
  }

  /**
   * Remove event listener
   */
  off(event: string, handler: EventHandler): void {
    const handlers = this.eventHandlers.get(event);
    if (handlers) {
      const index = handlers.indexOf(handler);
      if (index > -1) {
        handlers.splice(index, 1);
      }
    }
  }

  /**
   * Handle incoming websocket messages
   */
  private handleMessage(message: WebSocketMessage): void {
    console.log('📨 Live Timing received:', message.type);

    switch (message.type) {
      case 'apex_lap_recorded':
        this.emit('lapRecorded', message.lap as LapRecordedEvent);
        break;

      case 'apex_data':
        this.emit('apexData', message as ApexDataEvent);
        break;

      case 'apex_session_created':
        this.emit('sessionCreated', message.session);
        break;

      case 'apex_team_created':
        this.emit('teamCreated', message.team);
        break;

      case 'apex_pitstop_recorded':
        this.emit('pitStopRecorded', message.pitStop);
        break;

      default:
        // Log unknown message types for debugging
        console.log('🔍 Unknown message type:', message.type, message);
    }
  }

  /**
   * Emit event to handlers
   */
  private emit(event: string, data?: any): void {
    const handlers = this.eventHandlers.get(event);
    if (handlers) {
      handlers.forEach(handler => {
        try {
          handler(data);
        } catch (error) {
          console.error(`Error in Live Timing event handler for ${event}:`, error);
        }
      });
    }
  }

  /**
   * Attempt to reconnect with exponential backoff
   */
  private attemptReconnect(): void {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.error('❌ Max reconnection attempts reached for Live Timing WebSocket');
      return;
    }

    this.reconnectAttempts++;
    console.log(`🔄 Attempting to reconnect Live Timing WebSocket (${this.reconnectAttempts}/${this.maxReconnectAttempts}) in ${this.reconnectDelay}ms...`);

    setTimeout(() => {
      if (this.shouldReconnect) {
        this.connect();
      }
    }, this.reconnectDelay);

    // Exponential backoff with jitter
    this.reconnectDelay = Math.min(this.reconnectDelay * 2 + Math.random() * 1000, 30000);
  }

  /**
   * Get connection status
   */
  getStatus(): { 
    connected: boolean; 
    reconnectAttempts: number; 
    isConnecting: boolean;
    readyState?: number;
  } {
    return {
      connected: this.isConnected(),
      reconnectAttempts: this.reconnectAttempts,
      isConnecting: this.isConnecting,
      readyState: this.ws?.readyState
    };
  }
}

// Create singleton instance
export const liveTimingWebSocket = new LiveTimingWebSocket();

export default liveTimingWebSocket;
