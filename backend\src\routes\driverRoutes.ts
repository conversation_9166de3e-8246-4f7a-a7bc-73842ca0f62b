// routes/driverRoutes.ts
import express from 'express';
import {
  createDriver,
  getAllDrivers,
  getDriverById,
  updateDriver,
  deleteDriver,
} from '../controllers/driverController';

const router = express.Router();

// Create a new driver
router.post('/', createDriver);

// Get all drivers
router.get('/', getAllDrivers);

// Get a specific driver by ID
router.get('/:id', getDriverById);

// Update a driver by ID
router.put('/:id', updateDriver);

// Delete a driver by ID
router.delete('/:id', deleteDriver);

export default router;
