// backend/src/models/Kart.ts
import mongoose, { Document, Schema, Types } from 'mongoose';

export interface IKart extends Document {
  _id: Types.ObjectId;
  number: number;
  speed: number;
  currentTeamId: Types.ObjectId | null; // Team currently using it (on track)
  currentRowId: Types.ObjectId | null; // Row it's currently parked in (pit row) - NEW
  status: 'on_track' | 'in_pit_row' | 'maintenance' | 'available'; // NEW Status field
  // ... other fields like pastTeams, laps, etc.
}

const kartSchema = new Schema<IKart>({
  number: { type: Number, required: true, unique: true },
  speed: { type: Number },
  currentTeamId: { type: Schema.Types.ObjectId, ref: 'Team', default: null },
  currentRowId: { type: Schema.Types.ObjectId, ref: 'Row', default: null }, // NEW
  status: { // NEW
    type: String,
    enum: ['on_track', 'in_pit_row', 'maintenance', 'available'],
    default: 'available',
    required: true,
  },
  // ... other schema definitions
}, { timestamps: true });

// Add an index to prevent a kart being assigned to both a team and a row simultaneously (optional constraint)
// kartSchema.index({ currentTeamId: 1 }, { unique: true, partialFilterExpression: { currentTeamId: { $ne: null } } });
// kartSchema.index({ currentRowId: 1 }, { unique: true, partialFilterExpression: { currentRowId: { $ne: null } } });
// Note: The above indices might be too restrictive if a kart briefly exists in both during a swap transaction.
// A validation check in the controller is often more practical.

export const Kart = mongoose.model<IKart>('Kart', kartSchema);