// teamController.ts
import mongoose, { ClientSession, FilterQuery } from "mongoose"; // <-- Add ClientSession
import { Request, Response } from "express";
import { Team, ITeam } from "../models/Team";
import { Kart, IKart } from "../models/Kart";
import { sendWebSocketMessage } from "../websocket/websocket";
import {
  sendErrorResponse,
  isValidObjectId, // <-- Import isValidObjectId
  safelyEndSession, // <-- Import session helpers
  abortTransactionAndEndSession, // <-- Import session helpers
} from "../utils/controllerUtils";
import { AssignmentLog, IAssignmentLog } from "../models/AssignmentLog";

// --- Event names ---
const TEAM_UPDATE_EVENT = "teamsUpdated";
const KART_UPDATE_EVENT = "kartsUpdated"; // Need to update karts too

export const getAllTeams = async (
  req: Request,
  res: Response
  // Add return type Promise<void> if not already there
): Promise<void> => {
  try {
    // --- Add Populate Here ---
    const teams = await Team.find()
      // Populate 'currentKartId'. Select only '_id' and 'number' from the Kart document.
      .populate<{ currentKartId: IKart | null }>
      ({
        // Type the populated field
        path: "currentKartId",
      })
      // Optionally populate other fields if needed, e.g.:
      // .populate('drivers', 'name') // Get only driver names
      .sort({ number: 1 }) // Optional sorting
      .lean() // Use lean for performance if you don't need Mongoose documents after fetch
      .exec();

    res.status(200).json(teams);
  } catch (error: unknown) {
    // Use your error handling helper
    console.error("Error retrieving teams:", error);
    const message =
      error instanceof Error ? error.message : "Error retrieving teams";
    sendErrorResponse(res, message, 500, error);
    return;
  }
};

export const getTeamById = async (
  req: Request,
  res: Response
): Promise<void> => {
  console.log(`[Team Controller] GET /api/teams/${req.params.id} - Attempting to fetch team by ID.`);
  // ... (no changes needed here) ...
  const id = req.params.id;
  if (!id || !mongoose.Types.ObjectId.isValid(id)) {
    sendErrorResponse(res, "Invalid team ID format", 400);
    return;
  }
  try {
    const team = await Team.findById(id).populate([
      "currentKartId",
      "pastKarts",
      "pits",
      "laps",
      "drivers",
    ]);
    if (!team) {
      console.warn(`[Team Controller] Team with ID ${id} not found.`);
      sendErrorResponse(res, "Team not found", 404);
      return;
    }
    res.status(200).json(team);
  } catch (error) {
    sendErrorResponse(res, "Error retrieving team", 500, error);
    return;
  }
};

export const createTeam = async (
  req: Request,
  res: Response
): Promise<void> => {
  console.log("[Team Controller] POST /api/teams - Attempting to create a new team.");
  console.log("[Team Controller] Received team data:", req.body);
  try {
    const newTeam = new Team(req.body);
    await newTeam.save();
    console.log(`[Team Controller] Team created with ID: ${newTeam._id}`);

    // ✅ Notify clients via WebSocket using the EXPECTED event structure
    sendWebSocketMessage({
      event: TEAM_UPDATE_EVENT /*, optionalPayload: newTeam */,
    }); // Use 'event' key
    console.log(`WebSocket message sent: { event: "${TEAM_UPDATE_EVENT}" }`); // Log what's sent

    res.status(201).json(newTeam);
  } catch (error) {
    console.error("Error creating team:", error); // Log the error
    sendErrorResponse(res, "Error creating team", 500, error);
    return;
  }
};

export const updateTeam = async (
  req: Request,
  res: Response
): Promise<void> => {
  console.log(`[Team Controller] PUT /api/teams/${req.params.id} - Attempting to update team by ID.`);
  const id = req.params.id;
  if (!id || !mongoose.Types.ObjectId.isValid(id)) {
    sendErrorResponse(res, "Invalid team ID format", 400);
    return;
  }
  try {
    const updatedTeam = await Team.findByIdAndUpdate(id, req.body, {
      new: true,
    }).populate(["currentKartId", "pastKarts", "pits", "laps", "drivers"]);
    if (!updatedTeam) {
      console.warn(`[Team Controller] Team with ID ${id} not found for update.`);
      sendErrorResponse(res, "Team not found", 404);
      return;
    }

    // ✅ Notify clients via WebSocket using the EXPECTED event structure
    sendWebSocketMessage({
      event: TEAM_UPDATE_EVENT /*, optionalPayload: updatedTeam */,
    }); // Use 'event' key
    console.log(`WebSocket message sent: { event: "${TEAM_UPDATE_EVENT}" }`); // Log what's sent

    res.status(200).json(updatedTeam);
  } catch (error) {
    console.error("Error updating team:", error); // Log the error
    sendErrorResponse(res, "Error updating team", 500, error);
    return;
  }
};

export const deleteTeam = async (
  req: Request,
  res: Response
): Promise<void> => {
  console.log(`[Team Controller] DELETE /api/teams/${req.params.id} - Attempting to delete team by ID.`);
  const id = req.params.id;
  if (!id || !mongoose.Types.ObjectId.isValid(id)) {
    sendErrorResponse(res, "Invalid team ID format", 400);
    return;
  }
  try {
    const deletedTeam = await Team.findByIdAndDelete(id);
    if (!deletedTeam) {
      console.warn(`[Team Controller] Team with ID ${id} not found for deletion.`);
      sendErrorResponse(res, "Team not found", 404);
      return;
    }

    // ✅ Notify clients via WebSocket using the EXPECTED event structure
    sendWebSocketMessage({
      event: TEAM_UPDATE_EVENT /*, optionalPayload: { deletedId: id } */,
    }); // Use 'event' key
    console.log(`[Team Controller] Team ID ${id} deleted successfully.`);
    console.log(`WebSocket message sent: { event: "${TEAM_UPDATE_EVENT}" }`); // Log what's sent

    res.status(200).json({ message: "Team deleted successfully" }); // Send success message back
  } catch (error) {
    console.error("Error deleting team:", error); // Log the error
    res
      .status(500)
      .json({
        message: "Error deleting team",
        error: error instanceof Error ? error.message : String(error),
      });
  }
};

/**
 * @description Get assignment history logs related to a specific team.
 * @route GET /api/teams/:teamId/assignment-history
 */
export const getTeamAssignmentHistoryController = async (
  req: Request,
  res: Response
): Promise<void> => {
  console.log(`[Team Controller] GET /api/teams/${req.params.teamId}/assignment-history - Attempting to fetch assignment history for team.`);
  const { teamId } = req.params;
  const operation = "getTeamAssignmentHistory";

  // 1. Validate Team ID
  if (!teamId || !isValidObjectId(teamId)) {
    return sendErrorResponse(res, "Valid Team ID is required.", 400);
  }

  try {
    console.log(
      `[${operation}] Fetching assignment history for Team ${teamId}.`
    );
    const objectTeamId = new mongoose.Types.ObjectId(teamId);

    // 2. Define the filter: Find logs where the team was the target OR the source
    const filter: FilterQuery<IAssignmentLog> = {
      $or: [{ teamId: objectTeamId }, { previousTeamId: objectTeamId }],
    };

    // 3. Execute the query
    const historyLogs = await AssignmentLog.find(filter)
      .sort({ timestamp: -1 }) // Sort newest first by event timestamp
      .populate("kartId", "number") // Populate kart number
      // Populate other relevant fields as needed for display
      .populate("rowId", "rowNumber color")
      .populate("previousRowId", "rowNumber color")
      // Optionally populate teamId/previousTeamId again if you need more than just the ID
      // .populate('teamId', 'name number')
      // .populate('previousTeamId', 'name number')
      .lean() // Use lean for performance
      .exec();

    console.log(
      `[${operation}] Found ${historyLogs.length} history logs for Team ${teamId}.`
    );

    // 4. Send the response
    res.status(200).json(historyLogs);
  } catch (error: unknown) {
    console.error(
      `[${operation}] Error fetching assignment history for Team ${teamId}:`,
      error
    );
    sendErrorResponse(
      res,
      `Error fetching assignment history for team ${teamId}`,
      500,
      error
    );
  }
};
