/* eslint-disable react/prop-types */
/* eslint-disable no-case-declarations */
// src/components/Teams/TeamGrid.tsx
import React, { useState, useEffect, useCallback, useMemo, useRef } from 'react';
import {
    IonHeader,
    IonToolbar,
    IonTitle,
    IonContent,
    IonLoading,
    IonText,
    IonRefresher,
    IonRefresherContent,
    RefresherEventDetail,
    IonButtons,
    IonButton,
    IonIcon,
    IonToast,
    IonAlert,
    IonSearchbar,
    IonGrid,
    IonRow,
    IonCol,
    IonCard,
    IonCardHeader,
    IonCardTitle,
    IonRippleEffect,
    // IonSpinner as it's unused
    IonMenuButton,
} from '@ionic/react';
import {
    add,
    // close as it's unused
    trashOutline,
    searchCircleOutline,
    listCircleOutline,
    swapHorizontalOutline,
    closeCircleOutline,
} from 'ionicons/icons';
import apiService from '../../services/apiService';
import { Team, Kart, AssignmentLog } from '../../types';
import useWebSocket from '../../hooks/webSocketHandler';
import { useDataContext } from '../../context/DataContext';
import { getSpeedBgClass } from './Utils';
import { CreateTeamModal } from './CreateTeamModal';
import { AssignKartModal } from './AssignKartModal'; // Keep
import { TeamInfoModal } from './TeamInfoModal';

import '../../themes/list-styles.css';
import '../../themes/speed-styles.css';
import './TeamGrid.css';

const TEAM_UPDATE_EVENT = "teamsUpdated";
const KART_UPDATE_EVENT = "kartsUpdated";
// ASSIGNMENT_LOG_UPDATE_EVENT constant was removed as it wasn't directly used in the switch

// --- Team Card Component ---
// Note: The 'react/prop-types' ESLint rule is redundant for TypeScript components
// using interfaces like TeamCardProps. It's recommended to disable this rule
// in your ESLint config for .ts/.tsx files.
interface TeamCardProps {
    team: Team;
    isProcessing: boolean;
    onCardClick: (team: Team) => void;
}

const TeamCard: React.FC<TeamCardProps> = React.memo(({ team, isProcessing, onCardClick }) => {
    const currentKart = team.currentKartId as Kart | null;
    const kartSpeedBgClass = currentKart ? getSpeedBgClass(currentKart.speed) : 'speed-bg-medium';

    return (
        <IonCard
            button
            // Modify onClick to log isProcessing state
            onClick={() => {
                console.log(`[TeamGrid] TeamCard clicked for team ${team.number}. isProcessing: ${isProcessing}`);
                onCardClick(team); // Pass the team data to the handler prop
            }}
            className="team-card ion-activatable ripple-parent"
            disabled={isProcessing}
        >
            <IonCardHeader className="team-card-header-custom">
                <div className="team-card-top-row">
                    <span className="team-card-number-small">#{team.number}</span>
                    {currentKart && (
                        <div className={`team-card-kart-indicator speed-indicator ${kartSpeedBgClass}`}>
                            <span className="speed-label">#{currentKart.number}</span>
                        </div>
                    )}
                </div>
                <IonCardTitle className="team-card-name">{team.name}</IonCardTitle>
            </IonCardHeader>
            <IonRippleEffect />
        </IonCard>
    );
});
TeamCard.displayName = 'TeamCard'; // Add display name for React DevTools


// --- Main Grid Component ---
export const TeamGrid: React.FC = () => {
    // --- Data Context ---
    const {
        useApexDatabase,
        selectedSession,
        selectedTeam,
        availableTeams,
        isLoadingTeams,
        refreshTeams,
        error: contextError
    } = useDataContext();

    // --- State variables ---
    const [teams, setTeams] = useState<Team[]>([]);
    const [allKarts, setAllKarts] = useState<Kart[]>([]);
    const [isLoading, setIsLoading] = useState<boolean>(true);
    const [isMutating, setIsMutating] = useState<boolean>(false);
    const [isUnassigningAll, setIsUnassigningAll] = useState<boolean>(false);
    const [isDeletingAll, setIsDeletingAll] = useState<boolean>(false);
    const [error, setError] = useState<string | null>(null);
    const [toastMessage, setToastMessage] = useState<string | null>(null);
    const [toastColor, setToastColor] = useState<"success" | "danger" | "warning">("success");
    const [showCreateModal, setShowCreateModal] = useState<boolean>(false);
    const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
    const [teamToDelete, setTeamToDelete] = useState<Team | null>(null);
    const [searchText, setSearchText] = useState<string>('');
    const [showAssignModal, setShowAssignModal] = useState<boolean>(false);
    const [teamToAssignTo, setTeamToAssignTo] = useState<Team | null>(null);
    const [showInfoModal, setShowInfoModal] = useState<boolean>(false);
    const [selectedTeamForInfo, setSelectedTeamForInfo] = useState<Team | null>(null);
    const [showDeleteAllConfirm, setShowDeleteAllConfirm] = useState<boolean>(false);

    // --- State for Team Info Modal (History part remains here) ---
    const [assignmentHistory, setAssignmentHistory] = useState<AssignmentLog[]>([]);
    const [isLoadingHistory, setIsLoadingHistory] = useState<boolean>(false);
    const [historyError, setHistoryError] = useState<string | null>(null);


    // --- Refs for accessing state inside stable callbacks ---
    const showInfoModalRef = useRef(showInfoModal);
    const selectedTeamForInfoRef = useRef(selectedTeamForInfo);
    const isProcessingRef = useRef(false); // Initialize ref for isProcessing state


    // --- Helper: Show Toast ---
    const showToast = useCallback((message: string, color: "success" | "danger" | "warning") => {
        setToastMessage(message);
        setToastColor(color);
    }, []);

    // --- Fetching Logic ---
    const fetchData = useCallback(async (showLoading = true) => {
        if (showLoading) setIsLoading(true);
        setError(null);

        console.log('[TeamGrid] Fetching teams and karts...', useApexDatabase ? 'APEX MODE' : 'REGULAR MODE');
        try {
            const [teamsData, kartsData] = await Promise.all([
                apiService.teams.getAll('currentKartId,speed', useApexDatabase, selectedSession?._id),
                apiService.karts.getAll(undefined, useApexDatabase, selectedSession?._id)
            ]);
            setTeams(teamsData || []);
            setAllKarts(kartsData || []);
            console.log('[TeamGrid] Fetch successful:', teamsData?.length || 0, 'teams,', kartsData?.length || 0, 'karts');
        } catch (err) {
            console.error("[TeamGrid] Error fetching data:", err);
            const message = err instanceof Error ? err.message : "An unknown error occurred";
            setError(`Failed to load data: ${message}`);
        }

        if (showLoading) setIsLoading(false);
    }, [useApexDatabase, selectedSession]);

    // --- Fetch Assignment History ---
    const fetchAssignmentHistory = useCallback(async (teamId: string) => {
        setIsLoadingHistory(true);
        setHistoryError(null);
        try {
            // Assuming you need to implement this API endpoint
            // const response = await apiService.teams.getAssignmentHistory(teamId);
            
            // Temporary fix until you implement the API method:
            const response = await fetch(`${import.meta.env.VITE_API_URL}/api/teams/${teamId}/assignment-history`);
            const data = await response.json();
            
            // Fix the implicit any types
            const sortedHistory = data.sort((a: AssignmentLog, b: AssignmentLog) =>
                new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
            );
            setAssignmentHistory(sortedHistory);
        } catch (err) {
            console.error("[TeamGrid] Error fetching assignment history:", err);
            const message = err instanceof Error ? err.message : "Failed to load history.";
            setHistoryError(message);
        } finally {
            setIsLoadingHistory(false);
        }
    }, []);

    // --- WebSocket Handler ---
    // Using Record<string, unknown> as a safer alternative to any for payload
    // Correct the payload type to match the likely MessageHandler type from useWebSocket hook
    const handleWebSocketMessage = useCallback((message: { event: string; payload?: unknown }) => { // Removed unused 'payload' from destructuring
        console.log('[TeamGrid] Handling WebSocket message:', message);
        const { event } = message; // Removed unused payload

        // Access current state via refs
        const currentShowInfoModal = showInfoModalRef.current;
        const currentSelectedTeam = selectedTeamForInfoRef.current;

        if (event === TEAM_UPDATE_EVENT || event === KART_UPDATE_EVENT) {
            console.log(`[TeamGrid] Refetching list data due to ${event} event...`);
            fetchData(false);
            // If the info modal is open for the selected team, refetch its history as well
            if (currentShowInfoModal && currentSelectedTeam) {
                 fetchAssignmentHistory(currentSelectedTeam._id);
            }
        }

        // Note: ASSIGNMENT_LOG_UPDATE_EVENT is handled by the history fetch effect below,
        // which runs when selectedTeamForInfo changes or fetchAssignmentHistory is called.
        // If you need specific handling for log updates *without* a full history refetch,
        // you would process the payload here. For now, refetching history on related
        // TEAM/KART updates when the modal is open is sufficient.

        // Added fetchAssignmentHistory to dependency array
    }, [fetchData, fetchAssignmentHistory]); // Removed showInfoModal, selectedTeamForInfo

    // Effect to handle history refetch when the info modal is open and the selected team changes
    // or when fetchAssignmentHistory is called explicitly (e.g., by WebSocket handler)
    useEffect(() => {
        if (showInfoModal && selectedTeamForInfo?._id) {
            console.log(`[TeamGrid] useEffect triggered: Fetching history for ${selectedTeamForInfo._id}`);
            fetchAssignmentHistory(selectedTeamForInfo._id);
        }
        // Dependency array includes showInfoModal, selectedTeamForInfo, and fetchAssignmentHistory
    }, [showInfoModal, selectedTeamForInfo, fetchAssignmentHistory]);

    useWebSocket(handleWebSocketMessage);

    useEffect(() => {
        fetchData();
    }, [fetchData]);

    // --- Calculate Combined Loading State ---
    const isProcessing = isMutating || isLoading || isUnassigningAll || isDeletingAll;

    // --- Effects to keep refs updated ---
    useEffect(() => { showInfoModalRef.current = showInfoModal; }, [showInfoModal]);
    useEffect(() => { selectedTeamForInfoRef.current = selectedTeamForInfo; }, [selectedTeamForInfo]);
    useEffect(() => { isProcessingRef.current = isProcessing; }, [isProcessing]);


    // --- Pull-to-Refresh Handler ---
    const handleRefresh = async (event: CustomEvent<RefresherEventDetail>) => {
        await fetchData(false);
        event.detail.complete();
    };

    // --- API Call Handlers ---

    // Modified to be passed to CreateTeamModal
    const handleCreateTeam = async (name: string, number?: number): Promise<Team | null> => {
        // Validation can be enhanced here or rely on modal's basic checks
        if (!name) {
            showToast("Please enter a valid Team Name.", "warning");
            return null; // Return null on validation failure
        }
        if (number !== undefined && (isNaN(number) || number <= 0)) {
            showToast("If providing a Team Number, it must be a positive number.", "warning");
            return null; // Return null on validation failure
        }

        setIsMutating(true);
        let createdTeam: Team | null = null;

        try {
            const payload: { name: string; number?: number } = { name: name }; // Use passed name
            // This check correctly prevents adding undefined to the payload
            if (number !== undefined) {
                payload.number = number;
            }

            const response = await apiService.teams.create(payload);
            // Handle potential undefined number in response for the toast message
            const createdNumberDisplay = response.number !== undefined ? ` (Number: ${response.number})` : '';
            showToast(`Team '${response.name}' created successfully!${createdNumberDisplay}`, "success");
            createdTeam = response; // Store created team
        } catch (err) {
            console.error("[TeamGrid] Error creating team:", err);
            const message = err instanceof Error ? err.message : "Failed to create team.";
            showToast(`Error: ${message}`, "danger");
        } finally {
            setIsMutating(false);
        }
        return createdTeam; // Return created team or null
    };

    const confirmDeleteTeam = (team: Team) => {
        setTeamToDelete(team);
        setShowDeleteConfirm(true);
    };

    const handleDeleteTeam = async () => {
        if (!teamToDelete) return;
        setIsMutating(true);
        try {
            await apiService.teams.delete(teamToDelete._id);
            showToast(`Team #${teamToDelete.number} deleted successfully!`, "success");
            triggerCloseInfoModal(); // Use trigger to close modal
        } catch (err) {
            console.error("[TeamGrid] Error deleting team:", err);
            const message = err instanceof Error ? err.message : "Failed to delete team.";
            showToast(`Error: ${message}`, "danger");
        } finally {
            setIsMutating(false);
            setTeamToDelete(null);
            setShowDeleteConfirm(false);
        }
    };

    const confirmDeleteAllTeams = () => {
        setShowDeleteAllConfirm(true);
    };

    const handleDeleteAllTeams = async () => {
        setIsDeletingAll(true);
        setShowDeleteAllConfirm(false);
        try {
            const response = await apiService.admin.deleteAllTeamsAndKarts(); // Use the admin API method
            showToast(response.message || 'All teams and associated karts deleted successfully!', "success"); // Access message directly
        } catch (err) {
            console.error("[TeamGrid] Error deleting all teams:", err);
            const message = err instanceof Error ? err.message : "Failed to delete all teams.";
            showToast(`Error: ${message}`, "danger");
        } finally {
            setIsDeletingAll(false);
        }
    };

    const handleUnassignAllKarts = async () => {
        if (!window.confirm("Are you sure you want to unassign ALL karts from their teams? This does NOT delete teams.")) {
            return;
        }
        setIsUnassigningAll(true);
        try {
            const response = await apiService.admin.unassignAllKartsFromTeams(); // response is the data directly
            showToast(response.message || 'All karts unassigned.', "success"); // Access message directly
        } catch (err) {
            console.error("[TeamGrid] Error unassigning all karts:", err);
            const message = err instanceof Error ? err.message : "Failed to unassign karts.";
            showToast(`Error: ${message}`, "danger");
        } finally {
            setIsUnassigningAll(false);
        }
    };

    // Modified to be passed to AssignKartModal
    const handleAssignKart = async (teamId: string, kartId: string): Promise<boolean> => {
        setIsMutating(true);
        let success = false;
        try {
            // Find team number for toast message (optional)
            const teamNum = teams.find(t => t._id === teamId)?.number;
            const response = await apiService.karts.assignToTeam(kartId, teamId); // Use the correct karts API method, response is the Kart object
            showToast(`Kart #${response.number} assigned to Team ${teamNum ?? ''}.`, "success"); // Access number directly
            success = true;
            // Use ref to check modal state - fetch history if info modal was open for this team
            if (showInfoModalRef.current && selectedTeamForInfoRef.current?._id === teamId) {
                fetchAssignmentHistory(teamId);
            }
        } catch (err) {
            console.error("[TeamGrid] Error assigning kart:", err);
            const message = err instanceof Error ? err.message : "Failed to assign kart.";
            showToast(`Error: ${message}`, "danger");
        } finally {
            setIsMutating(false);
        }
        return success;
    };

    const handleUnassignKartFromTeam = async (kartId: string | undefined, teamId: string | undefined, teamNumber: number | undefined): Promise<boolean> => {
        if (!kartId || !teamId) {
            showToast("No kart is assigned or team ID missing.", "warning");
            return false;
        }
        if (!window.confirm(`Are you sure you want to unassign Kart from Team ${teamNumber}?`)) {
            return false;
        }
        setIsMutating(true);
        let success = false;
        try {
            const response = await apiService.karts.unassignFromTeam(kartId); // response is { message: string } directly
            showToast(response.message || `Kart unassigned from Team ${teamNumber}.`, "success"); // Access message directly
            success = true;
            // Use ref to check modal state
            if (showInfoModalRef.current && selectedTeamForInfoRef.current?._id === teamId) {
                fetchAssignmentHistory(teamId);
                // Note: The modal itself will update based on the main `fetchData` triggered by WebSocket
            }
        } catch (err) {
            console.error("[TeamGrid] Error unassigning kart:", err);
            const message = err instanceof Error ? err.message : "Failed to unassign kart.";
            showToast(`Error: ${message}`, "danger");
        } finally {
            setIsMutating(false);
        }
        return success;
    };

    // Modified to be passed to TeamInfoModal
    const handleUpdateTeamName = async (teamId: string, newName: string): Promise<boolean> => {
        setIsMutating(true);
        let success = false;
        try {
            // Assuming apiService.teams.update returns the updated Team object directly
            const response = await apiService.teams.update(teamId, { name: newName });
            // Update state using functional update, checking against ref
            setSelectedTeamForInfo(prev => (prev?._id === teamId ? { ...prev, name: response.name } : prev));
            showToast(`Team name updated successfully!`, "success");
            success = true; // Set success to true before potentially returning
        } catch (err) {
            console.error("[TeamGrid] Error updating team name:", err);
            const message = err instanceof Error ? err.message : "Failed to update team name.";
            showToast(`Error: ${message}`, "danger");
        } finally {
            setIsMutating(false);
        }
        return success;
    };

    // Modified to be passed to TeamInfoModal
    const handleUpdateTeamNumber = async (teamId: string, newNumber: number): Promise<boolean> => {
        if (isNaN(newNumber) || newNumber <= 0) {
            showToast("Please enter a valid positive number.", "warning");
            return false;
        }
        setIsMutating(true);
        let success = false;
        try {
            const response = await apiService.teams.update(teamId, { number: newNumber }); // Assuming this returns the updated Team object directly
            // Assuming apiService.teams.update returns the updated Team object directly
            // Update state using functional update, checking against ref
            setSelectedTeamForInfo(prev => (prev?._id === teamId ? { ...prev, number: response.number } : prev));
            showToast(`Team number updated successfully to #${response.number}!`, "success"); // Access number directly
            success = true; // Set success to true before potentially returning
        } catch (err) {
            console.error("[TeamGrid] Error updating team number:", err);
            const message = err instanceof Error ? err.message : "Failed to update team number.";
            showToast(`Error: ${message}`, "danger");
        } finally {
            setIsMutating(false);
        }
        return success;
    };
    // --- Modal Triggers ---

    const openAssignModal = (team: Team) => {
        if (team.currentKartId) {
            showToast(`Team ${team.number} already has Kart #${(team.currentKartId as Kart)?.number}. Unassign first.`, "warning");
            return;
        }
        setTeamToAssignTo(team);
        setShowAssignModal(true);
    };

    const closeAssignModal = () => {
        setShowAssignModal(false);
        setTeamToAssignTo(null);
        // kartForAssignConfirm state is now managed within AssignKartModal
    };


    // Info Modal Triggers
    const openInfoModal = (team: Team) => {
        console.log('[TeamGrid] ===> Attempting to OPEN Info Modal ===');
        setSelectedTeamForInfo(team);
        console.log('[TeamGrid] setSelectedTeamForInfo finished. State should update soon.');
        setAssignmentHistory([]); // Still reset history here before fetch
        setHistoryError(null);
        setShowInfoModal(true);
        // History fetching is now handled by the useEffect hook below
    };

    // --- Effect to Fetch History ---

    // Renamed closeInfoModal to triggerCloseInfoModal to avoid confusion with the dismiss handler
    const triggerCloseInfoModal = () => {
        console.log('[TeamGrid] ===> Attempting to CLOSE Info Modal (via button) ===');
        setShowInfoModal(false);
        // Removed setTimeout logic, handled by onDidDismiss
    };

    // --- Handler for Modal Dismiss (Backdrop/Hardware Back) ---
    const handleInfoModalDismiss = () => {
        // Log using the ref for isProcessing
        console.log('[TeamGrid] ===> Info Modal DID DISMISS (onDidDismiss) ===');
        console.log(`[TeamGrid] onDidDismiss: Scheduling state reset. isProcessing: ${isProcessingRef.current} (isMutating: ${isMutating}, isLoading: ${isLoading}, isUnassigningAll: ${isUnassigningAll}, isDeletingAll: ${isDeletingAll})`);
        setShowInfoModal(false);
    };

    // --- Wrapper function passed as onCardClick prop ---
    const handleTeamCardClick = (team: Team) => {
        // Check processing state using the ref
        const currentlyProcessing = isProcessingRef.current;
        console.log(`[TeamGrid] handleTeamCardClick called for team ${team.number}. isProcessing: ${currentlyProcessing}`);
        // Remove the modalAlreadyOpen check - rely on isProcessing and the dismiss reset
        if (!currentlyProcessing) {
            openInfoModal(team);
        }
    };

    const triggerAssignFromInfo = () => {
        // Use ref value
        const currentSelectedTeam = selectedTeamForInfoRef.current;
        if (currentSelectedTeam) {
            // Close info modal first
            setShowInfoModal(false);
            // Use setTimeout to allow dismiss animation to start before opening next modal
            // Pass the captured team object to the timeout function
            setTimeout(() => {
                openAssignModal(currentSelectedTeam);
            }, 300); // Adjust timing if needed (matches typical Ionic animation)
        }
    };

    // --- Filtering Logic ---
    const availableKartsForAssignment = useMemo(() => {
        return allKarts.filter(k => k.status === 'available' && !k.currentTeamId && !k.currentRowId);
    }, [allKarts]);

    const filteredTeams = useMemo(() => {
        if (!teams) return [];
        const lowerSearchText = searchText.toLowerCase().trim();
        if (!lowerSearchText) return teams;
        return teams.filter(team => {
            const kartNumber = (team.currentKartId as Kart)?.number?.toString();
            return (
                team.name?.toLowerCase().includes(lowerSearchText) ||
                team.number?.toString().includes(lowerSearchText) ||
                (kartNumber && kartNumber.includes(lowerSearchText))
            );
        });
    }, [teams, searchText]);

    // --- Combined Loading State ---
    const hasTeams = teams && teams.length > 0;

    // --- Render ---
    return (
        <>
            {/* --- Header --- */}
            <IonHeader>
                <IonToolbar>
                    <IonButtons slot="start">
                        <IonMenuButton></IonMenuButton>
                    </IonButtons>
                    <IonTitle>Teams (Grid)</IonTitle>
                    <IonButtons slot="end">
                        <IonButton onClick={() => setShowCreateModal(true)} disabled={isProcessing} title="Add Team">
                            <IonIcon slot="icon-only" icon={add} />
                        </IonButton>
                        <IonButton onClick={handleUnassignAllKarts} disabled={isProcessing || !hasTeams} title="Unassign All Karts (Keep Teams)">
                            <IonIcon slot="icon-only" icon={swapHorizontalOutline} color="warning" />
                        </IonButton>
                        <IonButton onClick={confirmDeleteAllTeams} disabled={isProcessing || !hasTeams} title="Delete ALL Teams">
                            <IonIcon slot="icon-only" icon={trashOutline} color="danger" />
                        </IonButton>
                    </IonButtons>
                </IonToolbar>
                <IonToolbar>
                    <IonSearchbar
                        value={searchText}
                        onIonInput={(e) => setSearchText(e.detail.value ?? "")}
                        placeholder="Search Name, #, Kart #"
                        animated={true}
                        debounce={300}
                        disabled={isLoading}
                    />
                </IonToolbar>
            </IonHeader>

            <IonContent>
                {/* --- Loading, Refresher, Toast, Error --- */}
                <IonLoading isOpen={isLoading && !hasTeams} message={'Loading Teams...'} />
                <IonLoading
                    isOpen={isMutating || isUnassigningAll || isDeletingAll}
                    message={
                        isDeletingAll ? 'Deleting All Teams...' :
                            isUnassigningAll ? 'Unassigning Karts...' :
                                'Updating...'
                    }
                />
                <IonRefresher slot="fixed" onIonRefresh={handleRefresh} disabled={isProcessing}>
                    <IonRefresherContent />
                </IonRefresher>
                <IonToast
                    isOpen={!!toastMessage}
                    message={toastMessage || ''}
                    duration={1500}
                    onDidDismiss={() => setToastMessage(null)}
                    color={toastColor}
                    position="bottom"
                    buttons={[{ icon: 'close', role: 'cancel' }]}
                />
                {error && !isLoading && !isProcessing && (
                    <div className="ion-padding ion-text-center no-data-message">
                        <IonIcon icon={closeCircleOutline} size="large" color="danger" />
                        <IonText color="danger"><h5>Error Loading Teams</h5><p>{error}</p></IonText>
                        <IonButton onClick={() => fetchData()} fill="outline" disabled={isProcessing}>Retry</IonButton>
                    </div>
                )}

                {/* --- Team Grid Layout --- */}
                {!isLoading && !error && (
                    <>
                        {filteredTeams.length > 0 ? (
                            <IonGrid>
                                <IonRow>
                                    {filteredTeams.map((team) => (
                                        <IonCol key={team._id} size="6" size-sm="4" size-md="4" size-lg="3">
                                            <TeamCard
                                                team={team}
                                                isProcessing={isProcessing}
                                                onCardClick={handleTeamCardClick} // Use the wrapper function
                                            />
                                        </IonCol>
                                    ))}
                                </IonRow>
                            </IonGrid>
                        ) : (
                            /* Empty State */
                            <div className="ion-padding ion-text-center no-data-message">
                                <IonIcon icon={searchText ? searchCircleOutline : listCircleOutline} size="large" color="medium" />
                                <IonText color="medium">
                                    <h5>{searchText ? "No teams match search" : "No Teams Found"}</h5>
                                    <p>{searchText ? "Clear search to see all teams." : "Use '+' to add a team."}</p>
                                </IonText>
                                {!searchText && <IonButton onClick={() => setShowCreateModal(true)} fill="clear" disabled={isProcessing}>Add Team</IonButton>}
                            </div>
                        )}
                    </>
                )}

                {/* --- Modals & Alerts --- */}
                {/* Create Team Modal */}
                <CreateTeamModal
                    isOpen={showCreateModal}
                    onDidDismiss={() => setShowCreateModal(false)}
                    onCreateTeam={handleCreateTeam} // Pass the correct handler
                    isMutating={isMutating}
                />

                {/* Assign Kart Modal */}
                <AssignKartModal
                    isOpen={showAssignModal}
                    onDidDismiss={closeAssignModal}
                    teamToAssignTo={teamToAssignTo}
                    availableKarts={availableKartsForAssignment}
                    onAssignKart={handleAssignKart} // Pass the correct handler
                    isMutating={isMutating}
                />

                {/* Delete Team Confirmation Alert */}
                <IonAlert
                    isOpen={showDeleteConfirm}
                    onDidDismiss={() => setShowDeleteConfirm(false)}
                    header={'Confirm Delete'}
                    message={`Are you sure you want to delete Team #${teamToDelete?.number} (${teamToDelete?.name})? This cannot be undone.`}
                    buttons={[
                        { text: 'Cancel', role: 'cancel' },
                        { text: 'Delete', role: 'destructive', handler: handleDeleteTeam }
                    ]}
                />

                {/* Delete All Teams Confirmation Alert */}
                <IonAlert
                    isOpen={showDeleteAllConfirm}
                    onDidDismiss={() => setShowDeleteAllConfirm(false)}
                    header={'DELETE ALL TEAMS?'}
                    subHeader={'This is irreversible!'}
                    message={`Are you absolutely sure you want to delete ALL teams and unassign their karts?`}
                    buttons={[
                        { text: 'Cancel', role: 'cancel' },
                        {
                            text: 'DELETE ALL',
                            role: 'destructive',
                            cssClass: 'danger-alert-button',
                            handler: handleDeleteAllTeams
                        }
                    ]}
                />

                {/* Team Info Modal */}
                <TeamInfoModal
                    isOpen={showInfoModal}
                    onDidDismiss={handleInfoModalDismiss}
                    onRequestClose={triggerCloseInfoModal} // Pass the close trigger function
                    team={selectedTeamForInfo}
                    isProcessing={isProcessing}
                    // Pass handlers as props
                    onUpdateName={handleUpdateTeamName} // Pass correct handler
                    onUpdateNumber={handleUpdateTeamNumber} // Pass correct handler
                    onAssignKart={triggerAssignFromInfo} // Renamed from openAssignModal for clarity
                    onUnassignKart={handleUnassignKartFromTeam} // Pass correct handler
                    onDeleteTeam={confirmDeleteTeam} // Renamed from triggerDeleteFromInfo
                    // Pass history data and state
                    assignmentHistory={assignmentHistory}
                    isLoadingHistory={isLoadingHistory}
                    historyError={historyError}
                />
            </IonContent >
        </>
    );
};
