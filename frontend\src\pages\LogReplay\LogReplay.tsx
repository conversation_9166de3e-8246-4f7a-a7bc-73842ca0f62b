import React, { useState, useEffect, useRef } from 'react';
import {
  IonContent,
  IonHeader,
  IonPage,
  IonTitle,
  IonToolbar,
  IonCard,
  IonCardHeader,
  IonCardTitle,
  IonCardSubtitle,
  IonCardContent,
  IonButton,
  IonItem,
  IonLabel,
  IonTextarea,
  IonProgressBar,
  IonBadge,
  IonIcon,
  IonGrid,
  IonRow,
  IonCol,
  IonRange,
  IonToast,
  IonLoading,
  IonList
} from '@ionic/react';
import {
  playOutline,
  pauseOutline,
  stopOutline,
  refreshOutline,
  speedometerOutline,
  documentTextOutline,
  cloudUploadOutline,
  statsChartOutline
} from 'ionicons/icons';
import apiService from '../../services/apiService';
import './LogReplay.css';

interface ReplayStatus {
  isPlaying: boolean;
  isPaused: boolean;
  currentLine: number;
  totalLines: number;
  progress: number;
  elapsedTime: number;
  estimatedTimeRemaining: number;
}

interface WebSocketMessage {
  type: string;
  content?: string;
  originalMessage?: string;
  parsedMessage?: any;
  lineNumber?: number;
  timestamp?: string;
  event?: string;
  status?: ReplayStatus;
  error?: string;
  // Apex-specific fields
  teamName?: string;
  competitorName?: string;
  lapTime?: string;
  kartNumber?: number;
  sessionId?: string;
  [key: string]: any; // Allow additional properties
}

const LogReplay: React.FC = () => {
  const [replayStatus, setReplayStatus] = useState<ReplayStatus>({
    isPlaying: false,
    isPaused: false,
    currentLine: 0,
    totalLines: 0,
    progress: 0,
    elapsedTime: 0,
    estimatedTimeRemaining: 0
  });
  
  const [speed, setSpeed] = useState<number>(1.0);
  const [logContent, setLogContent] = useState<string>('');
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [showToast, setShowToast] = useState(false);
  const [toastMessage, setToastMessage] = useState('');
  const [toastColor, setToastColor] = useState<'success' | 'danger'>('success');
  const [messages, setMessages] = useState<WebSocketMessage[]>([]);
  const [isConnected, setIsConnected] = useState(false);
  
  const wsRef = useRef<WebSocket | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    // Prevent double connection in React StrictMode
    if (wsRef.current?.readyState === WebSocket.CONNECTING || wsRef.current?.readyState === WebSocket.OPEN) {
      console.log('WebSocket already connected or connecting, skipping...');
      return;
    }

    connectWebSocket();
    loadReplayStatus();

    return () => {
      console.log('Cleaning up WebSocket connection...');
      if (wsRef.current) {
        wsRef.current.close();
        wsRef.current = null;
      }
    };
  }, []);

  const connectWebSocket = () => {
    // Close existing connection if any
    if (wsRef.current) {
      console.log('Closing existing WebSocket connection');
      wsRef.current.close();
      wsRef.current = null;
    }

    const wsUrl = `ws://localhost:5000`;
    console.log('Creating new WebSocket connection to:', wsUrl);
    wsRef.current = new WebSocket(wsUrl);

    wsRef.current.onopen = () => {
      console.log('WebSocket connected');
      setIsConnected(true);
      showToastMessage('WebSocket connected', 'success');
    };

    wsRef.current.onmessage = (event) => {
      try {
        const message: WebSocketMessage = JSON.parse(event.data);
        console.log('📨 Received WebSocket message:', message.type, message);
        handleWebSocketMessage(message);
      } catch (error) {
        console.error('Error parsing WebSocket message:', error);
      }
    };

    wsRef.current.onclose = (event) => {
      console.log('WebSocket disconnected:', event.code, event.reason);
      setIsConnected(false);

      // Only show toast and reconnect if it wasn't a manual close
      if (event.code !== 1000) { // 1000 = normal closure
        showToastMessage('WebSocket disconnected', 'danger');

        // Attempt to reconnect after 3 seconds, but only if no connection exists
        setTimeout(() => {
          if (!wsRef.current || wsRef.current.readyState === WebSocket.CLOSED) {
            console.log('Attempting to reconnect...');
            connectWebSocket();
          }
        }, 3000);
      }
    };

    wsRef.current.onerror = (error) => {
      console.error('WebSocket error:', error);
      showToastMessage('WebSocket connection error', 'danger');
    };
  };

  const handleWebSocketMessage = (message: WebSocketMessage) => {
    switch (message.type) {
      case 'apex_data':
        console.log('📊 Adding apex_data message to list:', message.lineNumber);
        // Add received message to the list
        setMessages(prev => {
          const newMessages = [...prev.slice(-99), message]; // Keep last 100 messages
          console.log(`📊 Messages list updated: ${prev.length} → ${newMessages.length}`);
          return newMessages;
        });
        break;

      case 'replay_status':
        if (message.status) {
          setReplayStatus(message.status);
        }
        break;

      case 'replay_error':
        showToastMessage(`Replay error: ${message.error}`, 'danger');
        break;

      // Apex-specific events
      case 'apex_session_created':
        console.log('✅ Session created:', message);
        showToastMessage('Race session created', 'success');
        break;

      case 'apex_team_created':
        console.log('👥 Team created:', message);
        const teamName = (message as any).team?.name || 'Unknown Team';
        const kartNumber = (message as any).team?.kartNumber || 'Unknown';
        showToastMessage(`Team created: ${teamName} (Kart ${kartNumber})`, 'success');
        break;

      case 'apex_lap_recorded':
        console.log('⏱️ Lap recorded:', message);
        const lapTime = (message as any).lap?.lapTimeFormatted || 'Unknown';
        const competitorId = (message as any).lap?.competitorId || 'Unknown';
        const isBest = (message as any).lap?.isBestLap ? ' [BEST]' : '';
        showToastMessage(`Lap: ${lapTime} by ${competitorId}${isBest}`, 'success');
        break;

      case 'apex_pitstop_recorded':
        console.log('🏁 Pit stop:', message);
        const pitCompetitor = (message as any).pitStop?.competitorId || 'Unknown';
        const pitType = (message as any).pitStop?.pitOutTime ? 'exit' : 'entry';
        showToastMessage(`Pit ${pitType}: ${pitCompetitor}`, 'success');
        break;

      case 'apex_position_update':
        console.log('📍 Position update:', message);
        const competitor = (message as any).update?.competitorId || 'Unknown';
        const position = (message as any).update?.position || 'Unknown';
        showToastMessage(`Position: ${competitor} → P${position}`, 'success');
        break;

      default:
        console.log('Unknown message type:', message.type, message);
    }
  };

  const loadReplayStatus = async () => {
    try {
      const response = await apiService.get('/replay/status');
      if (response.status) {
        setReplayStatus(response.status);
      }
    } catch (error) {
      console.error('Error loading replay status:', error);
    }
  };

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setSelectedFile(file);
    }
  };

  const uploadLogFile = async () => {
    if (!selectedFile) {
      showToastMessage('Please select a file first', 'danger');
      return;
    }

    setIsLoading(true);
    try {
      const formData = new FormData();
      formData.append('logFile', selectedFile);

      const response = await fetch('/api/replay/upload-log', {
        method: 'POST',
        body: formData
      });

      const result = await response.json();
      
      if (result.success) {
        showToastMessage('Log file uploaded successfully!', 'success');
        setReplayStatus(result.status);
        setMessages([]); // Clear previous messages
      } else {
        showToastMessage(result.error || 'Upload failed', 'danger');
      }
    } catch (error) {
      console.error('Error uploading file:', error);
      showToastMessage('Failed to upload file', 'danger');
    } finally {
      setIsLoading(false);
    }
  };

  const loadLogContent = async () => {
    if (!logContent.trim()) {
      showToastMessage('Please enter log content', 'danger');
      return;
    }

    setIsLoading(true);
    try {
      const response = await apiService.post('/replay/load-log', { logContent });
      
      if (response.success) {
        showToastMessage('Log content loaded successfully!', 'success');
        setReplayStatus(response.status);
        setMessages([]); // Clear previous messages
      } else {
        showToastMessage('Failed to load log content', 'danger');
      }
    } catch (error) {
      console.error('Error loading log content:', error);
      showToastMessage('Failed to load log content', 'danger');
    } finally {
      setIsLoading(false);
    }
  };

  const startReplay = async () => {
    try {
      await apiService.post('/replay/start');
      showToastMessage('Replay started', 'success');
    } catch (error) {
      console.error('Error starting replay:', error);
      showToastMessage('Failed to start replay', 'danger');
    }
  };

  const pauseReplay = async () => {
    try {
      await apiService.post('/replay/pause');
      showToastMessage('Replay paused', 'success');
    } catch (error) {
      console.error('Error pausing replay:', error);
      showToastMessage('Failed to pause replay', 'danger');
    }
  };

  const resumeReplay = async () => {
    try {
      await apiService.post('/replay/resume');
      showToastMessage('Replay resumed', 'success');
    } catch (error) {
      console.error('Error resuming replay:', error);
      showToastMessage('Failed to resume replay', 'danger');
    }
  };

  const stopReplay = async () => {
    try {
      await apiService.post('/replay/stop');
      showToastMessage('Replay stopped', 'success');
    } catch (error) {
      console.error('Error stopping replay:', error);
      showToastMessage('Failed to stop replay', 'danger');
    }
  };

  const resetReplay = async () => {
    try {
      await apiService.post('/replay/reset');
      showToastMessage('Replay reset', 'success');
      setMessages([]); // Clear messages
    } catch (error) {
      console.error('Error resetting replay:', error);
      showToastMessage('Failed to reset replay', 'danger');
    }
  };

  const setReplaySpeed = async (newSpeed: number) => {
    try {
      await apiService.post('/replay/speed', { speed: newSpeed });
      setSpeed(newSpeed);
      showToastMessage(`Speed set to ${newSpeed}x`, 'success');
    } catch (error) {
      console.error('Error setting speed:', error);
      showToastMessage('Failed to set speed', 'danger');
    }
  };

  const showToastMessage = (message: string, color: 'success' | 'danger') => {
    setToastMessage(message);
    setToastColor(color);
    setShowToast(true);
  };

  const formatTime = (milliseconds: number): string => {
    const seconds = Math.floor(milliseconds / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    
    if (hours > 0) {
      return `${hours}h ${minutes % 60}m ${seconds % 60}s`;
    } else if (minutes > 0) {
      return `${minutes}m ${seconds % 60}s`;
    } else {
      return `${seconds}s`;
    }
  };

  return (
    <IonPage>
      <IonHeader>
        <IonToolbar>
          <IonTitle>Log Replay</IonTitle>
          <IonBadge 
            color={isConnected ? 'success' : 'danger'} 
            slot="end"
            className="connection-badge"
          >
            {isConnected ? 'Connected' : 'Disconnected'}
          </IonBadge>
        </IonToolbar>
      </IonHeader>

      <IonContent fullscreen>
        <div className="log-replay-container">
          {/* File Upload Section */}
          <IonCard>
            <IonCardHeader>
              <IonCardTitle>Load Log File</IonCardTitle>
            </IonCardHeader>
            <IonCardContent>
              <input
                ref={fileInputRef}
                type="file"
                accept=".txt,.log"
                onChange={handleFileSelect}
                style={{ display: 'none' }}
              />
              
              <IonButton
                expand="block"
                fill="outline"
                onClick={() => fileInputRef.current?.click()}
                className="ion-margin-bottom"
              >
                <IonIcon icon={documentTextOutline} slot="start" />
                {selectedFile ? selectedFile.name : 'Select Log File'}
              </IonButton>

              <IonButton
                expand="block"
                onClick={uploadLogFile}
                disabled={!selectedFile || isLoading}
              >
                <IonIcon icon={cloudUploadOutline} slot="start" />
                Upload & Load
              </IonButton>
            </IonCardContent>
          </IonCard>

          {/* Custom Log Content Section */}
          <IonCard>
            <IonCardHeader>
              <IonCardTitle>Or Enter Log Content</IonCardTitle>
            </IonCardHeader>
            <IonCardContent>
              <IonTextarea
                value={logContent}
                onIonChange={(e) => setLogContent(e.detail.value!)}
                placeholder="Paste log content here..."
                rows={6}
                className="ion-margin-bottom"
              />
              
              <IonButton
                expand="block"
                onClick={loadLogContent}
                disabled={!logContent.trim() || isLoading}
              >
                <IonIcon icon={documentTextOutline} slot="start" />
                Load Content
              </IonButton>
            </IonCardContent>
          </IonCard>

          {/* Replay Controls */}
          <IonCard>
            <IonCardHeader>
              <IonCardTitle>Replay Controls</IonCardTitle>
            </IonCardHeader>
            <IonCardContent>
              <IonGrid>
                <IonRow>
                  <IonCol size="3">
                    <IonButton
                      expand="block"
                      onClick={startReplay}
                      disabled={replayStatus.totalLines === 0 || replayStatus.isPlaying}
                      color="success"
                    >
                      <IonIcon icon={playOutline} />
                    </IonButton>
                  </IonCol>
                  <IonCol size="3">
                    <IonButton
                      expand="block"
                      onClick={replayStatus.isPaused ? resumeReplay : pauseReplay}
                      disabled={!replayStatus.isPlaying}
                      color="warning"
                    >
                      <IonIcon icon={pauseOutline} />
                    </IonButton>
                  </IonCol>
                  <IonCol size="3">
                    <IonButton
                      expand="block"
                      onClick={stopReplay}
                      disabled={!replayStatus.isPlaying}
                      color="danger"
                    >
                      <IonIcon icon={stopOutline} />
                    </IonButton>
                  </IonCol>
                  <IonCol size="3">
                    <IonButton
                      expand="block"
                      onClick={resetReplay}
                      fill="outline"
                    >
                      <IonIcon icon={refreshOutline} />
                    </IonButton>
                  </IonCol>
                </IonRow>
              </IonGrid>

              {/* Speed Control */}
              <IonItem>
                <IonIcon icon={speedometerOutline} slot="start" />
                <IonLabel>Speed: {speed}x</IonLabel>
                <IonRange
                  min={0.1}
                  max={5.0}
                  step={0.1}
                  value={speed}
                  onIonChange={(e) => setReplaySpeed(e.detail.value as number)}
                  slot="end"
                  style={{ width: '150px' }}
                />
              </IonItem>

              {/* Progress */}
              {replayStatus.totalLines > 0 && (
                <>
                  <IonProgressBar value={replayStatus.progress} className="ion-margin-top" />
                  <div className="progress-info">
                    <span>Line {replayStatus.currentLine} of {replayStatus.totalLines}</span>
                    <span>{Math.round(replayStatus.progress * 100)}%</span>
                  </div>
                  {replayStatus.isPlaying && (
                    <div className="time-info">
                      <span>Elapsed: {formatTime(replayStatus.elapsedTime)}</span>
                      <span>Remaining: {formatTime(replayStatus.estimatedTimeRemaining)}</span>
                    </div>
                  )}
                </>
              )}
            </IonCardContent>
          </IonCard>

          {/* Raw WebSocket Messages */}
          <IonCard>
            <IonCardHeader>
              <IonCardTitle>
                Raw WebSocket Messages (Real-time Speed)
                <IonBadge color="primary" className="ion-margin-start">
                  {messages.length}
                </IonBadge>
              </IonCardTitle>
              <IonCardSubtitle>
                Raw data sent from log replay to WebSocket (emulating track data)
              </IonCardSubtitle>
            </IonCardHeader>
            <IonCardContent>
              <div className="messages-container">
                {messages.length === 0 ? (
                  <p>No messages received yet. Start replay to see live data with real-time intervals.</p>
                ) : (
                  <IonList>
                    {messages.slice(-10).map((message, index) => (
                      <IonItem key={index} className="message-item">
                        <IonLabel>
                          <div className="message-header">
                            <h3>Message {message.lineNumber}</h3>
                            <span className="timestamp">
                              {message.timestamp ? new Date(message.timestamp).toLocaleTimeString() : 'No timestamp'}
                            </span>
                          </div>

                          {/* Raw Message Content */}
                          <div className="original-message">
                            <h4>Raw Data (sent to WebSocket):</h4>
                            <pre className="message-content original">
                              {message.content}
                            </pre>
                          </div>
                        </IonLabel>
                      </IonItem>
                    ))}
                  </IonList>
                )}
              </div>
            </IonCardContent>
          </IonCard>

          {/* Database Updates */}
          <IonCard>
            <IonCardHeader>
              <IonCardTitle>
                Database Updates
                <IonBadge color="success" className="ion-margin-start">
                  Live
                </IonBadge>
              </IonCardTitle>
              <IonCardSubtitle>
                Parsed data automatically added to database
              </IonCardSubtitle>
            </IonCardHeader>
            <IonCardContent>
              <div className="database-updates">
                <p>Check the <strong>Database Monitor</strong> page to see:</p>
                <ul>
                  <li>✅ Sessions created from init messages</li>
                  <li>👥 Teams/Karts created from competitor data</li>
                  <li>⏱️ Lap times recorded from timing data</li>
                  <li>🏁 Pit stops tracked from status changes</li>
                  <li>📊 Real-time statistics and counts</li>
                </ul>
                <IonButton
                  fill="outline"
                  color="success"
                  onClick={() => window.open('/database-monitor', '_blank')}
                >
                  <IonIcon icon={statsChartOutline} slot="start" />
                  Open Database Monitor
                </IonButton>
              </div>
            </IonCardContent>
          </IonCard>
        </div>

        <IonLoading isOpen={isLoading} message="Processing..." />

        <IonToast
          isOpen={showToast}
          onDidDismiss={() => setShowToast(false)}
          message={toastMessage}
          duration={3000}
          color={toastColor}
          position="bottom"
        />
      </IonContent>
    </IonPage>
  );
};

export default LogReplay;
