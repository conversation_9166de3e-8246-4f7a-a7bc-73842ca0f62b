// routes/lapRoutes.ts
import express from 'express';
import {
  createLap,
  getAllLaps,
  getLapById,
  updateLap,
  deleteLap,
} from '../controllers/lapController';

const router = express.Router();

// Create a new lap
router.post('/', createLap);

// Get all laps
router.get('/', getAllLaps);

// Get a specific lap by ID
router.get('/:id', getLapById);

// Update a lap by ID
router.put('/:id', updateLap);

// Delete a lap by ID
router.delete('/:id', deleteLap);

export default router;
