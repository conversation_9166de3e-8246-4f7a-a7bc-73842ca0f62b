import React, { useState, useEffect } from 'react';
import {
  IonContent,
  IonHeader,
  IonPage,
  IonTitle,
  IonToolbar,
  IonCard,
  IonCardHeader,
  IonCardTitle,
  IonCardContent,
  IonButton,
  IonList,
  IonItem,
  IonLabel,
  IonBadge,
  IonIcon,
  IonGrid,
  IonRow,
  IonCol,
  IonRefresher,
  IonRefresherContent,
  IonToast,
  IonLoading,
  IonSelect,
  IonSelectOption
} from '@ionic/react';
import {
  statsChartOutline,
  timeOutline,
  peopleOutline,
  carSportOutline,
  refreshOutline,
  trashOutline,
  checkmarkCircleOutline,
  alertCircleOutline,
  speedometerOutline
} from 'ionicons/icons';
import apiService from '../../services/apiService';
import './DatabaseMonitor.css';

interface DatabaseStats {
  sessions: number;
  teams: number;
  karts: number;
  competitors: number;
  laps: number;
  pitStops: number;
  liveData: number;
  timestamp: string;
}

interface ParsingStatus {
  isActive: boolean;
  recentActivity: {
    sessionsCreated: number;
    lapsRecorded: number;
    teamsCreated: number;
  };
  lastActivity: {
    session: any;
    lap: any;
    team: any;
  };
}

interface RecentLap {
  _id: string;
  competitorId: string;
  competitorName: string;
  lapTime: number;
  lapTimeFormatted: string;
  timestamp: string;
  isBestLap: boolean;
  sessionId: string;
}

const DatabaseMonitor: React.FC = () => {
  const [stats, setStats] = useState<DatabaseStats | null>(null);
  const [parsingStatus, setParsingStatus] = useState<ParsingStatus | null>(null);
  const [recentLaps, setRecentLaps] = useState<RecentLap[]>([]);
  const [sessions, setSessions] = useState<any[]>([]);
  const [selectedSession, setSelectedSession] = useState<string>('');
  const [sessionDetails, setSessionDetails] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [showToast, setShowToast] = useState(false);
  const [toastMessage, setToastMessage] = useState('');
  const [toastColor, setToastColor] = useState<'success' | 'danger'>('success');
  const [autoRefresh, setAutoRefresh] = useState(true);

  useEffect(() => {
    loadData();
    
    let interval: NodeJS.Timeout;
    if (autoRefresh) {
      interval = setInterval(loadData, 5000); // Refresh every 5 seconds
    }
    
    return () => {
      if (interval) clearInterval(interval);
    };
  }, [autoRefresh]);

  const loadData = async () => {
    try {
      const [statsData, statusData, lapsData, sessionsData] = await Promise.all([
        apiService.get('/debug/stats'),
        apiService.get('/debug/parsing-status'),
        apiService.get('/debug/laps/recent?limit=10'),
        apiService.get('/debug/sessions?limit=5')
      ]);

      setStats(statsData);
      setParsingStatus(statusData);
      setRecentLaps(lapsData);
      setSessions(sessionsData);
    } catch (error) {
      console.error('Error loading data:', error);
      showToastMessage('Failed to load monitoring data', 'danger');
    }
  };

  const loadSessionDetails = async (sessionId: string) => {
    if (!sessionId) return;
    
    setIsLoading(true);
    try {
      const details = await apiService.get(`/debug/sessions/${sessionId}/details`);
      setSessionDetails(details);
    } catch (error) {
      console.error('Error loading session details:', error);
      showToastMessage('Failed to load session details', 'danger');
    } finally {
      setIsLoading(false);
    }
  };

  const clearAllData = async () => {
    if (!confirm('Are you sure you want to clear ALL apex data? This cannot be undone!')) {
      return;
    }

    setIsLoading(true);
    try {
      const result = await apiService.delete('/debug/clear-all');
      showToastMessage(result.message, 'success');
      await loadData();
      setSessionDetails(null);
      setSelectedSession('');
    } catch (error) {
      console.error('Error clearing data:', error);
      showToastMessage('Failed to clear data', 'danger');
    } finally {
      setIsLoading(false);
    }
  };

  const showToastMessage = (message: string, color: 'success' | 'danger') => {
    setToastMessage(message);
    setToastColor(color);
    setShowToast(true);
  };

  const formatTime = (timestamp: string): string => {
    return new Date(timestamp).toLocaleTimeString();
  };

  const doRefresh = async (event: CustomEvent) => {
    await loadData();
    event.detail.complete();
  };

  return (
    <IonPage>
      <IonHeader>
        <IonToolbar>
          <IonTitle>Database Monitor</IonTitle>
          <IonBadge 
            color={parsingStatus?.isActive ? 'success' : 'medium'} 
            slot="end"
            className="status-badge"
          >
            {parsingStatus?.isActive ? 'Parsing Active' : 'Idle'}
          </IonBadge>
        </IonToolbar>
      </IonHeader>

      <IonContent fullscreen>
        <IonRefresher slot="fixed" onIonRefresh={doRefresh}>
          <IonRefresherContent></IonRefresherContent>
        </IonRefresher>

        <div className="database-monitor-container">
          {/* Database Statistics */}
          <IonCard>
            <IonCardHeader>
              <IonCardTitle>
                <IonIcon icon={statsChartOutline} />
                Database Statistics
                {stats && (
                  <span className="timestamp">
                    Last updated: {formatTime(stats.timestamp)}
                  </span>
                )}
              </IonCardTitle>
            </IonCardHeader>
            <IonCardContent>
              {stats ? (
                <IonGrid>
                  <IonRow>
                    <IonCol size="6">
                      <div className="stat-item">
                        <IonIcon icon={timeOutline} />
                        <div>
                          <h3>{stats.sessions}</h3>
                          <p>Sessions</p>
                        </div>
                      </div>
                    </IonCol>
                    <IonCol size="6">
                      <div className="stat-item">
                        <IonIcon icon={peopleOutline} />
                        <div>
                          <h3>{stats.teams}</h3>
                          <p>Teams</p>
                        </div>
                      </div>
                    </IonCol>
                  </IonRow>
                  <IonRow>
                    <IonCol size="6">
                      <div className="stat-item">
                        <IonIcon icon={carSportOutline} />
                        <div>
                          <h3>{stats.karts}</h3>
                          <p>Karts</p>
                        </div>
                      </div>
                    </IonCol>
                    <IonCol size="6">
                      <div className="stat-item">
                        <IonIcon icon={speedometerOutline} />
                        <div>
                          <h3>{stats.laps}</h3>
                          <p>Lap Times</p>
                        </div>
                      </div>
                    </IonCol>
                  </IonRow>
                </IonGrid>
              ) : (
                <p>Loading statistics...</p>
              )}
            </IonCardContent>
          </IonCard>

          {/* Parsing Status */}
          <IonCard>
            <IonCardHeader>
              <IonCardTitle>
                <IonIcon icon={parsingStatus?.isActive ? checkmarkCircleOutline : alertCircleOutline} />
                Parsing Status
              </IonCardTitle>
            </IonCardHeader>
            <IonCardContent>
              {parsingStatus ? (
                <>
                  <div className="parsing-status">
                    <IonBadge color={parsingStatus.isActive ? 'success' : 'medium'}>
                      {parsingStatus.isActive ? 'Active' : 'Idle'}
                    </IonBadge>
                  </div>
                  
                  <h4>Recent Activity (Last 5 minutes)</h4>
                  <IonList>
                    <IonItem>
                      <IonLabel>Sessions Created</IonLabel>
                      <IonBadge slot="end">{parsingStatus.recentActivity.sessionsCreated}</IonBadge>
                    </IonItem>
                    <IonItem>
                      <IonLabel>Laps Recorded</IonLabel>
                      <IonBadge slot="end">{parsingStatus.recentActivity.lapsRecorded}</IonBadge>
                    </IonItem>
                    <IonItem>
                      <IonLabel>Teams Created</IonLabel>
                      <IonBadge slot="end">{parsingStatus.recentActivity.teamsCreated}</IonBadge>
                    </IonItem>
                  </IonList>

                  {parsingStatus.lastActivity.lap && (
                    <div className="last-activity">
                      <h4>Last Lap Time</h4>
                      <p>
                        Competitor {parsingStatus.lastActivity.lap.competitorId}: 
                        <strong> {parsingStatus.lastActivity.lap.lapTime}</strong>
                        <br />
                        <small>{formatTime(parsingStatus.lastActivity.lap.timestamp)}</small>
                      </p>
                    </div>
                  )}
                </>
              ) : (
                <p>Loading parsing status...</p>
              )}
            </IonCardContent>
          </IonCard>

          {/* Recent Lap Times */}
          <IonCard>
            <IonCardHeader>
              <IonCardTitle>
                Recent Lap Times
                <IonBadge color="primary" className="ion-margin-start">
                  {recentLaps.length}
                </IonBadge>
              </IonCardTitle>
            </IonCardHeader>
            <IonCardContent>
              {recentLaps.length > 0 ? (
                <IonList>
                  {recentLaps.map((lap) => (
                    <IonItem key={lap._id}>
                      <IonLabel>
                        <h3>{lap.competitorName}</h3>
                        <p>
                          <strong>{lap.lapTimeFormatted}</strong>
                          {lap.isBestLap && <IonBadge color="warning" className="ion-margin-start">Best</IonBadge>}
                        </p>
                        <p className="timestamp">{formatTime(lap.timestamp)}</p>
                      </IonLabel>
                    </IonItem>
                  ))}
                </IonList>
              ) : (
                <p>No recent lap times found.</p>
              )}
            </IonCardContent>
          </IonCard>

          {/* Session Details */}
          <IonCard>
            <IonCardHeader>
              <IonCardTitle>Session Details</IonCardTitle>
            </IonCardHeader>
            <IonCardContent>
              <IonItem>
                <IonLabel position="stacked">Select Session</IonLabel>
                <IonSelect
                  value={selectedSession}
                  onIonChange={(e) => {
                    setSelectedSession(e.detail.value);
                    loadSessionDetails(e.detail.value);
                  }}
                  placeholder="Choose a session"
                >
                  {sessions.map((session) => (
                    <IonSelectOption key={session._id} value={session._id}>
                      {session.title1} - {session.title2}
                    </IonSelectOption>
                  ))}
                </IonSelect>
              </IonItem>

              {sessionDetails && (
                <div className="session-details">
                  <h4>{sessionDetails.session.title1} - {sessionDetails.session.title2}</h4>
                  <p>Track: {sessionDetails.session.track}</p>
                  <p>Session ID: {sessionDetails.session._id}</p>
                  
                  <IonGrid>
                    <IonRow>
                      <IonCol size="6">
                        <div className="detail-stat">
                          <strong>{sessionDetails.counts.teams}</strong>
                          <span>Teams</span>
                        </div>
                      </IonCol>
                      <IonCol size="6">
                        <div className="detail-stat">
                          <strong>{sessionDetails.counts.karts}</strong>
                          <span>Karts</span>
                        </div>
                      </IonCol>
                    </IonRow>
                    <IonRow>
                      <IonCol size="6">
                        <div className="detail-stat">
                          <strong>{sessionDetails.counts.totalLaps}</strong>
                          <span>Total Laps</span>
                        </div>
                      </IonCol>
                      <IonCol size="6">
                        <div className="detail-stat">
                          <strong>{sessionDetails.counts.totalPitStops}</strong>
                          <span>Pit Stops</span>
                        </div>
                      </IonCol>
                    </IonRow>
                  </IonGrid>
                </div>
              )}
            </IonCardContent>
          </IonCard>

          {/* Controls */}
          <IonCard>
            <IonCardHeader>
              <IonCardTitle>Controls</IonCardTitle>
            </IonCardHeader>
            <IonCardContent>
              <IonGrid>
                <IonRow>
                  <IonCol size="6">
                    <IonButton
                      expand="block"
                      onClick={loadData}
                      fill="outline"
                    >
                      <IonIcon icon={refreshOutline} slot="start" />
                      Refresh
                    </IonButton>
                  </IonCol>
                  <IonCol size="6">
                    <IonButton
                      expand="block"
                      onClick={clearAllData}
                      color="danger"
                      fill="outline"
                    >
                      <IonIcon icon={trashOutline} slot="start" />
                      Clear All
                    </IonButton>
                  </IonCol>
                </IonRow>
              </IonGrid>
              
              <IonItem>
                <IonLabel>Auto Refresh (5s)</IonLabel>
                <IonBadge 
                  color={autoRefresh ? 'success' : 'medium'}
                  onClick={() => setAutoRefresh(!autoRefresh)}
                  style={{ cursor: 'pointer' }}
                >
                  {autoRefresh ? 'ON' : 'OFF'}
                </IonBadge>
              </IonItem>
            </IonCardContent>
          </IonCard>
        </div>

        <IonLoading isOpen={isLoading} message="Processing..." />

        <IonToast
          isOpen={showToast}
          onDidDismiss={() => setShowToast(false)}
          message={toastMessage}
          duration={3000}
          color={toastColor}
          position="bottom"
        />
      </IonContent>
    </IonPage>
  );
};

export default DatabaseMonitor;
