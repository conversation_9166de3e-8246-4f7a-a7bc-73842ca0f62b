import mongoose from 'mongoose';

/**
 * Database configuration optimized for high-performance websocket operations
 */
export const databaseConfig = {
  // Connection pool settings for high concurrency
  maxPoolSize: 50,        // Maximum number of connections
  minPoolSize: 5,         // Minimum number of connections
  maxIdleTimeMS: 30000,   // Close connections after 30 seconds of inactivity
  serverSelectionTimeoutMS: 5000, // How long to try selecting a server
  socketTimeoutMS: 45000, // How long a send or receive on a socket can take
  
  // Write concern for performance
  writeConcern: {
    w: 1,           // Acknowledge writes to primary only (faster)
    j: false,       // Don't wait for journal (faster, but less durable)
    wtimeout: 5000  // Timeout after 5 seconds
  },
  
  // Read preference for performance
  readPreference: 'primary',
  readConcern: { level: 'local' }
};

/**
 * Initialize database connection with performance optimizations
 */
export async function initializeDatabase(mongoUri: string): Promise<void> {
  try {
    // Set mongoose options for performance
    mongoose.set('strictQuery', false);
    mongoose.set('bufferCommands', false);
    
    await mongoose.connect(mongoUri, databaseConfig);
    
    console.log('✅ Database connected with performance optimizations');
    
    // Create indexes for better query performance
    await createPerformanceIndexes();
    
  } catch (error) {
    console.error('❌ Database connection failed:', error);
    throw error;
  }
}

/**
 * Create database indexes for optimal websocket performance
 */
async function createPerformanceIndexes(): Promise<void> {
  try {
    const db = mongoose.connection.db;
    
    // Apex Sessions indexes
    await db.collection('apexsessions').createIndexes([
      { key: { raceId: 1 }, background: true },
      { key: { isActive: 1 }, background: true },
      { key: { createdAt: -1 }, background: true }
    ]);
    
    // Apex Teams indexes (optimized schema - no teamId field)
    await db.collection('apexteams').createIndexes([
      { key: { sessionId: 1 }, background: true },
      { key: { sessionId: 1, name: 1 }, background: true },
      { key: { sessionId: 1, position: 1 }, background: true },
      { key: { sessionId: 1, status: 1 }, background: true }
    ]);
    
    // Apex Competitors indexes (optimized schema - uses websocketId instead of competitorId)
    await db.collection('apexcompetitors').createIndexes([
      { key: { sessionId: 1, websocketId: 1 }, unique: true, background: true },
      { key: { sessionId: 1, teamId: 1 }, background: true }
    ]);
    
    // Apex Laps indexes (optimized schema - no teamId field, uses kartId)
    await db.collection('apexlaps').createIndexes([
      { key: { sessionId: 1, competitorId: 1, lapNumber: 1 }, background: true },
      { key: { sessionId: 1, lapTime: 1 }, background: true },
      { key: { sessionId: 1, timestamp: -1 }, background: true },
      { key: { competitorId: 1, lapTime: 1 }, background: true },
      { key: { kartId: 1, lapTime: 1 }, background: true },
      { key: { isPersonalBest: 1, sessionId: 1 }, background: true },
      { key: { isBestLap: 1, sessionId: 1 }, background: true }
    ]);
    
    // Apex Karts indexes (optimized schema - no teamId field, uses currentTeamId)
    await db.collection('apexkarts').createIndexes([
      { key: { sessionId: 1, kartNumber: 1 }, unique: true, background: true },
      { key: { sessionId: 1, currentTeamId: 1 }, background: true },
      { key: { sessionId: 1, isActive: 1 }, background: true }
    ]);
    
    // Apex Pit Stops indexes (optimized schema - no teamId field, uses kartId)
    await db.collection('apexpitstops').createIndexes([
      { key: { sessionId: 1, competitorId: 1, pitInTime: -1 }, background: true },
      { key: { sessionId: 1, kartId: 1, pitInTime: -1 }, background: true }
    ]);
    
    console.log('✅ Performance indexes created');
    
  } catch (error) {
    console.error('⚠️ Error creating indexes:', error);
    // Don't throw - indexes are performance optimization, not critical
  }
}

/**
 * Get database performance statistics
 */
export async function getDatabaseStats(): Promise<any> {
  try {
    const db = mongoose.connection.db;
    const stats = await db.stats();
    
    return {
      collections: stats.collections,
      dataSize: stats.dataSize,
      indexSize: stats.indexSize,
      storageSize: stats.storageSize,
      connections: mongoose.connection.readyState,
      poolSize: mongoose.connection.db?.serverConfig?.s?.pool?.totalConnectionCount || 0
    };
  } catch (error) {
    console.error('Error getting database stats:', error);
    return null;
  }
}

/**
 * Monitor database performance
 */
export function startDatabaseMonitoring(): void {
  // Log connection events
  mongoose.connection.on('connected', () => {
    console.log('📊 Database connected');
  });
  
  mongoose.connection.on('error', (err) => {
    console.error('📊 Database error:', err);
  });
  
  mongoose.connection.on('disconnected', () => {
    console.log('📊 Database disconnected');
  });
  
  // Monitor slow operations (optional)
  if (process.env.NODE_ENV === 'development') {
    mongoose.set('debug', (collectionName: string, method: string, query: any, doc: any) => {
      const start = Date.now();
      console.log(`🔍 ${collectionName}.${method}`, JSON.stringify(query));
      
      // Log slow queries (>100ms)
      setTimeout(() => {
        const duration = Date.now() - start;
        if (duration > 100) {
          console.warn(`🐌 Slow query: ${collectionName}.${method} took ${duration}ms`);
        }
      }, 0);
    });
  }
}

export default {
  initializeDatabase,
  getDatabaseStats,
  startDatabaseMonitoring,
  databaseConfig
};
