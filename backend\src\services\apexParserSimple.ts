import { EventEmitter } from 'events';
import mongoose from 'mongoose';
import { parseLogMessage } from './apexMessageParser';
import { parseGridData, GridParseResult } from './apexGridParser';
import { ApexSession, ApexTeam, ApexKart, ApexCompetitor, ApexLap } from '../models/ApexModels';

export interface ApexParserConfig {
  sessionId?: string;
  raceId?: string;
  enableLogging?: boolean;
}

/**
 * Simplified Apex Parser
 *
 * Logic:
 * 1. Parse socket message
 * 2. If grid is found -> parse and create database elements
 * 3. If grid not found -> update existing database elements for old parsed grid
 * 4. Parse all received lines
 * 5. Queue messages during database transactions to prevent conflicts
 */
export class ApexParserSimple extends EventEmitter {
  private currentSession: any = null;
  private gridData: GridParseResult | null = null;
  private config: ApexParserConfig;
  private websocketToCompetitorMap: Map<string, mongoose.Types.ObjectId> = new Map(); // Map websocket ID to competitor ObjectId
  private messageQueue: Array<{ message: string; timestamp: Date }> = [];
  private isProcessingGrid: boolean = false;
  private isProcessingMessage: boolean = false;
  private raceTimeLeftMs: number = 0; // Race time left in milliseconds from dyn1
  private sessionStartTime: Date = new Date(); // When session started
  private raceDurationMs: number = 0; // Total race duration calculated from first dyn1
  private lastDyn1Timestamp: Date = new Date(); // When last dyn1 message was received
  private raceTimer: NodeJS.Timeout | null = null; // Internal timer to update race time

  // Performance monitoring
  private performanceStats: Map<string, { count: number; totalTime: number; maxTime: number }> = new Map();
  private slowOperationThreshold: number = 100; // Log operations taking more than 100ms

  // Performance optimization: Cache active pit stops to avoid repeated database queries
  private activePitStopsCache: Map<string, any> = new Map(); // competitorId -> activePitStop

  constructor(config: ApexParserConfig = {}) {
    super();
    this.config = {
      enableLogging: true,
      ...config
    };
  }

  /**
   * Performance monitoring helper
   */
  private async measurePerformance<T>(operationName: string, operation: () => Promise<T>): Promise<T> {
    const startTime = Date.now();
    try {
      const result = await operation();
      const duration = Date.now() - startTime;

      // Update performance stats
      const stats = this.performanceStats.get(operationName) || { count: 0, totalTime: 0, maxTime: 0 };
      stats.count++;
      stats.totalTime += duration;
      stats.maxTime = Math.max(stats.maxTime, duration);
      this.performanceStats.set(operationName, stats);

      // Log slow operations
      if (duration > this.slowOperationThreshold) {
        console.warn(`🐌 SLOW DB OPERATION: ${operationName} took ${duration}ms`);
      }

      if (this.config.enableLogging && duration > 50) {
        console.log(`⏱️ ${operationName}: ${duration}ms`);
      }

      return result;
    } catch (error) {
      const duration = Date.now() - startTime;
      console.error(`❌ DB OPERATION FAILED: ${operationName} failed after ${duration}ms:`, error);
      throw error;
    }
  }

  /**
   * Get performance statistics
   */
  getPerformanceStats(): Record<string, { count: number; avgTime: number; maxTime: number; totalTime: number }> {
    const stats: Record<string, { count: number; avgTime: number; maxTime: number; totalTime: number }> = {};

    for (const [operation, data] of this.performanceStats.entries()) {
      stats[operation] = {
        count: data.count,
        avgTime: Math.round(data.totalTime / data.count),
        maxTime: data.maxTime,
        totalTime: data.totalTime
      };
    }

    return stats;
  }

  /**
   * Log performance summary
   */
  logPerformanceSummary(): void {
    console.log('\n📊 DATABASE PERFORMANCE SUMMARY:');
    const stats = this.getPerformanceStats();

    // Sort by total time (most impactful operations first)
    const sortedStats = Object.entries(stats).sort((a, b) => b[1].totalTime - a[1].totalTime);

    for (const [operation, data] of sortedStats) {
      const impact = data.avgTime > 100 ? '🔴' : data.avgTime > 50 ? '🟡' : '🟢';
      console.log(`${impact} ${operation}: ${data.count} calls, avg: ${data.avgTime}ms, max: ${data.maxTime}ms, total: ${data.totalTime}ms`);
    }

    const totalOperations = Object.values(stats).reduce((sum, s) => sum + s.count, 0);
    const totalTime = Object.values(stats).reduce((sum, s) => sum + s.totalTime, 0);
    console.log(`\n📈 TOTAL: ${totalOperations} operations, ${totalTime}ms total time`);
  }

  /**
   * Parse log content (can be single line or multi-line)
   */
  async parseLogContent(content: string): Promise<void> {
    try {
      // Split content into lines and process each line
      const lines = content.split('\n').filter(line => line.trim().length > 0);
      
      if (this.config.enableLogging) {
        console.log(`📝 Processing ${lines.length} lines`);
      }

      for (const line of lines) {
        await this.parseMessage(line);
      }
    } catch (error) {
      console.error('Error parsing log content:', error);
      throw error;
    }
  }

  /**
   * Parse a single websocket message with transaction handling
   */
  async parseMessage(rawMessage: string): Promise<void> {
    try {
      if (this.config.enableLogging) {
        //console.log('📨 Parsing message:', rawMessage.substring(0, 100) + '...');
      }

      // If currently processing grid or another message, queue this message
      if (this.isProcessingGrid || this.isProcessingMessage) {
        this.messageQueue.push({ message: rawMessage, timestamp: new Date() });
        if (this.config.enableLogging) {
          console.log(`⏳ Message queued (${this.messageQueue.length} in queue) - Processing: grid=${this.isProcessingGrid}, message=${this.isProcessingMessage}`);
        }

        return;
      }

      // Parse the message
      const message = parseLogMessage(rawMessage);

      if (!message || !message.data) {
        if (this.config.enableLogging) {
          //console.warn('⚠️ Unknown message format, logging original message:');
          //console.warn('📝 Original message:', rawMessage);
        }
        return;
      }

      // Emit raw message event
      this.emit('messageReceived', { raw: rawMessage, parsed: message });

      // Simple logic: check if grid is present
      if (message.data.grid) {
        // Grid found -> parse and create database elements
        if (this.config.enableLogging) {
          console.log('🏁 Grid found - creating database elements');
        }
        this.isProcessingGrid = true;
        try {
          await this.handleGridMessage(message.data);
        } finally {
          this.isProcessingGrid = false;
        }
      } else {
        // No grid -> update existing database elements
        if (this.config.enableLogging) {
          console.log(`🔄 Processing update message (${Object.keys(message.data).length} fields)`);
        }
        this.isProcessingMessage = true;
        try {
          // Add timeout to prevent hanging
          const timeoutPromise = new Promise((_, reject) => {
            setTimeout(() => reject(new Error('Message processing timeout')), 5000); // 5 second timeout
          });

          await Promise.race([
            this.handleUpdateMessage(message.data),
            timeoutPromise
          ]);

          if (this.config.enableLogging) {
            console.log(`✅ Update message processed successfully`);
          }
        } catch (error) {
          console.error('❌ Error in handleUpdateMessage:', error);
          throw error; // Re-throw to be caught by outer try-catch
        } finally {
          this.isProcessingMessage = false;
          if (this.config.enableLogging) {
            console.log(`🔓 isProcessingMessage reset to false`);
          }
        }
      }

      // Process queued messages
      await this.processQueuedMessages();

    } catch (error) {
      console.error('Error parsing message:', error);
      console.error('📝 Original message that caused error:', rawMessage);
      // Reset processing flags on error
      this.isProcessingGrid = false;
      this.isProcessingMessage = false;
    }
  }

  /**
   * Process queued messages after current operation completes
   */
  private async processQueuedMessages(): Promise<void> {
    while (this.messageQueue.length > 0 && !this.isProcessingGrid && !this.isProcessingMessage) {
      const queuedMessage = this.messageQueue.shift();
      if (queuedMessage) {
        if (this.config.enableLogging) {
          console.log(`📤 Processing queued message (${this.messageQueue.length} remaining)`);
        }

        // Process the message directly without going through parseMessage to avoid re-queueing
        try {
          const message = parseLogMessage(queuedMessage.message);

          if (!message || !message.data) {
            continue; // Skip invalid messages
          }

          if (message.data.grid) {
            // Grid found -> parse and create database elements
            // Don't set processing flags here - we're already in queue processing
            await this.handleGridMessage(message.data);
          } else {
            // No grid -> update existing database elements
            // Don't set processing flags here - we're already in queue processing
            await this.handleUpdateMessage(message.data);
          }
        } catch (error) {
          console.error('Error processing queued message:', error);
          // Continue processing other messages even if one fails
        }
      }
    }
  }

  /**
   * Handle message with grid data - create database elements
   */
  private async handleGridMessage(messageData: Record<string, any>): Promise<void> {
    try {
      // Parse grid data
      if (messageData.grid) {
        if (this.config.enableLogging) {
          //console.log('📊 Parsing grid data...');
        }
        this.gridData = parseGridData(messageData.grid.value);
        if (this.config.enableLogging) {
          //console.log(`   Parsed ${Object.keys(this.gridData.drivers).length} drivers from grid`);
        }
      }

      // Create or update session
      await this.createSession(messageData);

      // Create teams, karts, and competitors from grid data
      if (this.gridData && this.currentSession) {
        await this.createEntitiesFromGrid();
      }

    } catch (error) {
      //console.error('Error handling grid message:', error);
    }
  }

  /**
   * Handle message without grid data - update existing database elements
   */
  private async handleUpdateMessage(messageData: Record<string, any>): Promise<void> {
    try {
      // Skip updates if no session with grid elements exists yet
      if (!this.currentSession || !this.gridData) {
        if (this.config.enableLogging) {
          //console.log('⚠️ Skipping updates - no session with grid elements found yet. Waiting for init message with grid.');
        }
        return;
      }

      // Process all fields as updates
      await this.processUpdates(messageData);

    } catch (error) {
      console.error('Error handling update message:', error);
    }
  }

  /**
   * Create NEW session from grid message data
   * Each grid message should create a new session
   */
  private async createSession(messageData: Record<string, any>): Promise<void> {
    try {
      const sessionData = {
        title1: messageData.title1?.value || 'Race Session',
        title2: messageData.title2?.value || new Date().toISOString(),
        track: messageData.track?.value || 'Unknown Track',
        isActive: true,
        gridData: this.gridData || {},
        sessionData: messageData || {}
      };

      // ALWAYS create a new session when grid message is found
      // This ensures each grid message starts a new session
      this.currentSession = await ApexSession.create(sessionData);

      // Clear websocket mapping for new session
      this.websocketToCompetitorMap.clear();

      // Reset race time tracking for new session
      this.sessionStartTime = new Date();
      this.raceTimeLeftMs = 0;
      this.raceDurationMs = 0;
      this.lastDyn1Timestamp = new Date();

      // Clear any existing timer
      if (this.raceTimer) {
        clearInterval(this.raceTimer);
        this.raceTimer = null;
      }

      // Removed general session logging - keeping only dyn1 and pit logging

      this.emit('sessionCreated', this.currentSession);
    } catch (error) {
      console.error('Error creating session:', error);
    }
  }

  /**
   * Populate websocket mapping from existing competitors
   */
  private async populateWebsocketMapping(): Promise<void> {
    if (!this.currentSession) return;

    try {
      const existingCompetitors = await ApexCompetitor.find({
        sessionId: this.currentSession._id
      }).lean();

      // Clear existing mapping first
      this.websocketToCompetitorMap.clear();

      // Populate the mapping
      existingCompetitors.forEach(competitor => {
        this.websocketToCompetitorMap.set(competitor.websocketId, competitor._id as mongoose.Types.ObjectId);
      });

      // Removed websocket mapping logging
    } catch (error) {
      console.error('Error populating websocket mapping:', error);
    }
  }

  /**
   * Create teams, karts, and competitors from grid data
   */
  private async createEntitiesFromGrid(): Promise<void> {
    if (!this.gridData || !this.currentSession) return;

    try {
      const sessionId = this.currentSession._id;

      // For new sessions, we always create fresh entities
      // No need to check for existing entities since each grid creates a new session
      const existingWebsocketIds = new Set(); // Empty set - always create new
      const existingKartNumbers = new Set(); // Empty set - always create new

      const teamsToCreate: any[] = [];
      const kartsToCreate: any[] = [];
      const competitorsToCreate: any[] = [];

      // First pass: Create teams and karts
      for (const [driverId, driverData] of Object.entries(this.gridData.drivers)) {
        const competitorId = driverId.replace('r', '');
        const teamName = driverData.dr?.value || `Team ${competitorId}`;
        const kartNumber = parseInt(driverData.no?.value || '0') || 0;
        const nationality = driverData.nat?.value || '';

        // Removed sample driver logging

        if (kartNumber > 0) {
          // Create team (if not exists)
          teamsToCreate.push({
            sessionId,
            name: teamName,
            currentKartId: null, // Will be set after kart creation
            // pastKarts removed - can be queried by currentTeamId in ApexKart collection
            // pits removed - can be queried by competitorId in ApexPitStop collection
            //drivers: [teamName], // Add team name as driver
            //nationality,
            //status: 'on_track',
            //isActive: true
          });

          // Create kart (if not exists)
          if (!existingKartNumbers.has(kartNumber)) {
            kartsToCreate.push({
              sessionId,
              kartNumber,
              speed: 4,
              currentTeamId: null, // Will be set after team creation
              currentRowId: null,
              //status: 'available',
              //isActive: true
            });
          }
        } else if (this.config.enableLogging) {
          console.warn(`⚠️ Skipping driver ${driverId} - invalid kart number: ${kartNumber} (raw: "${driverData.no?.value}")`);
        }
      }

      // Bulk create teams first
      let createdTeams: any[] = [];
      if (teamsToCreate.length > 0) {
        createdTeams = await this.measurePerformance(`ApexTeam.insertMany(${teamsToCreate.length})`, async () => {
          return await ApexTeam.insertMany(teamsToCreate);
        });
        // Removed team creation logging
      }

      // Bulk create karts
      let createdKarts: any[] = [];
      if (kartsToCreate.length > 0) {
        createdKarts = await this.measurePerformance(`ApexKart.insertMany(${kartsToCreate.length})`, async () => {
          return await ApexKart.insertMany(kartsToCreate);
        });
        // Removed kart creation logging
      }

      // Second pass: Create competitors with proper ObjectId references
      // Create maps to find teams and karts by their properties
      const teamsByName = new Map();
      const kartsByNumber = new Map();

      // Map ALL teams (both existing and newly created) by name
      const allTeams = await ApexTeam.find({ sessionId }).lean();
      allTeams.forEach(team => {
        teamsByName.set(team.name, team);
      });

      // Map ALL karts (both existing and newly created) by number
      const allKarts = await ApexKart.find({ sessionId }).lean();
      allKarts.forEach(kart => {
        kartsByNumber.set(kart.kartNumber, kart);
      });

      // Removed mapping logging

      let processedCount = 0;
      let validKartCount = 0;
      let matchedCount = 0;

      // Collect team and kart updates for batch processing
      const teamUpdates: Array<{ teamId: any; kartId: any }> = [];
      const kartUpdates: Array<{ kartId: any; teamId: any }> = [];

      if (this.config.enableLogging) {
        console.log(`🔄 Starting competitor creation loop for ${Object.keys(this.gridData.drivers).length} drivers`);
      }

      for (const [driverId, driverData] of Object.entries(this.gridData.drivers)) {
        const competitorId = driverId.replace('r', '');
        const teamName = driverData.dr?.value || `Team ${competitorId}`;
        const kartNumber = parseInt(driverData.no?.value || '0') || 0;
        const nationality = driverData.nat?.value || '';

        processedCount++;

        if (kartNumber > 0) {
          validKartCount++;

          if (!existingWebsocketIds.has(competitorId)) {
            const team = teamsByName.get(teamName);
            const kart = kartsByNumber.get(kartNumber);

            if (team && kart) {
              matchedCount++;
              competitorsToCreate.push({
                sessionId,
                websocketId: competitorId, // Store websocket ID for mapping
                teamId: team._id, // ObjectId reference
                kartId: kart._id, // ObjectId reference
                name: teamName,
                nationality,
                drivers: [teamName],
                isActive: true
              });

              // Collect updates for batch processing instead of individual updates
              teamUpdates.push({ teamId: team._id, kartId: kart._id });
              kartUpdates.push({ kartId: kart._id, teamId: team._id });

              if (this.config.enableLogging && (matchedCount < 3)) {
                console.warn(`⚠️ Could not find team "${teamName}" or kart ${kartNumber} for competitor ${competitorId}`);
                console.warn(`   Team found: ${!!team}, Kart found: ${!!kart}`);
                if (!team) console.warn(`   Available teams: ${Array.from(teamsByName.keys()).slice(0, 5).join(', ')}...`);
                if (!kart) console.warn(`   Available kart numbers: ${Array.from(kartsByNumber.keys()).slice(0, 10).join(', ')}...`);
              }
            }
          }
        } else if (this.config.enableLogging && (processedCount < 3)) {
          console.warn(`⚠️ Skipping competitor ${competitorId} - invalid kart number: ${kartNumber} (raw: "${driverData.no?.value}")`);
        }
      }

      // Removed competitor summary logging

      // Bulk create competitors
      if (competitorsToCreate.length > 0) {
        const createdCompetitors = await this.measurePerformance(`ApexCompetitor.insertMany(${competitorsToCreate.length})`, async () => {
          return await ApexCompetitor.insertMany(competitorsToCreate);
        });

        // Populate websocket to competitor ObjectId mapping
        createdCompetitors.forEach(competitor => {
          this.websocketToCompetitorMap.set(competitor.websocketId, competitor._id);
        });

        // Batch update teams and karts (much faster than individual updates)
        if (teamUpdates.length > 0) {
          await this.measurePerformance(`ApexTeam.bulkWrite(${teamUpdates.length})`, async () => {
            const bulkOps = teamUpdates.map(update => ({
              updateOne: {
                filter: { _id: update.teamId },
                update: { currentKartId: update.kartId }
              }
            }));
            return await ApexTeam.bulkWrite(bulkOps);
          });
        }

        if (kartUpdates.length > 0) {
          await this.measurePerformance(`ApexKart.bulkWrite(${kartUpdates.length})`, async () => {
            const bulkOps = kartUpdates.map(update => ({
              updateOne: {
                filter: { _id: update.kartId },
                update: { currentTeamId: update.teamId }
              }
            }));
            return await ApexKart.bulkWrite(bulkOps);
          });
        }

        // Removed competitor creation success logging
      } else {
        // Even if no new competitors were created, populate mapping from existing ones
        await this.populateWebsocketMapping();

        // Removed competitor creation warning logging
      }

      // Create apex karts for pit rows (optional - don't fail if this doesn't work)
      try {
        // Wait a moment to ensure session is fully committed
        await new Promise(resolve => setTimeout(resolve, 100));

        const { ApexPitRowService } = await import('./apexPitRowService');
        await ApexPitRowService.createApexKartsForPitRows(sessionId.toString());
        if (this.config.enableLogging) {
          console.log('✅ Created apex karts for pit rows');
        }
      } catch (pitRowError) {
        if (this.config.enableLogging) {
          const errorMessage = pitRowError instanceof Error ? pitRowError.message : String(pitRowError);
          console.warn('⚠️ Could not create apex karts for pit rows (this is optional):', errorMessage);
        }
        // Don't throw - this is optional functionality
      }

    } catch (error) {
      console.error('Error creating entities from grid:', error);
    }
  }

  /**
   * Process update messages for existing entities
   */
  private async processUpdates(messageData: Record<string, any>): Promise<void> {
    if (!this.currentSession) return;

    try {
      // Ensure websocket mapping is populated before processing updates (only once per session)
      if (this.websocketToCompetitorMap.size === 0 && this.currentSession) {
        await this.measurePerformance('populateWebsocketMapping', async () => {
          await this.populateWebsocketMapping();
        });
      }

      // Separate competitor fields from session fields for optimized processing
      const competitorFields: Array<[string, any]> = [];
      const sessionFields: Array<[string, any]> = [];

      for (const [key, data] of Object.entries(messageData)) {
        // Convert data to expected format if needed
        const fieldData = typeof data === 'object' && data.value !== undefined
          ? data
          : { type: key, value: String(data) };

        if (key.startsWith('r') && key.includes('c')) {
          competitorFields.push([key, fieldData]);
        } else {
          sessionFields.push([key, fieldData]);
        }
      }

      // Process session fields first (usually fewer and more important)
      for (const [key, fieldData] of sessionFields) {
        if (this.config.enableLogging && key === 'dyn1') {
          console.log(`🔄 Processing session field: ${key}`);
        }
        await this.handleSessionUpdate(key, fieldData);
      }

      // Process competitor fields in batches for better performance
      const batchSize = 10; // Process 10 competitor fields at a time
      for (let i = 0; i < competitorFields.length; i += batchSize) {
        const batch = competitorFields.slice(i, i + batchSize);

        // Process batch in parallel
        await Promise.all(batch.map(async ([key, fieldData]) => {
          try {
            if (this.config.enableLogging && (key.includes('c1') || key.includes('c14'))) {
              console.log(`🔄 Processing field: ${key}`);
            }
            await this.handleDriverUpdate(key, fieldData);
          } catch (error) {
            console.error(`Error processing field ${key}:`, error);
          }
        }));
      }
    } catch (error) {
      console.error('Error processing updates:', error);
    }
  }

  /**
   * Handle driver/competitor updates
   */
  private async handleDriverUpdate(key: string, data: any): Promise<void> {
    // Extract competitor ID and column from key (e.g., "r45393c4" -> competitorId: "45393", column: "c4")
    const match = key.match(/r(\d+)c(\d+)/);
    if (!match) return;

    const competitorId = match[1];
    const columnId = `c${match[2]}`;

    // Get the field type from grid header if available
    const fieldType = this.gridData?.header_types[columnId] || 'unknown';

    if (this.config.enableLogging) {
      //console.log(`Updating competitor ${competitorId}, column ${columnId}, field ${fieldType}: ${data.value}`);
      if (fieldType === 'unknown') {
        console.log(`🔍 Available grid header types:`, this.gridData?.header_types);
      }
    }

    // Handle different field types
    if (this.config.enableLogging) {
      //console.log(`🔍 Processing field type: '${fieldType}' for competitor: ${competitorId} with value: '${data.value}'`);
    }

    // Handle field types based on raceService.js logic
    switch (fieldType) {
      case 'llp': // Last lap time column
      case 'blp': // Best lap time column
        // Check the data type to determine what kind of lap time this is
        if (competitorId && data.value) {
          await this.handleLapTimeByType(competitorId, data.type, data.value, fieldType);
        }
        break;
      case 'tlp': // Total laps column
        if (competitorId && data.value && data.type === 'in') {
          await this.handleLapCountUpdate(competitorId, data.value);
        }
        break;
      case 'pit': // Pit column
        if (this.config.enableLogging) {
          console.log(`🔍 PIT FIELD DETECTED: competitorId=${competitorId}, data.type=${data.type}, data.value=${data.value}`);
        }

        if (competitorId && data.value) {
          // data-type="pit" represents total pit number, not pit status
          // This is just a counter of total pit stops, not entry/exit events
          if (this.config.enableLogging) {
            console.log(`🏁 Pit count update: ${competitorId} -> ${data.value} (total pit stops)`);
          }
          await this.handlePitCountUpdate(competitorId, data.value);
        } else {
          if (this.config.enableLogging) {
            console.log(`⚠️ Pit field detected but missing data: competitorId=${competitorId}, value=${data.value}`);
          }
        }
        break;
      case 'otr': // On track time column
        if (competitorId && data.value) {
          if (this.config.enableLogging) {
            //console.log(`🔍 OTR FIELD DETECTED: competitorId=${competitorId}, data.type=${data.type}, data.value=${data.value}`);
          }

          if (data.type === 'to') {
            // Pit current duration from 24h serres format: r id|c14|to|ss (or mm:ss)
            if (this.config.enableLogging) {
              console.log(`⏱️ Pit current duration from otr: ${competitorId} -> ${data.value} (type: ${data.type})`);
            }
            await this.handlePitCurrentDurationUpdate(competitorId, data.value);
          } else {
            // Other otr types - time on track
            await this.handleTimeOnTrackUpdate(competitorId, data.value);
          }
        }
        break;
      case 'sta': // Status column
        if (competitorId) {
          if (data.type === 'si') {
            // Pit in: r17768c1|si|
            if (this.config.enableLogging) {
              console.log(`🏁 PIT IN detected: ${competitorId} (status field si)`);
            }
            await this.handlePitStatusUpdate(competitorId, 'IN');
          } else if (data.type === 'so') {
            // Pit out: r17778c1|so|
            if (this.config.enableLogging) {
              console.log(`🏁 PIT OUT detected: ${competitorId} (status field so)`);
            }
            await this.handlePitStatusUpdate(competitorId, 'OUT');
          } else if (data.value) {
            await this.handleStatusUpdate(competitorId, data.value);
          }
        }
        break;
      default:
        // Check if this might be a pit update based on the value (fallback)
        if (competitorId && data.value && (data.value === 'IN' || data.value === 'OUT')) {
          if (this.config.enableLogging) {
            console.log(`🏁 Potential pit event detected (unknown field type '${fieldType}'): ${competitorId} -> ${data.value}`);
          }
          await this.handlePitStatusUpdate(competitorId, data.value);
        } else if (this.config.enableLogging) {
          //console.log(`⚠️ Unhandled field type: '${fieldType}' with data type: '${data.type}' for competitor: ${competitorId} with value: '${data.value}'`);
        }
        break;
    }
  }

  /**
   * Handle session-level updates
   */
  private async handleSessionUpdate(key: string, data: any): Promise<void> {
    if (!this.currentSession) {
      if (this.config.enableLogging) {
        console.log(`⚠️ handleSessionUpdate: No current session for key ${key}`);
      }
      return;
    }

    // Skip competitor fields - they shouldn't be stored in session
    if (key.startsWith('r') && key.includes('c')) {
      if (this.config.enableLogging) {
        console.log(`⚠️ Skipping session update for competitor field: ${key}`);
      }
      return;
    }

    if (this.config.enableLogging) {
      console.log(`🔄 handleSessionUpdate: Processing ${key} = ${data.value}`);
    }

    try {
      // Handle special session fields
      if (key === 'dyn1' && data.value) {
        if (this.config.enableLogging) {
          console.log(`⏱️ Processing dyn1 update: ${data.value}`);
        }
        await this.handleRaceTimeUpdate(data.value);
        // Don't store dyn1 in session - it's handled by race time tracking
        return;
      }

      // Only update session for actual session-level fields (title1, title2, track, etc.)
      const sessionFields = ['title1', 'title2', 'track', 'status', 'weather', 'temperature'];
      if (sessionFields.includes(key)) {
        if (this.config.enableLogging) {
          console.log(`💾 Updating session field ${key} in database`);
        }
        await this.measurePerformance(`ApexSession.findByIdAndUpdate(${key})`, async () => {
          return await ApexSession.findByIdAndUpdate(this.currentSession._id, {
            [key]: data.value,
            updatedAt: new Date()
          });
        });

        if (this.config.enableLogging) {
          console.log(`✅ handleSessionUpdate: Completed ${key}`);
        }
      } else {
        if (this.config.enableLogging) {
          console.log(`⚠️ Skipping unknown session field: ${key}`);
        }
      }
    } catch (error) {
      console.error(`Error updating session field ${key}:`, error);
    }
  }

  /**
   * Handle race time updates from dyn1 messages
   * dyn1 contains milliseconds left in the race
   * Implements a countdown timer that updates between dyn1 messages
   */
  private async handleRaceTimeUpdate(timeValue: string): Promise<void> {
    try {
      // Parse time value - could be in format "HH:MM:SS" or milliseconds
      let timeLeftMs = 0;

      if (timeValue.includes(':')) {
        // Format: "HH:MM:SS" or "MM:SS"
        const parts = timeValue.split(':').map(p => parseInt(p) || 0);
        if (parts.length === 3 && parts[0] !== undefined && parts[1] !== undefined && parts[2] !== undefined) {
          // HH:MM:SS
          timeLeftMs = (parts[0] * 3600 + parts[1] * 60 + parts[2]) * 1000;
        } else if (parts.length === 2 && parts[0] !== undefined && parts[1] !== undefined) {
          // MM:SS
          timeLeftMs = (parts[0] * 60 + parts[1]) * 1000;
        }
      } else {
        // Assume milliseconds
        timeLeftMs = parseInt(timeValue) || 0;
      }

      // Update race time tracking
      this.raceTimeLeftMs = timeLeftMs;
      this.lastDyn1Timestamp = new Date();

      // Calculate total race duration from first dyn1 message
      if (this.raceDurationMs === 0 && timeLeftMs > 0) {
        this.raceDurationMs = timeLeftMs;
        if (this.config.enableLogging) {
          console.log(`⏱️ Race duration set: ${this.formatTime(this.raceDurationMs)} (${this.raceDurationMs}ms)`);
        }

        // Start the countdown timer
        this.startRaceTimer();

        // Update any existing pit stops that were created before dyn1 data was available
        await this.updatePitStopsWithRaceTime();
      }

      if (this.config.enableLogging) {
        console.log(`⏱️ dyn1 update - Race time left: ${this.formatTime(timeLeftMs)} (${timeLeftMs}ms)`);
      }

    } catch (error) {
      console.error('Error handling race time update:', error);
    }
  }

  /**
   * Start internal race timer that counts down every second
   */
  private startRaceTimer(): void {
    // Clear any existing timer
    if (this.raceTimer) {
      clearInterval(this.raceTimer);
    }

    // Start new timer that updates every 100ms for precision
    this.raceTimer = setInterval(() => {
      if (this.raceTimeLeftMs > 0) {
        // Calculate time elapsed since last dyn1 message
        const now = new Date();
        const elapsedSinceLastDyn1 = now.getTime() - this.lastDyn1Timestamp.getTime();

        // Update race time left by subtracting elapsed time
        const newTimeLeft = Math.max(0, this.raceTimeLeftMs - elapsedSinceLastDyn1);

        // Only update if there's a significant change (avoid constant updates)
        if (Math.abs(newTimeLeft - this.raceTimeLeftMs) > 100) {
          this.raceTimeLeftMs = newTimeLeft;
          this.lastDyn1Timestamp = now;
        }

        // Stop timer if race is over
        if (this.raceTimeLeftMs <= 0) {
          if (this.raceTimer) {
            clearInterval(this.raceTimer);
            this.raceTimer = null;
          }
          if (this.config.enableLogging) {
            console.log('⏱️ Race timer stopped - race finished');
          }
        }
      }
    }, 100); // Update every 100ms
  }

  /**
   * Format milliseconds to HH:MM:SS
   */
  private formatTime(ms: number): string {
    const totalSeconds = Math.floor(ms / 1000);
    const hours = Math.floor(totalSeconds / 3600);
    const minutes = Math.floor((totalSeconds % 3600) / 60);
    const seconds = totalSeconds % 60;

    if (hours > 0) {
      return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    } else {
      return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    }
  }

  /**
   * Convert lap time string to milliseconds
   */
  private convertLapTimeToMs(lapTimeStr: string): number | null {
    if (!lapTimeStr || lapTimeStr === '') return null;

    try {
      // Handle formats like "1:11.328", "71.328", "1:11:328"
      const timeStr = lapTimeStr.trim();

      // Format: "1:11.328" (minutes:seconds.milliseconds)
      if (timeStr.includes(':') && timeStr.includes('.')) {
        const parts = timeStr.split(':');
        if (parts.length === 2 && parts[0] && parts[1]) {
          const minutes = parseInt(parts[0]);
          const secondsParts = parts[1].split('.');
          if (secondsParts.length === 2 && secondsParts[0] && secondsParts[1]) {
            const seconds = parseInt(secondsParts[0]);
            const milliseconds = parseInt(secondsParts[1].padEnd(3, '0').substring(0, 3));

            return (minutes * 60 * 1000) + (seconds * 1000) + milliseconds;
          }
        }
      }

      // Format: "71.328" (seconds.milliseconds)
      if (timeStr.includes('.') && !timeStr.includes(':')) {
        const parts = timeStr.split('.');
        if (parts.length === 2 && parts[0] && parts[1]) {
          const seconds = parseInt(parts[0]);
          const milliseconds = parseInt(parts[1].padEnd(3, '0').substring(0, 3));

          return (seconds * 1000) + milliseconds;
        }
      }

      // Format: just seconds as string
      const numericValue = parseFloat(timeStr);
      if (!isNaN(numericValue)) {
        return Math.round(numericValue * 1000); // Convert seconds to milliseconds
      }

      return null;
    } catch (error) {
      console.warn(`Error converting lap time "${lapTimeStr}":`, error);
      return null;
    }
  }

  /**
   * Handle lap time updates
   */
  private async handleLapTimeUpdate(websocketId: string, _fieldType: string, lapTime: string): Promise<void> {
    if (!lapTime || lapTime === '') return;

    try {
      // Get competitor ObjectId from websocket mapping (optimized)
      let competitorObjectId = this.websocketToCompetitorMap.get(websocketId);
      if (!competitorObjectId) {
        // If not in map, competitor might not exist or mapping is incomplete
        if (this.config.enableLogging) {
          console.warn(`⚠️ Competitor not found in mapping: ${websocketId}`);
        }
        return; // Skip processing instead of expensive database lookup
      }

      // Convert lap time string to milliseconds (Number) - do this first to avoid unnecessary DB calls
      const lapTimeMs = this.convertLapTimeToMs(lapTime);
      if (lapTimeMs === null) {
        if (this.config.enableLogging) {
          console.warn(`⚠️ Invalid lap time format: ${lapTime}`);
        }
        return;
      }

      const { ApexLap, ApexCompetitor } = await import('../models/ApexModels');

      // Get competitor info (cached from websocket mapping, so this should be fast)
      const competitor = await ApexCompetitor.findById(competitorObjectId).lean(); // Use lean() for better performance
      if (!competitor) {
        return;
      }

      // Skip expensive count query - let database auto-increment or use a simpler approach
      // We'll create the lap without explicit lap number and let the frontend calculate it if needed

      // Create lap record with required lapNumber (use timestamp as simple incrementing number)
      const lapData = {
        sessionId: this.currentSession._id,
        competitorId: competitor._id, // ObjectId reference to competitor
        kartId: competitor.kartId, // ObjectId reference to kart
        lapNumber: Date.now(), // Use timestamp as unique lap identifier (avoids expensive count query)
        lapTime: lapTimeMs // Required Number field (milliseconds)
        // lapTimeFormatted removed - frontend can format
        // isBestLap removed - frontend can calculate
        // isPersonalBest removed - frontend can calculate
        // timestamp removed - use createdAt
      };

      await ApexLap.create(lapData);

      // Removed lap recording logging
    } catch (error) {
      console.error('Error handling lap time update:', error);
    }
  }

  /**
   * Handle status updates
   */
  private async handleStatusUpdate(websocketId: string, status: string): Promise<void> {
    try {
      // Get competitor ObjectId from websocket mapping
      let competitorObjectId = this.websocketToCompetitorMap.get(websocketId);
      if (!competitorObjectId) {
        // Try to find competitor by websocketId if not in map
        const competitor = await ApexCompetitor.findOne({
          sessionId: this.currentSession._id,
          websocketId
        });

        if (competitor) {
          // Add to map for future use
          this.websocketToCompetitorMap.set(websocketId, competitor._id as mongoose.Types.ObjectId);
          competitorObjectId = competitor._id as mongoose.Types.ObjectId;
        } else {
          // Removed competitor not found logging
          return;
        }
      }

      // Find competitor to get kart info
      const competitor = await ApexCompetitor.findById(competitorObjectId);
      if (!competitor) return;

      // Update kart status
      await ApexKart.updateOne(
        {
          sessionId: this.currentSession._id,
          _id: competitor.kartId
        },
        {
          status: status === 'PIT' ? 'in_pit_row' : 'on_track',
          updatedAt: new Date()
        }
      );
    } catch (error) {
      console.error('Error handling status update:', error);
    }
  }

  /**
   * Handle pit updates - create pitstop records for IN/OUT events
   */
  private async handlePitUpdate(websocketId: string, pitStatus: string): Promise<void> {
    if (this.config.enableLogging) {
      console.log(`🏁 handlePitUpdate called: websocketId=${websocketId}, pitStatus=${pitStatus}`);
    }

    if (!this.currentSession) {
      if (this.config.enableLogging) {
        console.warn(`No current session for pit update`);
      }
      return;
    }

    try {
      // Get competitor ObjectId from websocket mapping
      const competitorObjectId = this.websocketToCompetitorMap.get(websocketId);
      if (!competitorObjectId) {
        if (this.config.enableLogging) {
          console.warn(`❌ No competitor mapping found for websocket ID: ${websocketId}`);
          console.warn(`📋 Available mappings (${this.websocketToCompetitorMap.size} total):`, Array.from(this.websocketToCompetitorMap.keys()));
          console.warn(`🔍 Pit status was: ${pitStatus}`);
        }
        return;
      }

      if (this.config.enableLogging) {
        console.log(`✅ Found competitor mapping: ${websocketId} -> ${competitorObjectId}`);
      }

      // Find competitor to get kart info
      const competitor = await ApexCompetitor.findById(competitorObjectId);
      if (!competitor) {
        if (this.config.enableLogging) {
          console.warn(`No competitor found for ObjectId: ${competitorObjectId}`);
        }
        return;
      }

      if (this.config.enableLogging) {
        console.log(`🏁 Processing pit ${pitStatus} for competitor: ${competitor.name} (ID: ${competitor._id})`);
        console.log(`🔍 Pit status type check: isNumeric=${this.isPitStatusNumber(pitStatus)}, value="${pitStatus}"`);
      }

      // Handle different pit status formats
      if (pitStatus === 'IN') {
        // Handle pit entry (string format)
        if (this.config.enableLogging) {
          console.log(`🏁 Handling pit entry (string): ${competitor.name}`);
        }
        await this.handlePitEntry(competitor, competitorObjectId);
      } else if (pitStatus === 'OUT') {
        // Handle pit exit (string format)
        if (this.config.enableLogging) {
          console.log(`🏁 Handling pit exit (string): ${competitor.name}`);
        }
        await this.handlePitExit(competitor, competitorObjectId);
      } else if (this.isPitStatusNumber(pitStatus)) {
        // Handle numeric pit status (e.g., "6" means in pit for 6 seconds)
        if (this.config.enableLogging) {
          console.log(`🏁 Handling numeric pit status: ${competitor.name} -> ${pitStatus} seconds`);
        }
        await this.handleNumericPitStatus(competitor, competitorObjectId, pitStatus);
      } else {
        if (this.config.enableLogging) {
          console.warn(`❌ Unknown pit status: "${pitStatus}" for ${competitor.name}`);
          console.warn(`   Status type: ${typeof pitStatus}, length: ${pitStatus.length}`);
          console.warn(`   ASCII codes: ${Array.from(pitStatus).map(c => c.charCodeAt(0)).join(', ')}`);
        }
      }

      // Also update kart status
      await this.handleStatusUpdate(websocketId, pitStatus === 'IN' ? 'PIT' : 'ON TRACK');

      if (this.config.enableLogging) {
        console.log(`🏁 Pit ${pitStatus} completed: ${competitor.name} (Team ${competitor.teamId})`);
      }
    } catch (error) {
      console.error('Error handling pit update:', error);
      if (error instanceof Error) {
        console.error('Stack trace:', error.stack);
      }
    }
  }

  /**
   * Handle pit entry - create new pitstop record
   */
  private async handlePitEntry(competitor: any, competitorObjectId: mongoose.Types.ObjectId): Promise<void> {
    try {
      if (this.config.enableLogging) {
        console.log(`🏁 Creating pit entry for: ${competitor.name} (ID: ${competitorObjectId})`);
        console.log(`   Session ID: ${this.currentSession._id}`);
        console.log(`   Kart ID: ${competitor.kartId}`);
      }

      const { ApexPitStop } = await import('../models/ApexModels');

      // Get current race time from session start
      const raceTimeAtPitIn = this.getCurrentRaceTime();

      // Create pit entry record (removed lapNumber as per requirements)
      const pitStopData = {
        sessionId: this.currentSession._id,
        competitorId: competitorObjectId,
        kartId: competitor.kartId,
        pitInTime: new Date(),
        raceTimeAtPitIn: raceTimeAtPitIn, // Total race time when entering pit
        pitCurrentDuration: 0, // Will be updated in real-time
      };

      if (this.config.enableLogging) {
        console.log(`🔍 Pit stop data to create:`, JSON.stringify(pitStopData, null, 2));
      }

      const createdPitStop = await this.measurePerformance('ApexPitStop.create', async () => {
        return await ApexPitStop.create(pitStopData);
      });

      // Update team status to in_pit
      await this.updateTeamStatus(competitorObjectId, 'in_pit');

      if (this.config.enableLogging) {
        console.log(`✅ Pit entry recorded successfully: ${competitor.name}`);
        console.log(`   PitStop ID: ${createdPitStop._id}`);
        console.log(`   Race time: ${raceTimeAtPitIn}s`);
        console.log(`   Pit in time: ${createdPitStop.pitInTime}`);
        console.log(`   Team status updated to: in_pit`);
      }
    } catch (error) {
      console.error('Error handling pit entry:', error);
      if (error instanceof Error) {
        console.error('Stack trace:', error.stack);
      }
    }
  }

  /**
   * Handle pit exit - update existing pitstop record
   */
  private async handlePitExit(competitor: any, competitorObjectId: mongoose.Types.ObjectId): Promise<void> {
    try {
      if (this.config.enableLogging) {
        console.log(`🏁 Processing pit exit for: ${competitor.name}`);
      }

      const { ApexPitStop } = await import('../models/ApexModels');

      // Find the most recent pit entry without exit time
      const activePitStop = await this.measurePerformance('ApexPitStop.findOne(activePit)', async () => {
        return await ApexPitStop.findOne({
          sessionId: this.currentSession._id,
          competitorId: competitorObjectId,
          pitOutTime: { $exists: false }
        }).sort({ pitInTime: -1 });
      });

      if (activePitStop) {
        const pitOutTime = new Date();
        const pitDuration = (pitOutTime.getTime() - activePitStop.pitInTime.getTime()) / 1000; // seconds
        const raceTimeAtPitOut = this.getCurrentRaceTime();

        // Calculate total duration from all previous completed pit stops + this one
        const previousPitStops = await ApexPitStop.find({
          sessionId: this.currentSession._id,
          competitorId: competitorObjectId,
          pitDuration: { $exists: true },
          _id: { $ne: activePitStop._id } // Exclude current pit stop
        });

        const previousTotalDuration = previousPitStops.reduce((total, pit) => total + (pit.pitDuration || 0), 0);
        const newPitTotalDuration = previousTotalDuration + pitDuration;

        await this.measurePerformance('ApexPitStop.updateOne(pitExit)', async () => {
          return await ApexPitStop.updateOne(
            { _id: activePitStop._id },
            {
              pitOutTime: pitOutTime,
              pitDuration: pitDuration,
              raceTimeAtPitOut: raceTimeAtPitOut, // Total race time when exiting pit
              pitCurrentDuration: 0, // Reset current duration as pit stop is complete
              pitTotalDuration: newPitTotalDuration, // Calculate at pit out as requested
              isActive: false // Mark as completed
            }
          );
        });

        // Update team status to on_track
        await this.updateTeamStatus(competitorObjectId, 'on_track');

        if (this.config.enableLogging) {
          console.log(`🏁 Pit exit recorded: ${competitor.name} - Duration: ${pitDuration.toFixed(1)}s - Total: ${newPitTotalDuration.toFixed(1)}s - Race time: ${raceTimeAtPitOut}s`);
          console.log(`   Team status updated to: on_track`);
        }
      } else {
        if (this.config.enableLogging) {
          console.warn(`No active pit stop found for ${competitor.name} to exit`);
        }
      }
    } catch (error) {
      console.error('Error handling pit exit:', error);
      if (error instanceof Error) {
        console.error('Stack trace:', error.stack);
      }
    }
  }

  /**
   * Check if pit status is a number (indicating pit duration)
   */
  private isPitStatusNumber(pitStatus: string): boolean {
    const num = parseInt(pitStatus);
    return !isNaN(num) && num >= 0;
  }

  /**
   * Handle numeric pit status (e.g., "6" means in pit for 6 seconds)
   */
  private async handleNumericPitStatus(competitor: any, competitorObjectId: mongoose.Types.ObjectId, pitStatus: string): Promise<void> {
    try {
      const { ApexPitStop } = await import('../models/ApexModels');
      const pitDurationSeconds = parseInt(pitStatus);

      if (this.config.enableLogging) {
        console.log(`🏁 Numeric pit status: ${competitor.name} - ${pitDurationSeconds} seconds in pit`);
        console.log(`🔍 Checking for existing active pit stop...`);
      }

      // Check if there's an active pit stop for this competitor
      let activePitStop = await ApexPitStop.findOne({
        sessionId: this.currentSession._id,
        competitorId: competitorObjectId,
        pitOutTime: { $exists: false }
      }).sort({ pitInTime: -1 });

      if (this.config.enableLogging) {
        console.log(`🔍 Active pit stop check: ${activePitStop ? 'FOUND' : 'NOT FOUND'} for ${competitor.name}`);
        if (activePitStop) {
          console.log(`   Existing pit stop ID: ${activePitStop._id}, pitInTime: ${activePitStop.pitInTime}`);
        }
      }

      if (!activePitStop && pitDurationSeconds > 0) {
        // No active pit stop but we have duration - create pit entry
        if (this.config.enableLogging) {
          console.log(`🏁 Creating new pit entry for ${competitor.name} (duration: ${pitDurationSeconds}s)`);
        }
        await this.handlePitEntry(competitor, competitorObjectId);

        // Get the newly created pit stop
        activePitStop = await ApexPitStop.findOne({
          sessionId: this.currentSession._id,
          competitorId: competitorObjectId,
          pitOutTime: { $exists: false }
        }).sort({ pitInTime: -1 });

        if (this.config.enableLogging) {
          console.log(`🔍 After pit entry creation: ${activePitStop ? 'SUCCESS' : 'FAILED'}`);
          if (activePitStop) {
            console.log(`   New pit stop ID: ${activePitStop._id}`);
          }
        }
      }

      if (activePitStop) {
        // Update current duration
        await ApexPitStop.updateOne(
          { _id: activePitStop._id },
          {
            pitCurrentDuration: pitDurationSeconds,
            // Update total duration as well (current + previous)
            pitTotalDuration: (activePitStop.pitTotalDuration || 0) + pitDurationSeconds
          }
        );

        if (this.config.enableLogging) {
          console.log(`⏱️ Updated pit duration: ${competitor.name} - current: ${pitDurationSeconds}s`);
        }
      }

      // If duration is 0, it might mean pit exit
      if (pitDurationSeconds === 0 && activePitStop) {
        await this.handlePitExit(competitor, competitorObjectId);
      }

    } catch (error) {
      console.error('Error handling numeric pit status:', error);
    }
  }

  /**
   * Handle pit current duration updates from otr field (24h serres format)
   * Format: r id|c14|to|ss (or mm:ss)
   * This updates pitCurrentDuration for active pit stops
   */
  private async handlePitCurrentDurationUpdate(websocketId: string, durationValue: string): Promise<void> {
    if (!this.currentSession) {
      if (this.config.enableLogging) {
        console.log(`⚠️ handlePitCurrentDurationUpdate: No current session`);
      }
      return;
    }

    // Reduced logging for performance
    if (this.config.enableLogging && durationValue.includes(':')) {
      console.log(`🔄 handlePitCurrentDurationUpdate: Starting for ${websocketId} with value ${durationValue}`);
    }

    try {
      // Get competitor ObjectId from websocket mapping
      const competitorObjectId = this.websocketToCompetitorMap.get(websocketId);
      if (!competitorObjectId) {
        if (this.config.enableLogging) {
          console.warn(`No competitor mapping found for websocket ID: ${websocketId}`);
        }
        return;
      }

      // Find competitor
      const competitor = await ApexCompetitor.findById(competitorObjectId);
      if (!competitor) return;

      // Parse duration from 24h serres format: ss or mm:ss
      const durationSeconds = this.parsePitCurrentDuration(durationValue);
      if (durationSeconds === null) {
        if (this.config.enableLogging) {
          console.warn(`Invalid pit current duration format: ${durationValue}`);
        }
        return;
      }

      const { ApexPitStop } = await import('../models/ApexModels');

      // Find active pit stop for this competitor
      const activePitStop = await ApexPitStop.findOne({
        sessionId: this.currentSession._id,
        competitorId: competitorObjectId,
        pitOutTime: { $exists: false }
      }).sort({ pitInTime: -1 });

      if (activePitStop) {
        // Update current duration from otr field
        await ApexPitStop.updateOne(
          { _id: activePitStop._id },
          { pitCurrentDuration: durationSeconds }
        );

        // Update team status to in_pit
        await this.updateTeamStatus(competitorObjectId, 'in_pit');

        if (this.config.enableLogging) {
          console.log(`⏱️ Updated pit current duration from otr: ${competitor.name} - ${durationSeconds}s`);
        }
      } else if (durationSeconds > 0) {
        // No active pit stop but we have duration - don't create automatically
        // Wait for explicit pit in message instead of assuming pit entry
        if (this.config.enableLogging) {
          console.log(`⚠️ Pit duration update for ${competitor.name} but no active pit stop found - skipping (waiting for pit in message)`);
        }
        return;
      } else if (durationSeconds === 0) {
        // Duration is 0 - competitor might be exiting pit
        await this.updateTeamStatus(competitorObjectId, 'on_track');
      }

    } catch (error) {
      console.error('Error handling pit duration update:', error);
    }

    // Reduced logging for performance
    if (this.config.enableLogging && durationValue.includes(':')) {
      console.log(`✅ handlePitCurrentDurationUpdate: Completed for ${websocketId}`);
    }
  }

  /**
   * Parse pit current duration from 24h serres format
   * Format: r id|c14|to|ss. or r id|c14|to|mm:ss.
   * Examples: "05." = 5 seconds, "2:30." = 150 seconds, "00." = 0 seconds
   */
  private parsePitCurrentDuration(durationValue: string): number | null {
    if (!durationValue) return null;

    try {
      // Remove trailing dot and trim
      const cleanValue = durationValue.replace(/\.$/, '').trim();

      // Format: mm:ss (like "2:30")
      if (cleanValue.includes(':')) {
        const parts = cleanValue.split(':');
        if (parts.length === 2 && parts[0] && parts[1]) {
          const minutes = parseInt(parts[0]) || 0;
          const seconds = parseInt(parts[1]) || 0;
          return (minutes * 60) + seconds;
        }
      }

      // Format: ss (just seconds, like "05" or "00")
      const seconds = parseInt(cleanValue);
      return isNaN(seconds) ? null : seconds;
    } catch (error) {
      return null;
    }
  }

  /**
   * Update team status (on_track or in_pit)
   */
  private async updateTeamStatus(competitorObjectId: mongoose.Types.ObjectId, status: 'on_track' | 'in_pit'): Promise<void> {
    try {
      // Find competitor to get team info
      const competitor = await ApexCompetitor.findById(competitorObjectId);
      if (!competitor) return;

      // Update team status
      await ApexTeam.updateOne(
        {
          sessionId: this.currentSession._id,
          _id: competitor.teamId
        },
        {
          status: status,
          updatedAt: new Date()
        }
      );

      if (this.config.enableLogging) {
        console.log(`🏁 Updated team status: ${competitor.name} -> ${status}`);
      }
    } catch (error) {
      console.error('Error updating team status:', error);
    }
  }

  /**
   * Handle lap time updates based on data type (following raceService.js logic)
   */
  private async handleLapTimeByType(websocketId: string, dataType: string, lapTime: string, fieldType: string): Promise<void> {
    if (!lapTime || lapTime === '') return;

    const dataTypeLower = dataType.toLowerCase();

    // Based on raceService.js logic:
    // ti = personal best time, tn = normal time, tb = best time in race, ib = best lap
    if ((dataTypeLower === 'ti' || dataTypeLower === 'tn' || dataTypeLower === 'tb') && fieldType === 'llp') {
      // Last lap time
      await this.handleLapTimeUpdate(websocketId, dataType, lapTime);
    } else if ((dataTypeLower === 'ti' || dataTypeLower === 'ib' || dataTypeLower === 'in') && fieldType === 'blp') {
      // Best lap time
      await this.handleLapTimeUpdate(websocketId, dataType, lapTime);
    }
  }

  /**
   * Handle lap count updates
   */
  private async handleLapCountUpdate(websocketId: string, lapCount: string): Promise<void> {
    // This is handled automatically by counting laps in the database
    // No need to store lap count separately as per your requirements
    // Removed lap count logging
  }

  /**
   * Handle pit count updates (number of pit stops)
   */
  private async handlePitCountUpdate(websocketId: string, pitCount: string): Promise<void> {
    // This is handled automatically by counting pit stops in the database
    if (this.config.enableLogging) {
      console.log(`🏁 Pit count update: ${websocketId} -> ${pitCount} (handled automatically)`);
    }
  }

  /**
   * Handle time on track updates
   */
  private async handleTimeOnTrackUpdate(websocketId: string, timeValue: string): Promise<void> {
    // This could be used for race time tracking
    if (this.config.enableLogging) {
      //console.log(`⏱️ Time on track: ${websocketId} -> ${timeValue}`);
    }
  }

  /**
   * Handle pit status updates (IN/OUT)
   */
  private async handlePitStatusUpdate(websocketId: string, pitStatus: string): Promise<void> {
    await this.handlePitUpdate(websocketId, pitStatus);
  }

  /**
   * Update existing pit stops with proper race time when dyn1 data becomes available
   */
  private async updatePitStopsWithRaceTime(): Promise<void> {
    if (!this.currentSession || this.raceDurationMs === 0) return;

    try {
      const { ApexPitStop } = await import('../models/ApexModels');

      // Find pit stops with raceTimeAtPitIn = 0 (created before dyn1 data was available)
      const pitStopsToUpdate = await ApexPitStop.find({
        sessionId: this.currentSession._id,
        raceTimeAtPitIn: 0
      });

      if (pitStopsToUpdate.length > 0) {
        const currentRaceTime = this.getCurrentRaceTime();

        // Update all pit stops with the current race time
        await ApexPitStop.updateMany(
          {
            sessionId: this.currentSession._id,
            raceTimeAtPitIn: 0
          },
          {
            raceTimeAtPitIn: currentRaceTime,
            raceTimeAtPitOut: currentRaceTime // Also update pit out time if they're still in pit
          }
        );

        if (this.config.enableLogging) {
          console.log(`⏱️ Updated ${pitStopsToUpdate.length} pit stops with race time: ${currentRaceTime}s`);
        }
      }
    } catch (error) {
      console.error('Error updating pit stops with race time:', error);
    }
  }

  /**
   * Get current race time in seconds from dyn1 timer
   * Race time = total race duration - current time left (updated by timer)
   */
  private getCurrentRaceTime(): number {
    if (this.raceDurationMs === 0 || this.raceTimeLeftMs === 0) {
      // No dyn1 data available yet - return 0 instead of fallback
      if (this.config.enableLogging) {
        console.log(`⏱️ No dyn1 race timer data available yet - using 0s for raceTimeAtPitIn`);
      }
      return 0;
    }

    // Calculate current race time from timer
    // Account for time elapsed since last dyn1 message
    const now = new Date();
    const elapsedSinceLastDyn1 = now.getTime() - this.lastDyn1Timestamp.getTime();
    const currentTimeLeftMs = Math.max(0, this.raceTimeLeftMs - elapsedSinceLastDyn1);

    if (this.config.enableLogging) {
      console.log(`⏱️ Race time from dyn1 timer: (${Math.floor(currentTimeLeftMs / 1000)}s left)`);
    }

    return currentTimeLeftMs;
  }



  /**
   * Get available competitors for debugging
   */
  private async getAvailableCompetitors(): Promise<string[]> {
    if (!this.currentSession) return [];

    try {
      const competitors = await ApexCompetitor.find({
        sessionId: this.currentSession._id
      }).select('websocketId name').lean();

      return competitors.map(c => `${c.websocketId} (${c.name})`);
    } catch (error) {
      console.error('Error fetching available competitors:', error);
      return [];
    }
  }

  /**
   * Get current session
   */
  getCurrentSession() {
    return this.currentSession;
  }

  /**
   * Get grid data
   */
  getGridData() {
    return this.gridData;
  }

  /**
   * Cleanup method to stop race timer and log performance
   */
  cleanup(): void {
    if (this.raceTimer) {
      clearInterval(this.raceTimer);
      this.raceTimer = null;
      if (this.config.enableLogging) {
        console.log('⏱️ Race timer cleaned up');
      }
    }

    // Log performance summary on cleanup
    if (this.performanceStats.size > 0) {
      this.logPerformanceSummary();
    }
  }

  /**
   * Get current race time left in milliseconds (for external use)
   */
  getCurrentRaceTimeLeft(): number {
    if (this.raceDurationMs === 0) return 0;

    const now = new Date();
    const elapsedSinceLastDyn1 = now.getTime() - this.lastDyn1Timestamp.getTime();
    return Math.max(0, this.raceTimeLeftMs - elapsedSinceLastDyn1);
  }

  /**
   * Get race duration in milliseconds (for external use)
   */
  getRaceDuration(): number {
    return this.raceDurationMs;
  }
}
