# Session Per Grid Implementation

## Overview

This document outlines the changes made to ensure that **each grid message creates a new session** and **all subsequent messages reference that session ID**, with **pit rows creating karts for each session**.

## Changes Implemented

### ✅ **1. New Session Creation for Each Grid**

**Previous Behavior**: Grid messages would reuse existing sessions if a sessionId was configured.

**New Behavior**: Every grid message creates a completely new session.

```typescript
/**
 * Create NEW session from grid message data
 * Each grid message should create a new session
 */
private async createSession(messageData: Record<string, any>): Promise<void> {
  try {
    const sessionData = {
      title1: messageData.title1?.value || 'Race Session',
      title2: messageData.title2?.value || new Date().toISOString(),
      track: messageData.track?.value || 'Unknown Track',
      isActive: true,
      gridData: this.gridData || {},
      sessionData: messageData || {}
    };

    // ALWAYS create a new session when grid message is found
    // This ensures each grid message starts a new session
    this.currentSession = await ApexSession.create(sessionData);
    
    // Clear websocket mapping for new session
    this.websocketToCompetitorMap.clear();
    
    if (this.config.enableLogging) {
      console.log(`✅ Created NEW session: ${this.currentSession._id} - ${sessionData.title1}`);
    }

    this.emit('sessionCreated', this.currentSession);
  } catch (error) {
    console.error('Error creating session:', error);
  }
}
```

**Benefits**:
- Each race/session is completely independent
- No data contamination between sessions
- Clear session boundaries
- Proper session lifecycle management

### ✅ **2. Fresh Entity Creation Per Session**

**Previous Behavior**: Checked for existing entities and tried to avoid duplicates.

**New Behavior**: Always create fresh entities for each new session.

```typescript
// For new sessions, we always create fresh entities
// No need to check for existing entities since each grid creates a new session
const existingWebsocketIds = new Set(); // Empty set - always create new
const existingKartNumbers = new Set(); // Empty set - always create new
```

**Benefits**:
- No entity conflicts between sessions
- Faster entity creation (no duplicate checks)
- Clean data model per session
- Simplified logic

### ✅ **3. Session-Specific Pit Row Karts**

**Previous Behavior**: Pit row karts were shared across sessions.

**New Behavior**: Each session gets its own set of pit row karts.

```typescript
/**
 * Create apex karts for all existing pit rows in a NEW session
 * Each session gets its own set of pit row karts
 */
static async createApexKartsForPitRows(sessionId: string): Promise<void> {
  // For new sessions, always create fresh karts for pit rows
  // No need to check existing since each session is independent
  const kartsToCreate = [];
  
  for (const row of pitRows) {
    // Generate a unique kart number for this pit row (use row number + 1000 to avoid conflicts)
    const pitRowKartNumber = 1000 + row.rowNumber;

    // Create apex kart for this pit row (always create for new sessions)
    kartsToCreate.push({
      sessionId: new mongoose.Types.ObjectId(sessionId),
      kartNumber: pitRowKartNumber,
      speed: 3,
      currentTeamId: null,
      currentRowId: row._id, // Link to pit row
      status: 'in_pit_row',
      isActive: true
    });
  }
}
```

**Benefits**:
- Each session has independent pit row karts
- No conflicts between session pit assignments
- Clear pit row state per session
- Proper session isolation

## Session Lifecycle

### **Grid Message Processing**
```
1. Grid message received (e.g., init|r|)
2. NEW session created in database
3. Grid data parsed
4. Fresh teams, karts, competitors created for this session
5. Fresh pit row karts created for this session
6. Websocket mapping populated for this session
```

### **Subsequent Message Processing**
```
1. Update message received (e.g., r17749c9|tn|1:10.062)
2. Uses current session ID from grid processing
3. Updates entities within that session
4. All data references the same session
```

### **Next Grid Message**
```
1. New grid message received (e.g., init|p|)
2. NEW session created (independent of previous)
3. Previous session remains in database (historical)
4. Fresh entities created for new session
5. Process repeats
```

## Database Structure

### **Sessions Table**
```javascript
// Each grid creates a new session
{
  _id: ObjectId("session1"),
  title1: "EKO 4H of Serres",
  title2: "Final",
  track: "Karting Track (1200m)",
  isActive: true,
  createdAt: "2024-11-09T10:00:00.000Z"
}

{
  _id: ObjectId("session2"), 
  title1: "Master Vittoria Championship",
  title2: "Corsa 3",
  track: "Pista Orario Full (1400m)",
  isActive: true,
  createdAt: "2024-11-09T10:15:00.000Z"
}
```

### **Teams/Karts/Competitors Per Session**
```javascript
// Session 1 entities
{ sessionId: ObjectId("session1"), name: "FSB RACING", ... }
{ sessionId: ObjectId("session1"), name: "RAMUS RACING", ... }
{ sessionId: ObjectId("session1"), kartNumber: 19, ... }
{ sessionId: ObjectId("session1"), kartNumber: 42, ... }

// Session 2 entities (completely separate)
{ sessionId: ObjectId("session2"), name: "GIARRATANA", ... }
{ sessionId: ObjectId("session2"), name: "ROSSI TEAM", ... }
{ sessionId: ObjectId("session2"), kartNumber: 5, ... }
{ sessionId: ObjectId("session2"), kartNumber: 8, ... }
```

### **Pit Row Karts Per Session**
```javascript
// Session 1 pit row karts
{ sessionId: ObjectId("session1"), kartNumber: 1001, currentRowId: ObjectId("row1") }
{ sessionId: ObjectId("session1"), kartNumber: 1002, currentRowId: ObjectId("row2") }

// Session 2 pit row karts (separate set)
{ sessionId: ObjectId("session2"), kartNumber: 1001, currentRowId: ObjectId("row1") }
{ sessionId: ObjectId("session2"), kartNumber: 1002, currentRowId: ObjectId("row2") }
```

## Sample Log Processing

### **First Grid Message (10:00:00)**
```
[2024-11-09T10:00:00.000Z] init|r|
title1||EKO 4H of Serres - Sample Race
grid||<tbody>...FSB RACING, RAMUS RACING, SKR JUNIOR...</tbody>

Result:
✅ Created NEW session: 68482df93e787b226168d79e - EKO 4H of Serres - Sample Race
✅ Created 3 new teams
✅ Created 3 new karts  
✅ Created 3 new competitors
✅ Created 3 NEW apex karts for pit rows in session 68482df93e787b226168d79e
```

### **Subsequent Messages (10:01:00 - 10:08:55)**
```
[2024-11-09T10:01:15.000Z] r17749c9|tn|1:10.062
[2024-11-09T10:04:45.000Z] r17749c15|pit|IN
[2024-11-09T10:05:15.000Z] r17749c15|pit|OUT

Result:
📊 Lap 1 recorded: FSB RACING - 1:10.062 (70062ms)
🏁 Pit IN: FSB RACING (Team 68482df93e787b226168d79f)
🏁 Pit OUT: FSB RACING - Duration: 30.0s
```

### **Second Grid Message (10:15:00)**
```
[2024-11-09T10:15:00.000Z] init|p|
title1||Master Vittoria Championship
grid||<tbody>...GIARRATANA, ROSSI TEAM...</tbody>

Result:
✅ Created NEW session: 78593ef04f898c337279e80f - Master Vittoria Championship
✅ Created 3 new teams (different from session 1)
✅ Created 3 new karts (different from session 1)
✅ Created 3 new competitors (different from session 1)
✅ Created 3 NEW apex karts for pit rows in session 78593ef04f898c337279e80f
```

## API Impact

### **Session Selection**
Frontend can now select specific sessions:
```typescript
// Get all sessions
GET /api/apex/sessions

// Get session-specific data
GET /api/apex/sessions/:sessionId/teams
GET /api/apex/sessions/:sessionId/karts
GET /api/apex/sessions/:sessionId/laps
GET /api/apex/sessions/:sessionId/pitstops
```

### **Historical Data**
All sessions are preserved:
```typescript
// View historical sessions
const sessions = await ApexSession.find({}).sort({ createdAt: -1 });

// Compare data between sessions
const session1Teams = await ApexTeam.find({ sessionId: session1._id });
const session2Teams = await ApexTeam.find({ sessionId: session2._id });
```

## Benefits

### ✅ **Data Isolation**
- Each session is completely independent
- No data contamination between races
- Clear session boundaries

### ✅ **Historical Tracking**
- All sessions preserved in database
- Can compare performance across sessions
- Full audit trail of race data

### ✅ **Simplified Logic**
- No complex duplicate checking
- Faster entity creation
- Cleaner code paths

### ✅ **Proper Pit Row Management**
- Each session gets its own pit row karts
- No conflicts between session assignments
- Clear pit row state per session

### ✅ **Scalability**
- Can handle unlimited sessions
- No performance degradation over time
- Clean database structure

## Testing

The comprehensive test log now properly creates two independent sessions:

1. **Session 1**: EKO 4H of Serres (3 teams, 4 pit stops)
2. **Session 2**: Master Vittoria Championship (3 teams, 2 pit stops)

Each session has its own complete set of entities and pit row karts, ensuring proper isolation and functionality.
