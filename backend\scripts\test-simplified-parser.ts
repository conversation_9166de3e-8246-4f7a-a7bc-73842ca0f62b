#!/usr/bin/env ts-node

/**
 * Comprehensive test script for the simplified apex parser
 * Tests both 24h Serres and Master Vittoria grid formats
 */

import fs from 'fs';
import path from 'path';
import mongoose from 'mongoose';
import { ApexParserSimple } from '../src/services/apexParserSimple';
import { ApexSession, ApexTeam, ApexKart, ApexCompetitor, ApexLap } from '../src/models/ApexModels';

// Test configuration
interface TestConfig {
  logFile: string;
  raceName: string;
  expectedDrivers: number;
  expectedTeams: number;
  expectedKarts: number;
}

const testConfigs: TestConfig[] = [
  {
    logFile: '24h serres.txt',
    raceName: '24h Serres (Endurance)',
    expectedDrivers: 20, // Estimate
    expectedTeams: 20,
    expectedKarts: 20
  },
  {
    logFile: 'master vitoria.txt',
    raceName: 'Master Vitt<PERSON> (Sprint)',
    expectedDrivers: 15, // Estimate
    expectedTeams: 15,
    expectedKarts: 15
  }
];

// Connect to test database
async function connectToDatabase(): Promise<void> {
  try {
    const mongoUri = process.env.MONGODB_URI || 'mongodb://localhost:27017/race-planner-test';
    await mongoose.connect(mongoUri);
    console.log('✅ Connected to test database');
  } catch (error) {
    console.error('❌ Failed to connect to database:', error);
    process.exit(1);
  }
}

// Clean up test data
async function cleanupTestData(): Promise<void> {
  try {
    await Promise.all([
      ApexSession.deleteMany({}),
      ApexTeam.deleteMany({}),
      ApexKart.deleteMany({}),
      ApexCompetitor.deleteMany({}),
      ApexLap.deleteMany({})
    ]);
    console.log('🧹 Cleaned up test data');
  } catch (error) {
    console.error('❌ Failed to cleanup test data:', error);
  }
}

// Extract messages from log file
function extractMessagesFromLog(logContent: string): { gridMessages: string[], updateMessages: string[] } {
  const lines = logContent.split('\n').filter(line => line.trim().length > 0);
  const gridMessages: string[] = [];
  const updateMessages: string[] = [];
  
  for (const line of lines) {
    if (line.includes('grid||')) {
      gridMessages.push(line);
    } else if (line.includes('|') && (line.includes('r') || line.includes('c'))) {
      updateMessages.push(line);
    }
  }
  
  return { gridMessages, updateMessages };
}

// Test a single race format
async function testRaceFormat(config: TestConfig): Promise<void> {
  console.log(`\n🏁 Testing ${config.raceName}`);
  console.log('='.repeat(50));
  
  const logPath = path.join(__dirname, '..', 'apex parser files', config.logFile);
  
  if (!fs.existsSync(logPath)) {
    console.error(`❌ Log file not found: ${config.logFile}`);
    return;
  }
  
  try {
    // Read log content
    const logContent = fs.readFileSync(logPath, 'utf8');
    console.log(`📖 Read log file: ${logContent.length} characters`);
    
    // Extract messages
    const { gridMessages, updateMessages } = extractMessagesFromLog(logContent);
    console.log(`📊 Found ${gridMessages.length} grid messages, ${updateMessages.length} update messages`);
    
    // Create parser
    const parser = new ApexParserSimple({
      enableLogging: true
    });
    
    // Test 1: Process grid message first
    console.log('\n🧪 Test 1: Processing grid message (should create entities)');
    if (gridMessages.length > 0) {
      await parser.parseMessage(gridMessages[0]);
      
      const session = parser.getCurrentSession();
      const gridData = parser.getGridData();
      
      if (session) {
        console.log(`✅ Session created: ${session._id}`);
        console.log(`   Title: ${session.title1} - ${session.title2}`);
      } else {
        console.log('❌ No session created');
      }
      
      if (gridData) {
        console.log(`✅ Grid data parsed: ${Object.keys(gridData.drivers).length} drivers`);
        console.log(`   Headers: ${Object.keys(gridData.header_types).length} columns`);
        
        // Show sample driver data
        const sampleDriver = Object.entries(gridData.drivers)[0];
        if (sampleDriver) {
          const [driverId, driverData] = sampleDriver;
          console.log(`   Sample: ${driverId} -> ${driverData.dr?.value} (Kart: ${driverData.no?.value})`);
        }
      } else {
        console.log('❌ No grid data parsed');
      }
    } else {
      console.log('❌ No grid messages found in log file');
      return;
    }
    
    // Test 2: Check database entities
    console.log('\n🧪 Test 2: Checking database entities');
    const [teams, karts, competitors] = await Promise.all([
      ApexTeam.find({ sessionId: parser.getCurrentSession()?._id }),
      ApexKart.find({ sessionId: parser.getCurrentSession()?._id }),
      ApexCompetitor.find({ sessionId: parser.getCurrentSession()?._id })
    ]);
    
    console.log(`📊 Database entities created:`);
    console.log(`   Teams: ${teams.length} (expected: ~${config.expectedTeams})`);
    console.log(`   Karts: ${karts.length} (expected: ~${config.expectedKarts})`);
    console.log(`   Competitors: ${competitors.length} (expected: ~${config.expectedDrivers})`);
    
    // Show sample entities
    if (teams.length > 0) {
      const sampleTeam = teams[0];
      console.log(`   Sample Team: ${sampleTeam.name} (Kart: ${sampleTeam.kartNumber})`);
    }
    
    if (competitors.length > 0) {
      const sampleCompetitor = competitors[0];
      console.log(`   Sample Competitor: ${sampleCompetitor.name} (ID: ${sampleCompetitor.competitorId})`);
    }
    
    // Test 3: Process update messages (should work now)
    console.log('\n🧪 Test 3: Processing update messages (should update entities)');
    const sampleUpdates = updateMessages.slice(0, 5); // Test first 5 updates
    
    for (const updateMessage of sampleUpdates) {
      await parser.parseMessage(updateMessage);
    }
    
    console.log(`✅ Processed ${sampleUpdates.length} update messages`);
    
    // Test 4: Check lap times created
    const laps = await ApexLap.find({ sessionId: parser.getCurrentSession()?._id });
    console.log(`📊 Lap times created: ${laps.length}`);
    
    if (laps.length > 0) {
      const sampleLap = laps[0];
      console.log(`   Sample Lap: Team ${sampleLap.teamId} - ${sampleLap.lapTime}`);
    }
    
    // Test 5: Test update messages without session (should be skipped)
    console.log('\n🧪 Test 4: Testing updates without session (should skip)');
    const newParser = new ApexParserSimple({ enableLogging: true });
    
    if (updateMessages.length > 0) {
      await newParser.parseMessage(updateMessages[0]);
      console.log('✅ Update message correctly skipped (no session/grid)');
    }
    
    console.log(`\n✅ ${config.raceName} test completed successfully!`);
    
  } catch (error) {
    console.error(`❌ Error testing ${config.raceName}:`, error);
  }
}

// Test grid format differences
async function testGridFormatDifferences(): Promise<void> {
  console.log('\n🔍 Testing Grid Format Differences');
  console.log('='.repeat(50));
  
  for (const config of testConfigs) {
    const logPath = path.join(__dirname, '..', 'apex parser files', config.logFile);
    
    if (!fs.existsSync(logPath)) {
      console.log(`❌ Skipping ${config.raceName} - file not found`);
      continue;
    }
    
    try {
      const logContent = fs.readFileSync(logPath, 'utf8');
      const { gridMessages } = extractMessagesFromLog(logContent);
      
      if (gridMessages.length === 0) {
        console.log(`❌ No grid messages in ${config.raceName}`);
        continue;
      }
      
      // Parse grid data
      const { parseGridData } = await import('../src/services/apexGridParser');
      const gridMatch = gridMessages[0].match(/grid\|\|([^]*?)(?=\n|$)/);
      
      if (!gridMatch) {
        console.log(`❌ Could not extract grid from ${config.raceName}`);
        continue;
      }
      
      const gridData = parseGridData(gridMatch[1]);
      
      console.log(`\n📊 ${config.raceName}:`);
      console.log(`   Drivers: ${Object.keys(gridData.drivers).length}`);
      console.log(`   Headers: ${Object.keys(gridData.header_types).length}`);
      
      // Show header mapping
      console.log(`   Header Types:`);
      for (const [column, type] of Object.entries(gridData.header_types)) {
        const label = gridData.header_labels[column] || 'N/A';
        console.log(`      ${column}: ${type} ("${label}")`);
      }
      
      // Show sample drivers
      const sampleDrivers = Object.entries(gridData.drivers).slice(0, 2);
      console.log(`   Sample Drivers:`);
      for (const [driverId, driverData] of sampleDrivers) {
        const name = driverData.dr?.value || 'N/A';
        const kartNumber = driverData.no?.value || 'N/A';
        const nationality = driverData.nat?.value || 'N/A';
        console.log(`      ${driverId}: "${name}" (Kart: ${kartNumber}, Nation: ${nationality})`);
      }
      
    } catch (error) {
      console.error(`❌ Error analyzing ${config.raceName}:`, error);
    }
  }
}

// Main test execution
async function main(): Promise<void> {
  console.log('🚀 Simplified Apex Parser Test Suite');
  console.log('=====================================');
  
  try {
    // Connect to database
    await connectToDatabase();
    
    // Clean up previous test data
    await cleanupTestData();
    
    // Test grid format differences
    await testGridFormatDifferences();
    
    // Test each race format
    for (const config of testConfigs) {
      await cleanupTestData(); // Clean between tests
      await testRaceFormat(config);
    }
    
    console.log('\n🎉 All tests completed!');
    console.log('\n📝 Summary:');
    console.log('   - Both grid formats can be parsed correctly');
    console.log('   - Parser correctly skips updates without session/grid');
    console.log('   - Database entities are created from grid data');
    console.log('   - Update messages work after grid initialization');
    
  } catch (error) {
    console.error('❌ Test suite failed:', error);
  } finally {
    // Cleanup and disconnect
    await cleanupTestData();
    await mongoose.disconnect();
    console.log('👋 Disconnected from database');
  }
}

// Run the test suite
if (require.main === module) {
  main().catch(console.error);
}

export { testRaceFormat, testGridFormatDifferences };
