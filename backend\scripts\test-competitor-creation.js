#!/usr/bin/env node

/**
 * Test script to verify competitor creation works correctly
 */

const mongoose = require('mongoose');

async function testCompetitorCreation() {
  console.log('🧪 Testing Competitor Creation Fix...');
  
  try {
    // Connect to database
    const mongoUri = process.env.MONGO_URI || 'mongodb+srv://manuelbiancolilla:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0';
    await mongoose.connect(mongoUri);
    console.log('✅ Connected to database');
    
    // Import the optimized models
    const { ApexSession, ApexTeam, ApexKart, ApexCompetitor } = require('../build/models/ApexModels');
    
    // Clean up test data
    const testSessionName = 'test-competitor-creation';
    await ApexSession.deleteMany({ title1: testSessionName });
    await ApexTeam.deleteMany({});
    await ApexKart.deleteMany({});
    await ApexCompetitor.deleteMany({});
    console.log('🧹 Cleaned up test data');
    
    // Create test session
    const session = await ApexSession.create({
      title1: testSessionName,
      title2: 'Competitor Creation Test',
      track: 'Test Track',
      isActive: true
    });
    console.log(`✅ Session created: ${session._id}`);
    
    // Create test teams
    const teams = await ApexTeam.insertMany([
      {
        sessionId: session._id,
        name: 'GIARRATANA',
        drivers: ['GIARRATANA'],
        nationality: '',
        totalLaps: 0,
        status: 'active',
        isActive: true
      },
      {
        sessionId: session._id,
        name: 'ZR RACING',
        drivers: ['ZR RACING'],
        nationality: '',
        totalLaps: 0,
        status: 'active',
        isActive: true
      }
    ]);
    console.log(`✅ Created ${teams.length} teams`);
    
    // Create test karts
    const karts = await ApexKart.insertMany([
      {
        sessionId: session._id,
        kartNumber: 1,
        speed: 3,
        status: 'available',
        isActive: true
      },
      {
        sessionId: session._id,
        kartNumber: 2,
        speed: 3,
        status: 'available',
        isActive: true
      }
    ]);
    console.log(`✅ Created ${karts.length} karts`);
    
    // Test the mapping logic (simulate what the parser does)
    console.log('\n📝 Testing team/kart mapping logic...');
    
    const teamsByName = new Map();
    const kartsByNumber = new Map();
    
    teams.forEach(team => {
      teamsByName.set(team.name, team);
      console.log(`   Team mapped: "${team.name}" -> ${team._id}`);
    });
    
    karts.forEach(kart => {
      kartsByNumber.set(kart.kartNumber, kart);
      console.log(`   Kart mapped: ${kart.kartNumber} -> ${kart._id}`);
    });
    
    // Simulate grid data processing
    const gridDrivers = {
      'r17742': { dr: { value: 'GIARRATANA' }, no: { value: '1' }, nat: { value: '' } },
      'r17794': { dr: { value: 'ZR RACING' }, no: { value: '2' }, nat: { value: '' } }
    };
    
    const competitorsToCreate = [];
    
    for (const [driverId, driverData] of Object.entries(gridDrivers)) {
      const competitorId = driverId.replace('r', '');
      const teamName = driverData.dr?.value || `Team ${competitorId}`;
      const kartNumber = parseInt(driverData.no?.value || '0') || 0;
      const nationality = driverData.nat?.value || '';
      
      console.log(`\n   Processing: ${competitorId} -> "${teamName}" (kart ${kartNumber})`);
      
      const team = teamsByName.get(teamName);
      const kart = kartsByNumber.get(kartNumber);
      
      if (team && kart) {
        console.log(`   ✅ Found team: ${team._id}, kart: ${kart._id}`);
        
        competitorsToCreate.push({
          sessionId: session._id,
          competitorId,
          teamId: team._id,
          kartId: kart._id,
          name: teamName,
          nationality,
          drivers: [teamName],
          isActive: true
        });
        
        // Update relationships
        await ApexTeam.findByIdAndUpdate(team._id, { currentKartId: kart._id });
        await ApexKart.findByIdAndUpdate(kart._id, { currentTeamId: team._id });
        console.log(`   ✅ Updated relationships`);
      } else {
        console.log(`   ❌ Missing team: ${!!team}, kart: ${!!kart}`);
      }
    }
    
    // Create competitors
    if (competitorsToCreate.length > 0) {
      const createdCompetitors = await ApexCompetitor.insertMany(competitorsToCreate);
      console.log(`\n✅ Created ${createdCompetitors.length} competitors`);
      
      // Test competitor lookup (simulate lap time update)
      for (const competitor of createdCompetitors) {
        const foundCompetitor = await ApexCompetitor.findOne({
          sessionId: session._id,
          competitorId: competitor.competitorId
        });
        
        if (foundCompetitor) {
          console.log(`   ✅ Competitor ${competitor.competitorId} can be found for lap time updates`);
        } else {
          console.log(`   ❌ Competitor ${competitor.competitorId} NOT found`);
        }
      }
    } else {
      console.log('\n❌ No competitors were created');
    }
    
    // Clean up test data
    await ApexSession.deleteMany({ title1: testSessionName });
    await ApexTeam.deleteMany({ sessionId: session._id });
    await ApexKart.deleteMany({ sessionId: session._id });
    await ApexCompetitor.deleteMany({ sessionId: session._id });
    console.log('\n🧹 Cleaned up test data');
    
    console.log('\n🎉 Competitor creation test completed!');
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  } finally {
    await mongoose.disconnect();
    console.log('👋 Disconnected from database');
  }
}

// Run the test
if (require.main === module) {
  testCompetitorCreation().catch(console.error);
}

module.exports = { testCompetitorCreation };
