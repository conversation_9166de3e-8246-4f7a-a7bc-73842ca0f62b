/* StintList Component Styles - Grid Version */

.stint-list-container {
  margin: 16px 0;
  width: 100%;
  padding: 0 8px;
}

.stint-list-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr)); /* Adjusted for mobile */
  gap: 10px;
  width: 100%;
}

/* Ensure grid items behave correctly */
.stint-list-grid > div {
  min-width: 0;
}

/* Responsive adjustments */
@media (min-width: 390px) {
  .stint-list-grid {
    grid-template-columns: repeat(2, 1fr); /* 2 columns on small to medium screens */
  }
}

@media (min-width: 578px) {
  .stint-list-grid {
    grid-template-columns: repeat(2, 1fr); /* 2 columns on small to medium screens */
  }
}

@media (min-width: 768px) {
  .stint-list-grid {
    grid-template-columns: repeat(4, 1fr); /* 3 columns on medium screens */
  }
}

@media (min-width: 992px) {
  .stint-list-grid {
    grid-template-columns: repeat(4, 1fr); /* 4 columns on large screens */
  }
}

@media (min-width: 1200px) {
  .stint-list-grid {
    grid-template-columns: repeat(5, 1fr); /* 5 columns on extra large screens */
  }
}

/* Make sure the IonCard components fit properly in the grid */
.stint-list-grid ion-card {
  margin: 0;
  height: 100%;
}
