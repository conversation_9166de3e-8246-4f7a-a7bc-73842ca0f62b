import express from "express";
import { getAllTeams, getTeamById, createTeam, updateTeam, deleteTeam, getTeamAssignmentHistoryController, } from '../controllers/teamController';

const router = express.Router();

router.get('/', getAllTeams);
router.get('/:id', getTeamById);
router.post('/', createTeam);
router.put('/:id', updateTeam);
router.delete('/:id', deleteTeam);      // Define the parameterized '/:id' route SECOND

router.get('/:teamId/assignment-history', getTeamAssignmentHistoryController)

export default router;