#!/usr/bin/env ts-node

/**
 * Test script to verify pitstop detection is working correctly
 * This script tests the new pitstop handling logic in ApexParserSimple
 */

import mongoose from 'mongoose';
import { ApexParserSimple } from '../src/services/apexParserSimple';
import { ApexSession, ApexTeam, ApexKart, ApexCompetitor, ApexPitStop } from '../src/models/ApexModels';

// Test configuration
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/raceplanner';

async function connectToDatabase() {
  try {
    await mongoose.connect(MONGODB_URI);
    console.log('✅ Connected to MongoDB');
  } catch (error) {
    console.error('❌ Failed to connect to MongoDB:', error);
    process.exit(1);
  }
}

async function cleanupTestData() {
  console.log('🧹 Cleaning up test data...');
  await ApexSession.deleteMany({ title1: 'Test Pitstop Session' });
  await ApexTeam.deleteMany({ name: 'Test Team' });
  await ApexKart.deleteMany({ kartNumber: 999 });
  await ApexCompetitor.deleteMany({ name: 'Test Driver' });
  await ApexPitStop.deleteMany({ reason: 'Test' });
}

async function testPitstopDetection() {
  console.log('\n🏁 Testing Pitstop Detection...\n');

  const parser = new ApexParserSimple({ enableLogging: true });

  // Test 1: Create a session with grid data
  console.log('📊 Test 1: Creating session with grid data');
  const gridMessage = `init|r|
title1||Test Pitstop Session
title2||Pitstop Test
track||Test Track
grid||<tbody><tr data-id="r0"><td data-id="c1" data-type="no">No</td><td data-id="c2" data-type="name">Name</td><td data-id="c9" data-type="pit">Pit</td></tr><tr data-id="r12345"><td data-id="r12345c1">999</td><td data-id="r12345c2">Test Driver</td><td data-id="r12345c9"></td></tr></tbody>`;

  await parser.parseMessage(gridMessage);
  console.log('✅ Session created with grid data');

  // Wait a moment for database operations
  await new Promise(resolve => setTimeout(resolve, 1000));

  // Test 2: Send pit entry message
  console.log('\n📊 Test 2: Sending pit entry message');
  const pitInMessage = 'r12345c9|pit|IN';
  await parser.parseMessage(pitInMessage);
  console.log('✅ Pit entry message sent');

  // Wait a moment
  await new Promise(resolve => setTimeout(resolve, 500));

  // Test 3: Check if pitstop was created
  console.log('\n📊 Test 3: Checking if pitstop was created');
  const pitStops = await ApexPitStop.find({ reason: 'Regular' }).lean();
  console.log(`Found ${pitStops.length} pitstop(s)`);
  
  if (pitStops.length > 0) {
    console.log('✅ Pitstop entry recorded successfully');
    console.log('   Pitstop details:', {
      competitorId: pitStops[0]?.competitorId,
      kartId: pitStops[0]?.kartId,
      pitInTime: pitStops[0]?.pitInTime,
      lapNumber: pitStops[0]?.lapNumber
    });
  } else {
    console.log('❌ No pitstop entry found');
  }

  // Test 4: Send pit exit message
  console.log('\n📊 Test 4: Sending pit exit message');
  const pitOutMessage = 'r12345c9|pit|OUT';
  await parser.parseMessage(pitOutMessage);
  console.log('✅ Pit exit message sent');

  // Wait a moment
  await new Promise(resolve => setTimeout(resolve, 500));

  // Test 5: Check if pitstop was completed
  console.log('\n📊 Test 5: Checking if pitstop was completed');
  const completedPitStops = await ApexPitStop.find({ 
    reason: 'Regular',
    pitOutTime: { $exists: true }
  }).lean();
  
  console.log(`Found ${completedPitStops.length} completed pitstop(s)`);
  
  if (completedPitStops.length > 0) {
    console.log('✅ Pitstop exit recorded successfully');
    console.log('   Completed pitstop details:', {
      competitorId: completedPitStops[0]?.competitorId,
      pitInTime: completedPitStops[0]?.pitInTime,
      pitOutTime: completedPitStops[0]?.pitOutTime,
      pitDuration: completedPitStops[0]?.pitDuration
    });
  } else {
    console.log('❌ No completed pitstop found');
  }

  // Test 6: Test transaction handling with rapid messages
  console.log('\n📊 Test 6: Testing transaction handling with rapid messages');
  const rapidMessages = [
    'r12345c9|pit|IN',
    'r12345c2|name|Test Driver Updated',
    'r12345c9|pit|OUT'
  ];

  // Send messages rapidly
  for (const message of rapidMessages) {
    parser.parseMessage(message); // Don't await - send rapidly
  }

  // Wait for all messages to process
  await new Promise(resolve => setTimeout(resolve, 2000));

  const allPitStops = await ApexPitStop.find({ reason: 'Regular' }).lean();
  console.log(`Total pitstops after rapid messages: ${allPitStops.length}`);
  console.log('✅ Transaction handling test completed');

  return {
    pitStopsCreated: pitStops.length,
    pitStopsCompleted: completedPitStops.length,
    totalPitStops: allPitStops.length
  };
}

async function main() {
  try {
    await connectToDatabase();
    await cleanupTestData();
    
    const results = await testPitstopDetection();
    
    console.log('\n📊 Test Results Summary:');
    console.log(`   Pitstops Created: ${results.pitStopsCreated}`);
    console.log(`   Pitstops Completed: ${results.pitStopsCompleted}`);
    console.log(`   Total Pitstops: ${results.totalPitStops}`);
    
    if (results.pitStopsCreated > 0 && results.pitStopsCompleted > 0) {
      console.log('\n✅ All pitstop detection tests passed!');
    } else {
      console.log('\n❌ Some pitstop detection tests failed!');
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  } finally {
    await cleanupTestData();
    await mongoose.disconnect();
    console.log('🔌 Disconnected from MongoDB');
  }
}

// Run the test
if (require.main === module) {
  main().catch(console.error);
}
