/* Log Replay Styles */

.log-replay-container {
  padding: 16px;
  max-width: 1200px;
  margin: 0 auto;
}

.connection-badge {
  margin-right: 16px;
  font-size: 0.8rem;
  padding: 4px 8px;
}

/* Card spacing */
.log-replay-container ion-card {
  margin-bottom: 16px;
}

.log-replay-container ion-card:last-child {
  margin-bottom: 0;
}

/* Progress info */
.progress-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 8px;
  font-size: 0.9rem;
  color: var(--ion-color-medium);
}

.time-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 4px;
  font-size: 0.8rem;
  color: var(--ion-color-medium);
}

/* Messages container */
.messages-container {
  max-height: 600px;
  overflow-y: auto;
  border: 1px solid var(--ion-color-light-shade);
  border-radius: 8px;
  padding: 8px;
}

.messages-container ion-list {
  padding: 0;
}

.message-item {
  --padding-start: 12px;
  --padding-end: 12px;
  --border-color: var(--ion-color-light);
  margin-bottom: 12px;
  border-radius: 8px;
  background: var(--ion-color-light-tint);
}

.message-item:last-child {
  margin-bottom: 0;
}

.message-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid var(--ion-color-light-shade);
}

.message-header h3 {
  margin: 0;
  font-size: 1rem;
  font-weight: 600;
  color: var(--ion-color-primary);
}

.timestamp {
  font-size: 0.8rem;
  color: var(--ion-color-medium);
  font-family: 'Courier New', monospace;
  background: var(--ion-color-light);
  padding: 2px 6px;
  border-radius: 4px;
}

.original-message,
.parsed-message {
  margin: 8px 0;
  padding: 12px;
  border-radius: 6px;
  border-left: 4px solid;
}

.original-message {
  background-color: var(--ion-color-light);
  border-left-color: var(--ion-color-medium);
}

.parsed-message {
  background-color: var(--ion-color-success-tint);
  border-left-color: var(--ion-color-success);
}

.original-message h4,
.parsed-message h4 {
  margin: 0 0 8px 0;
  font-size: 0.9rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.original-message h4 {
  color: var(--ion-color-dark);
}

.parsed-message h4 {
  color: var(--ion-color-success-shade);
}

.message-content {
  font-family: 'Courier New', monospace;
  font-size: 0.8rem;
  white-space: pre-wrap;
  word-break: break-all;
  margin: 0;
  padding: 10px;
  border-radius: 4px;
  max-height: 200px;
  overflow-y: auto;
  line-height: 1.4;
}

.message-content.original {
  background-color: var(--ion-color-light-shade);
  color: var(--ion-color-dark);
  border: 1px solid var(--ion-color-medium-tint);
}

.message-content.parsed {
  background-color: var(--ion-color-success-tint);
  color: var(--ion-color-success-shade);
  border: 1px solid var(--ion-color-success);
}

.parsed-content p {
  margin: 6px 0;
  font-size: 0.85rem;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.parsed-content strong {
  color: var(--ion-color-success-shade);
  font-weight: 600;
}

.parsed-data {
  margin-top: 8px;
}

.parsed-data strong {
  display: block;
  margin-bottom: 4px;
}

/* Button styling */
.log-replay-container ion-button {
  --border-radius: 8px;
}

.log-replay-container ion-grid {
  padding: 0;
}

.log-replay-container ion-row {
  margin-bottom: 16px;
}

.log-replay-container ion-row:last-child {
  margin-bottom: 0;
}

/* Range styling */
.log-replay-container ion-range {
  --bar-background: var(--ion-color-light);
  --bar-background-active: var(--ion-color-primary);
  --knob-background: var(--ion-color-primary);
}

/* Textarea styling */
.log-replay-container ion-textarea {
  --padding-start: 12px;
  --padding-end: 12px;
  --padding-top: 12px;
  --padding-bottom: 12px;
  border: 1px solid var(--ion-color-light-shade);
  border-radius: 8px;
  font-family: monospace;
  font-size: 0.9rem;
}

/* File input styling */
.log-replay-container input[type="file"] {
  display: none;
}

/* Badge styling */
.log-replay-container ion-badge {
  font-size: 0.8rem;
  padding: 4px 8px;
}

/* Card header improvements */
.log-replay-container ion-card-header {
  padding-bottom: 8px;
}

.log-replay-container ion-card-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 1.2rem;
  font-weight: 600;
}

/* Item improvements */
.log-replay-container ion-item {
  --border-color: transparent;
  --background: transparent;
  margin-bottom: 8px;
}

.log-replay-container ion-item:last-child {
  margin-bottom: 0;
}

/* Loading states */
.log-replay-container .loading-placeholder {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40px;
  color: var(--ion-color-medium);
}

/* Empty state */
.log-replay-container .empty-state {
  text-align: center;
  padding: 40px 20px;
  color: var(--ion-color-medium);
}

.log-replay-container .empty-state ion-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
}

/* Status indicators */
.log-replay-container .status-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 0.9rem;
  font-weight: 500;
}

.log-replay-container .status-indicator.playing {
  background-color: var(--ion-color-success-tint);
  color: var(--ion-color-success-shade);
}

.log-replay-container .status-indicator.paused {
  background-color: var(--ion-color-warning-tint);
  color: var(--ion-color-warning-shade);
}

.log-replay-container .status-indicator.stopped {
  background-color: var(--ion-color-medium-tint);
  color: var(--ion-color-medium-shade);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .log-replay-container {
    padding: 12px;
  }
  
  .progress-info,
  .time-info {
    font-size: 0.8rem;
  }
  
  .messages-container {
    max-height: 300px;
  }
  
  .messages-container h3 {
    font-size: 0.8rem;
  }
  
  .messages-container p {
    font-size: 0.7rem;
  }
  
  .connection-badge {
    margin-right: 8px;
    font-size: 0.7rem;
    padding: 2px 6px;
  }
}

@media (max-width: 480px) {
  .log-replay-container {
    padding: 8px;
  }
  
  .progress-info,
  .time-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
  
  .messages-container {
    max-height: 250px;
  }
}

/* Animation for live updates */
@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
  100% {
    opacity: 1;
  }
}

.log-replay-container .live-indicator {
  animation: pulse 2s infinite;
}

/* Scrollbar styling for webkit browsers */
.messages-container::-webkit-scrollbar {
  width: 6px;
}

.messages-container::-webkit-scrollbar-track {
  background: var(--ion-color-light);
  border-radius: 3px;
}

.messages-container::-webkit-scrollbar-thumb {
  background: var(--ion-color-medium);
  border-radius: 3px;
}

.messages-container::-webkit-scrollbar-thumb:hover {
  background: var(--ion-color-medium-shade);
}

/* Database updates section */
.database-updates {
  padding: 8px 0;
}

.database-updates ul {
  margin: 16px 0;
  padding-left: 20px;
}

.database-updates li {
  margin: 8px 0;
  font-size: 0.9rem;
  line-height: 1.4;
}

.database-updates p {
  margin: 12px 0;
  color: var(--ion-color-medium-shade);
}
