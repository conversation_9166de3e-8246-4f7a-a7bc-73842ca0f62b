# Real 24h Serres Format Parsing

## Overview

Based on your actual log examples, here's how the parsing now works for the real 24h serres format:

## Message Format Analysis

### ✅ **PIT IN Sequence**
```
r17768c1|si|           → Status field: pit in
r17768c2||43           → Rank: 43
r17768c6||             → S1 sector time (empty)
r17768c7||             → S2 sector time (empty)
r17768c9|tn|1:08.015   → Last lap time
r17768c10|ib|1:06.825  → Gap time
r17768c11|in|4.078     → Interval time
r17768c13|in|144       → Total laps: 144
r17768c14|to|00.       → On track time: 0 seconds (just entered pit)
r17768c15|in|7         → Pit count: 7 (7th pit stop)
```

### ✅ **PIT SECONDS COUNTER**
```
r17778c14|to|2:30.     → On track time: 2 minutes 30 seconds in pit
```

### ✅ **PIT OUT Sequence**
```
r17778c1|so|           → Status field: pit out
r17778c16|to|17:45     → Total time field
r17778|*out|0          → Special out message
```

## Parsing Logic Implementation

### ✅ **1. Status Field (c1) - Pit Entry/Exit Detection**
```typescript
case 'sta': // Status column
  if (competitorId) {
    if (data.type === 'si') {
      // Pit in: r17768c1|si|
      console.log(`🏁 PIT IN detected: ${competitorId} (status field si)`);
      await this.handlePitStatusUpdate(competitorId, 'IN');
    } else if (data.type === 'so') {
      // Pit out: r17778c1|so|
      console.log(`🏁 PIT OUT detected: ${competitorId} (status field so)`);
      await this.handlePitStatusUpdate(competitorId, 'OUT');
    }
  }
  break;
```

### ✅ **2. OTR Field (c14) - Pit Current Duration**
```typescript
case 'otr': // On track time column
  if (competitorId && data.value) {
    if (data.type === 'to') {
      // Pit current duration: r17778c14|to|2:30. or r17768c14|to|00.
      console.log(`⏱️ Pit current duration from otr: ${competitorId} -> ${data.value}`);
      await this.handlePitCurrentDurationUpdate(competitorId, data.value);
    }
  }
  break;
```

### ✅ **3. Pit Field (c15) - Total Pit Count**
```typescript
case 'pit': // Pit column - total pit number
  if (competitorId && data.value) {
    // Pit count: r17768c15|in|7 (7th pit stop)
    console.log(`🏁 Pit count update: ${competitorId} -> ${data.value} (total pit stops)`);
    await this.handlePitCountUpdate(competitorId, data.value);
  }
  break;
```

### ✅ **4. Duration Parser - Handles Dot Format**
```typescript
/**
 * Parse pit current duration from 24h serres format
 * Format: r id|c14|to|ss. or r id|c14|to|mm:ss.
 * Examples: "05." = 5 seconds, "2:30." = 150 seconds, "00." = 0 seconds
 */
private parsePitCurrentDuration(durationValue: string): number | null {
  // Remove trailing dot and trim
  const cleanValue = durationValue.replace(/\.$/, '').trim();
  
  // Format: mm:ss (like "2:30")
  if (cleanValue.includes(':')) {
    const parts = cleanValue.split(':');
    if (parts.length === 2 && parts[0] && parts[1]) {
      const minutes = parseInt(parts[0]) || 0;
      const seconds = parseInt(parts[1]) || 0;
      return (minutes * 60) + seconds;
    }
  }
  
  // Format: ss (just seconds, like "05" or "00")
  const seconds = parseInt(cleanValue);
  return isNaN(seconds) ? null : seconds;
}
```

## Expected Parsing Flow

### **Complete Pit Stop Sequence**
```
1. dyn1|text|5400000        → Set race duration: 90 minutes
2. dyn1|text|5385000        → Race time: 15 seconds elapsed

3. r17768c1|si|             → PIT IN detected
   → Creates pit stop with raceTimeAtPitIn: 15

4. r17768c14|to|00.         → Pit duration: 0 seconds (just entered)
   → Updates pitCurrentDuration: 0

5. r17768c15|in|7           → Pit count: 7th pit stop
   → Updates pit count (informational)

6. r17778c14|to|2:30.       → Pit duration: 150 seconds (2:30 in pit)
   → Updates pitCurrentDuration: 150

7. r17778c1|so|             → PIT OUT detected
   → Updates pit stop with raceTimeAtPitOut, pitDuration, etc.
```

### **Database Results**
```javascript
// Pit stop record
{
  pitInTime: "2024-11-09T15:30:45.000Z",
  pitOutTime: "2024-11-09T15:33:15.000Z",
  raceTimeAtPitIn: 15,        // ✅ From countdown timer
  raceTimeAtPitOut: 165,      // ✅ From countdown timer  
  pitCurrentDuration: 0,      // ✅ Reset on exit
  pitDuration: 150,           // ✅ Calculated from in/out times
  pitTotalDuration: 150,      // ✅ Calculated at pit out
  isActive: false             // ✅ Completed
}

// Team status
{
  name: "TEST TEAM 1",
  status: "on_track",         // ✅ Updated on pit out
  isActive: true
}
```

## Expected Logging Output

### **Pit In Detection**
```
🏁 PIT IN detected: 17768 (status field si)
🏁 handlePitUpdate called: websocketId=17768, pitStatus=IN
✅ Found competitor mapping: 17768 -> ObjectId(...)
🏁 Creating pit entry for: TEST TEAM 1 (ID: ObjectId(...))
⏱️ Race time from timer: 15s (duration: 5400000ms, left: 5385000ms)
✅ Pit entry recorded successfully: TEST TEAM 1
```

### **Pit Duration Updates**
```
🔍 OTR FIELD DETECTED: competitorId=17768, data.type=to, data.value=00.
⏱️ Pit current duration from otr: 17768 -> 00. (type: to)
⏱️ Updated pit current duration from otr: TEST TEAM 1 - 0s

🔍 OTR FIELD DETECTED: competitorId=17778, data.type=to, data.value=2:30.
⏱️ Pit current duration from otr: 17778 -> 2:30. (type: to)
⏱️ Updated pit current duration from otr: TEST TEAM 2 - 150s
```

### **Pit Count Updates**
```
🔍 PIT FIELD DETECTED: competitorId=17768, data.type=in, data.value=7
🏁 Pit count update: 17768 -> 7 (total pit stops)
```

### **Pit Out Detection**
```
🏁 PIT OUT detected: 17778 (status field so)
🏁 handlePitUpdate called: websocketId=17778, pitStatus=OUT
🏁 Processing pit exit for: TEST TEAM 2
⏱️ Race time from timer: 165s (duration: 5400000ms, left: 5235000ms)
🏁 Pit exit recorded: TEST TEAM 2 - Duration: 150.0s - Race time: 165s
```

## Key Features

### ✅ **Accurate Field Mapping**
- `c1` with `si`/`so` → Pit entry/exit detection
- `c14` with `to` → Pit current duration (with dot format)
- `c15` with `in` → Total pit count

### ✅ **Format Handling**
- Handles trailing dots: `"2:30."` → 150 seconds
- Supports mm:ss format: `"2:30"` → 150 seconds  
- Supports ss format: `"05"` → 5 seconds

### ✅ **Race Time Integration**
- Uses countdown timer for accurate race time
- Syncs with dyn1 messages
- Provides race time context for pit events

### ✅ **Real-time Updates**
- Pit current duration updates in real-time
- Team status reflects current pit state
- Comprehensive logging for debugging

## Test Script

Run the test to verify parsing:

```bash
cd backend
npm run build
node scripts/test-real-format.js
```

**Expected Output**:
```
✅ All parsing features are working correctly!
   Pit IN detection: ✅ WORKING
   Pit duration update: ✅ WORKING  
   Race time calculation: ✅ WORKING
   Team status update: ✅ WORKING
```

The parser now correctly handles the actual 24h serres format with proper field mapping and duration parsing!
