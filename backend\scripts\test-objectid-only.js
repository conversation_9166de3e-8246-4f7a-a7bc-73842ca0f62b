#!/usr/bin/env node

/**
 * Test script to verify ObjectId-only approach works correctly
 * Tests that competitors use only ObjectId references, no string competitorId
 */

const mongoose = require('mongoose');

async function testObjectIdOnly() {
  console.log('🧪 Testing ObjectId-Only Approach...');
  
  try {
    // Connect to database
    const mongoUri = process.env.MONGO_URI || 'mongodb+srv://manuelbiancolilla:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0';
    await mongoose.connect(mongoUri);
    console.log('✅ Connected to database');
    
    // Import the optimized models
    const { ApexSession, ApexTeam, ApexKart, ApexCompetitor, ApexLap } = require('../build/models/ApexModels');
    
    // Clean up test data
    const testSessionName = 'test-objectid-only';
    await ApexSession.deleteMany({ title1: testSessionName });
    await ApexTeam.deleteMany({});
    await ApexKart.deleteMany({});
    await ApexCompetitor.deleteMany({});
    await ApexLap.deleteMany({});
    console.log('🧹 Cleaned up test data');
    
    // Create test session
    const session = await ApexSession.create({
      title1: testSessionName,
      title2: 'ObjectId Only Test',
      track: 'Test Track',
      isActive: true
    });
    console.log(`✅ Session created: ${session._id}`);
    
    // Create test team
    const team = await ApexTeam.create({
      sessionId: session._id,
      name: 'GIARRATANA',
      drivers: ['GIARRATANA'],
      nationality: '',
      totalLaps: 0,
      status: 'active',
      isActive: true
    });
    console.log(`✅ Team created: ${team._id}`);
    
    // Create test kart
    const kart = await ApexKart.create({
      sessionId: session._id,
      kartNumber: 1,
      speed: 3,
      currentTeamId: team._id,
      status: 'available',
      isActive: true
    });
    console.log(`✅ Kart created: ${kart._id}`);
    
    // Test 1: Create competitor with websocketId (no string competitorId)
    console.log('\n📝 Test 1: Creating competitor with websocketId only...');
    const competitor = await ApexCompetitor.create({
      sessionId: session._id,
      websocketId: '17742', // Websocket ID for mapping
      teamId: team._id, // ObjectId reference
      kartId: kart._id, // ObjectId reference
      name: 'GIARRATANA',
      nationality: '',
      drivers: ['GIARRATANA'],
      isActive: true
    });
    console.log(`✅ Competitor created: ${competitor._id}`);
    console.log(`   websocketId: ${competitor.websocketId} (string for websocket mapping)`);
    console.log(`   teamId: ${competitor.teamId} (ObjectId)`);
    console.log(`   kartId: ${competitor.kartId} (ObjectId)`);
    console.log(`   NO competitorId field ✅`);
    
    // Test 2: Create lap with competitor ObjectId reference
    console.log('\n📝 Test 2: Creating lap with competitor ObjectId reference...');
    const lap = await ApexLap.create({
      sessionId: session._id,
      competitorId: competitor._id, // ObjectId reference to competitor
      kartId: kart._id, // ObjectId reference to kart
      lapNumber: 1,
      lapTime: 71328, // milliseconds
      lapTimeFormatted: '1:11.328',
      isBestLap: false,
      isPersonalBest: false,
      timestamp: new Date()
    });
    console.log(`✅ Lap created: ${lap._id}`);
    console.log(`   competitorId: ${lap.competitorId} (ObjectId reference)`);
    console.log(`   kartId: ${lap.kartId} (ObjectId reference)`);
    console.log(`   NO string competitorId ✅`);
    
    // Test 3: Test websocket ID to ObjectId mapping
    console.log('\n📝 Test 3: Testing websocket ID to ObjectId mapping...');
    const websocketToCompetitorMap = new Map();
    websocketToCompetitorMap.set(competitor.websocketId, competitor._id);
    
    // Simulate websocket message processing
    const websocketId = '17742';
    const competitorObjectId = websocketToCompetitorMap.get(websocketId);
    
    if (competitorObjectId) {
      console.log(`✅ Websocket mapping works: "${websocketId}" -> ${competitorObjectId}`);
      
      // Find competitor by ObjectId
      const foundCompetitor = await ApexCompetitor.findById(competitorObjectId);
      if (foundCompetitor) {
        console.log(`✅ Competitor found by ObjectId: ${foundCompetitor.name}`);
      }
    }
    
    // Test 4: Test population queries with ObjectId references
    console.log('\n📝 Test 4: Testing population queries...');
    const populatedLap = await ApexLap.findById(lap._id)
      .populate('competitorId')
      .populate('kartId');
    
    console.log('✅ Population query successful:');
    console.log(`   Lap time: ${populatedLap.lapTimeFormatted}`);
    console.log(`   Competitor: ${populatedLap.competitorId.name}`);
    console.log(`   Kart: ${populatedLap.kartId.kartNumber}`);
    
    // Test 5: Test complex query with multiple ObjectId joins
    console.log('\n📝 Test 5: Testing complex queries...');
    const lapsWithDetails = await ApexLap.find({ sessionId: session._id })
      .populate({
        path: 'competitorId',
        populate: {
          path: 'teamId',
          model: 'ApexTeam'
        }
      })
      .populate('kartId');
    
    if (lapsWithDetails.length > 0) {
      const lapWithDetails = lapsWithDetails[0];
      console.log('✅ Complex query successful:');
      console.log(`   Lap time: ${lapWithDetails.lapTimeFormatted}`);
      console.log(`   Competitor: ${lapWithDetails.competitorId.name}`);
      console.log(`   Team: ${lapWithDetails.competitorId.teamId.name}`);
      console.log(`   Kart: ${lapWithDetails.kartId.kartNumber}`);
    }
    
    // Test 6: Verify no string competitorId fields exist
    console.log('\n📝 Test 6: Verifying schema integrity...');
    const competitorDoc = competitor.toObject();
    const lapDoc = lap.toObject();
    
    // Check that old string competitorId field doesn't exist
    if (!competitorDoc.hasOwnProperty('competitorId')) {
      console.log('✅ ApexCompetitor has no string competitorId field');
    } else {
      console.log('❌ ApexCompetitor still has string competitorId field');
    }
    
    if (typeof lapDoc.competitorId === 'object') {
      console.log('✅ ApexLap.competitorId is ObjectId reference');
    } else {
      console.log('❌ ApexLap.competitorId is still string');
    }
    
    // Clean up test data
    await ApexSession.deleteMany({ title1: testSessionName });
    await ApexTeam.deleteMany({ sessionId: session._id });
    await ApexKart.deleteMany({ sessionId: session._id });
    await ApexCompetitor.deleteMany({ sessionId: session._id });
    await ApexLap.deleteMany({ sessionId: session._id });
    console.log('\n🧹 Cleaned up test data');
    
    console.log('\n🎉 ObjectId-only approach test completed!');
    console.log('\n📝 Summary:');
    console.log('   ✅ Competitors use websocketId for websocket mapping');
    console.log('   ✅ All references use ObjectId (no string IDs)');
    console.log('   ✅ Population queries work correctly');
    console.log('   ✅ Complex joins work efficiently');
    console.log('   ✅ Schema integrity maintained');
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  } finally {
    await mongoose.disconnect();
    console.log('👋 Disconnected from database');
  }
}

// Run the test
if (require.main === module) {
  testObjectIdOnly().catch(console.error);
}

module.exports = { testObjectIdOnly };
