#!/usr/bin/env ts-node

/**
 * Test script to verify the real-world pit detection fix
 */

import mongoose from 'mongoose';
import { ApexParserSimple } from '../src/services/apexParserSimple';
import { ApexSession, ApexTeam, ApexKart, ApexCompetitor, ApexPitStop } from '../src/models/ApexModels';

// Test configuration
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/raceplanner';

async function connectToDatabase() {
  try {
    await mongoose.connect(MONGODB_URI);
    console.log('✅ Connected to MongoDB');
  } catch (error) {
    console.error('❌ Failed to connect to MongoDB:', error);
    process.exit(1);
  }
}

async function cleanupTestData() {
  console.log('🧹 Cleaning up test data...');
  await ApexSession.deleteMany({ title1: { $regex: /Real World Pit Fix/ } });
  await ApexTeam.deleteMany({ name: { $regex: /VANHAT KOIRAT/ } });
  await ApexKart.deleteMany({ kartNumber: 25 });
  await ApexCompetitor.deleteMany({ websocketId: '17788' });
  await ApexPitStop.deleteMany({});
}

async function testRealWorldPitFix() {
  console.log('\n🔍 Testing Real World Pit Fix...\n');

  const parser = new ApexParserSimple({ enableLogging: true });

  // Create session with exact format from your logs
  const gridMessage = `init|r|
title1||Real World Pit Fix Test
title2||data.type=in fix
track||Test Track
grid||<tbody><tr data-id="r0"><td data-id="c1" data-type="sta">N/A</td><td data-id="c2" data-type="rk">Rnk</td><td data-id="c3" data-type="no">Kart</td><td data-id="c5" data-type="dr">Team</td><td data-id="c15" data-type="pit">Pits</td></tr><tr data-id="r17788"><td data-id="r17788c1">N/A</td><td data-id="r17788c2">1</td><td data-id="r17788c3">25</td><td data-id="r17788c5">VANHAT KOIRAT</td><td data-id="r17788c15">0</td></tr></tbody>`;

  console.log('📊 Creating session and competitor...');
  await parser.parseMessage(gridMessage);

  // Wait for database operations to complete
  await new Promise(resolve => setTimeout(resolve, 1000));

  console.log('\n🏁 Testing exact message from your logs...\n');

  // Test the exact message format from your logs
  console.log('--- Sending: r17788c15|in|6 ---');
  await parser.parseMessage('r17788c15|in|6');
  
  await new Promise(resolve => setTimeout(resolve, 1000));

  console.log('\n--- Sending: r17788c15|in|0 (pit exit) ---');
  await parser.parseMessage('r17788c15|in|0');

  await new Promise(resolve => setTimeout(resolve, 1000));

  // Check database results
  console.log('\n📊 Checking database results...');
  
  const sessions = await ApexSession.find({ title1: { $regex: /Real World Pit Fix/ } }).lean();
  console.log(`Sessions created: ${sessions.length}`);
  
  if (sessions.length > 0) {
    const session = sessions[0];
    console.log(`✅ Session: ${session._id} - ${session.title1}`);
    
    const competitors = await ApexCompetitor.find({ sessionId: session._id }).lean();
    console.log(`Competitors: ${competitors.length}`);
    
    if (competitors.length > 0) {
      console.log(`✅ Competitor: ${competitors[0].name} (websocketId: ${competitors[0].websocketId})`);
    }
    
    const pitStops = await ApexPitStop.find({ sessionId: session._id }).lean();
    console.log(`🏁 Pit stops created: ${pitStops.length}`);
    
    if (pitStops.length > 0) {
      console.log('✅ Pit stop details:');
      pitStops.forEach((pit, index) => {
        console.log(`   ${index + 1}. Competitor: ${pit.competitorId}`);
        console.log(`      Pit In: ${pit.pitInTime}`);
        console.log(`      Pit Out: ${pit.pitOutTime || 'ACTIVE'}`);
        console.log(`      Duration: ${pit.pitDuration || 'N/A'}s`);
        console.log(`      Current Duration: ${pit.pitCurrentDuration || 0}s`);
        console.log(`      Race Time In: ${pit.raceTimeAtPitIn || 'N/A'}s`);
        console.log(`      Race Time Out: ${pit.raceTimeAtPitOut || 'N/A'}s`);
        console.log(`      Active: ${pit.isActive}`);
      });
    } else {
      console.log('❌ No pit stops were created!');
      
      // Debug: Check if competitors exist
      const allCompetitors = await ApexCompetitor.find({}).lean();
      console.log(`\n🔍 Debug - All competitors in database: ${allCompetitors.length}`);
      allCompetitors.forEach(comp => {
        console.log(`   - ${comp.name} (websocketId: ${comp.websocketId}, ObjectId: ${comp._id})`);
      });
    }
    
    return {
      sessionId: session._id,
      competitorsCreated: competitors.length,
      pitStopsCreated: pitStops.length
    };
  } else {
    console.log('❌ No sessions were created');
    return {
      sessionId: null,
      competitorsCreated: 0,
      pitStopsCreated: 0
    };
  }
}

async function main() {
  try {
    await connectToDatabase();
    await cleanupTestData();
    
    const results = await testRealWorldPitFix();
    
    console.log('\n📊 Test Results Summary:');
    console.log(`   Session ID: ${results.sessionId}`);
    console.log(`   Competitors Created: ${results.competitorsCreated}`);
    console.log(`   Pit Stops Created: ${results.pitStopsCreated}`);
    
    if (results.pitStopsCreated > 0) {
      console.log('\n✅ Real world pit fix test passed!');
    } else {
      console.log('\n❌ Real world pit fix test failed - no pit stops created!');
      console.log('\n🔍 This means the data.type=in routing is still not working correctly.');
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  } finally {
    await cleanupTestData();
    await mongoose.disconnect();
    console.log('🔌 Disconnected from MongoDB');
  }
}

// Run the test
if (require.main === module) {
  main().catch(console.error);
}
