import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import { Team, Kart, Row, AssignmentLog, StrategyData } from '../types'; // StintInfo removed as it's not exported from ../types
import { StintInfo } from '../pages/RaceStrategy/types'; // Import StintInfo

// Create a base API URL from environment variable
const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:5000';

// Create axios instance with default config
const apiClient: AxiosInstance = axios.create({
  baseURL: API_BASE_URL,
  timeout: 15000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor for adding auth token
apiClient.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor for handling common errors
apiClient.interceptors.response.use(
  (response) => response,
  (error) => {
    // Handle authentication errors
    if (error.response && error.response.status === 401) {
      // Clear token and redirect to login
      localStorage.removeItem('token');
      window.location.href = '/login';
    }
    
    // Log errors in development
    if (import.meta.env.NODE_ENV !== 'production') {
      console.error('API Error:', error.response || error);
    }
    
    return Promise.reject(error);
  }
);

// Generic API request method
const apiRequest = async <T>(config: AxiosRequestConfig): Promise<T> => {
  try {
    const response: AxiosResponse<T> = await apiClient(config);
    return response.data;
  } catch (error) {
    console.error(`API Error in ${config.method?.toUpperCase()} ${config.url}:`, error);
    throw error;
  }
};

// ===== AUTH API =====
export const authApi = {
  login: async (email: string, password: string) => {
    return apiRequest<{ token: string; user: { id: string; name: string; email: string } }>({
      method: 'post',
      url: '/auth/login',
      data: { email, password },
    });
  },
  
  register: async (name: string, email: string, password: string) => {
    return apiRequest<{ message: string }>({
      method: 'post',
      url: '/auth/register',
      data: { name, email, password },
    });
  },
  
  getCurrentUser: async () => {
    return apiRequest<{ user: { id: string; name: string; email: string; preferences: any } }>({
      method: 'get',
      url: '/auth/me',
    });
  },

  updatePreferences: async (preferences: { useApexDatabase: boolean; selectedSessionId: string | null; selectedTeamId: string | null }) => {
    return apiRequest<{ success: boolean; message: string; preferences: any }>({
      method: 'put',
      url: '/auth/preferences',
      data: { preferences },
    });
  },
};

// ===== SMART TEAMS API (Auto-redirects to apex when enabled) =====
export const teamsApi = {
  getAll: async (populate?: string, useApex?: boolean, sessionId?: string) => {
    if (useApex && sessionId) {
      return apiRequest<Team[]>({
        method: 'get',
        url: `/apex/sessions/${sessionId}/teams`,
      });
    }
    return apiRequest<Team[]>({
      method: 'get',
      url: '/teams',
      params: populate ? { populate } : undefined,
    });
  },
  
  getById: async (id: string, populate?: string) => {
    return apiRequest<Team>({
      method: 'get',
      url: `/teams/${id}`,
      params: populate ? { populate } : undefined,
    });
  },
  
  create: async (teamData: Partial<Team>) => {
    return apiRequest<Team>({
      method: 'post',
      url: '/teams',
      data: teamData,
    });
  },
  
  update: async (id: string, teamData: Partial<Team>) => {
    return apiRequest<Team>({
      method: 'put',
      url: `/teams/${id}`,
      data: teamData,
    });
  },
  
  delete: async (id: string) => {
    return apiRequest<{ message: string }>({
      method: 'delete',
      url: `/teams/${id}`,
    });
  },
};

// ===== SMART KARTS API (Auto-redirects to apex when enabled) =====
export const kartsApi = {
  getAll: async (populate?: string, useApex?: boolean, sessionId?: string) => {
    if (useApex && sessionId) {
      return apiRequest<Kart[]>({
        method: 'get',
        url: `/apex/sessions/${sessionId}/karts`,
      });
    }
    return apiRequest<Kart[]>({
      method: 'get',
      url: '/karts',
      params: populate ? { populate } : undefined,
    });
  },
  
  getById: async (id: string, populate?: string) => {
    return apiRequest<Kart>({
      method: 'get',
      url: `/karts/${id}`,
      params: populate ? { populate } : undefined,
    });
  },
  
  create: async (kartData: Partial<Kart>) => {
    return apiRequest<Kart>({
      method: 'post',
      url: '/karts',
      data: kartData,
    });
  },
  
  update: async (id: string, kartData: Partial<Kart>, useApex?: boolean, sessionId?: string) => {
    if (useApex && sessionId) {
      return apiRequest<Kart>({
        method: 'put',
        url: `/apex/sessions/${sessionId}/karts/${id}`,
        data: kartData,
      });
    }
    return apiRequest<Kart>({
      method: 'put',
      url: `/karts/${id}`,
      data: kartData,
    });
  },

  // New method to assign a kart to a team
  assignToTeam: async (kartId: string, teamId: string) => {
    return apiRequest<Kart>({ // Assuming the backend returns the updated Kart
      method: 'put', // Backend uses PUT
      url: `/karts/${kartId}/assign/${teamId}`, // Corrected Backend endpoint
      data: { teamId }, // teamId in the request body
    });
  },

  // New method to unassign a kart from its team
  unassignFromTeam: async (kartId: string) => {
    return apiRequest<{ message: string }>({ method: 'delete', url: `/karts/${kartId}/assign` }); // Backend uses DELETE
  },
  
  delete: async (id: string) => {
    return apiRequest<{ message: string }>({
      method: 'delete',
      url: `/karts/${id}`,
    });
  },
    
  assignToRow: async (kartId: string, rowId: string) => {
    return apiRequest<Kart>({
      method: 'put',
      url: `/karts/${kartId}/assign-row`,
      data: { rowId },
    });
  },
  };

// ===== ROWS API =====
export const rowsApi = {
  getAll: async (populate?: string, useApexDatabase?: boolean, sessionId?: string) => {
    const params: any = {};
    if (populate) params.populate = populate;
    if (useApexDatabase) params.useApexDatabase = 'true';
    if (sessionId) params.sessionId = sessionId;

    return apiRequest<Row[]>({
      method: 'get',
      url: '/rows',
      params: Object.keys(params).length > 0 ? params : undefined,
    });
  },
  
  getById: async (id: string, populate?: string) => {
    return apiRequest<Row>({
      method: 'get',
      url: `/rows/${id}`,
      params: populate ? { populate } : undefined,
    });
  },
  
  create: async (rowData: Partial<Row>) => {
    return apiRequest<Row>({
      method: 'post',
      url: '/rows',
      data: rowData,
    });
  },
  
  // Update method now uses rowNumber in the URL to match backend route
  update: async (rowNumber: number, rowData: Partial<Row>) => {
    return apiRequest<Row>({
      method: 'put',
      url: `/rows/${rowNumber}`, // Use rowNumber in the URL
      data: rowData,
    });
  },
  
  delete: async (id: string) => {
    return apiRequest<{ message: string }>({
      method: 'delete',
      url: `/rows/${id}`,
    });
  },

  // Method to assign a kart to a row
  assignKart: async (rowId: string, kartId: string) => {
    return apiRequest<{ message: string; row: Row; kart: Kart }>({ // Assuming backend returns message and updated entities
      method: 'post', // Or 'post' depending on backend
      url: `/rows/${rowId}/assign-kart/${kartId}`,
    });
  },

  // Method to unassign a kart from a row
  unassignKart: async (rowId: string) => {
    return apiRequest<{ message: string; row: Row; kart?: Kart }>({ // Kart might be nullified or returned
      method: 'delete', // Or 'put' to nullify kartId field
      url: `/rows/${rowId}/unassign-kart`,
    });
  },

  // Method to swap a team's kart with a row's kart (or assign team's kart to empty row)
  swapTeamKart: async (rowId: string, teamId: string) => {
    return apiRequest<{ message: string; updatedRow: Row; updatedTeam: Team; originalRowKart?: Kart; originalTeamKart?: Kart }>({
      method: 'post', // Or 'put'
      url: `/rows/${rowId}/swap-team-kart/${teamId}`,
    });
  },

  // Smart swap that works with both regular and apex teams
  smartSwapTeamKart: async (rowId: string, teamId: string, sessionId?: string) => {
    return apiRequest<{ message: string; updatedRow: Row; updatedTeam: Team; originalRowKart?: Kart; originalTeamKart?: Kart }>({
      method: 'post',
      url: `/rows/${rowId}/smart-swap/${teamId}`,
      params: sessionId ? { sessionId } : undefined,
    });
  },
};

// ===== ASSIGNMENT HISTORY API =====
export const assignmentHistoryApi = {
  getAll: async (populate?: string) => {
    return apiRequest<AssignmentLog[]>({
      method: 'get',
      url: '/assignment-history',
      params: populate ? { populate } : undefined,
    });
  },
  
  getById: async (id: string, populate?: string) => {
    return apiRequest<AssignmentLog>({
      method: 'get',
      url: `/assignment-history/${id}`,
      params: populate ? { populate } : undefined,
    });
  },
};

// ===== STRATEGY API =====
export const strategyApi = {
  getActive: async () => {
    return apiRequest<StrategyData>({
      method: 'get',
      url: '/strategy',
    });
  },
  
  getAll: async () => {
    return apiRequest<StrategyData[]>({
      method: 'get',
      url: '/strategy/all',
    });
  },
  
  getById: async (id: string) => {
    return apiRequest<StrategyData>({
      method: 'get',
      url: `/strategy/${id}`,
    });
  },
  
  create: async (strategyData: Partial<StrategyData>) => {
    return apiRequest<StrategyData>({
      method: 'post',
      url: '/strategy',
      data: strategyData,
    });
  },
  
  update: async (id: string, strategyData: Partial<StrategyData>) => {
    return apiRequest<StrategyData>({
      method: 'put',
      url: `/strategy/${id}`,
      data: strategyData,
    });
  },
  
  delete: async (id: string) => {
    return apiRequest<{ message: string }>({
      method: 'delete',
      url: `/strategy/${id}`,
    });
  },
  
  activate: async (id: string) => {
    return apiRequest<StrategyData>({
      method: 'put',
      url: `/strategy/${id}/activate`,
    });
  },
  
  duplicate: async (id: string) => {
    return apiRequest<StrategyData>({
      method: 'post',
      url: `/strategy/${id}/duplicate`,
    });
  },
  
  calculateStints: async (id: string, modifiedStints?: Partial<StintInfo>[]) => {
    return apiRequest<StrategyData>({
      method: 'post',
      url: `/strategy/${id}/calculate-stints`,
      data: { modifiedStints },
    });
  },
  
  startRace: async (id?: string, data?: Record<string, unknown>) => {
    return apiRequest<StrategyData>({
      method: 'post',
      url: id ? `/strategy/${id}/start` : '/strategy/start', // Use ID if provided for specific strategy start
      data: data || {}
    });
  },
  
  stopRace: async () => {
    return apiRequest<{ message: string; strategy: StrategyData }>({
      method: 'post',
      url: '/strategy/stop',
    });
  },
};

// ===== STINT API =====
export const stintApi = {
  getAll: async (strategyId: string) => {
    return apiRequest<StintInfo[]>({
      method: 'get',
      url: '/stint',
      params: { strategyId },
    });
  },
  
  getById: async (id: string) => {
    return apiRequest<StintInfo>({
      method: 'get',
      url: `/stint/${id}`,
    });
  },
  
  update: async (id: string, stintData: Partial<StintInfo>) => {
    return apiRequest<StintInfo>({
      method: 'put',
      url: `/stint/${id}`,
      data: stintData,
    });
  },
  
  // Add a new function to update stint actuals
  updateActuals: async (id: string, actualPitEntryTime: number, actualPitDuration: number) => {
    return apiRequest<{stint: StintInfo, stints: StintInfo[]}>({
      method: 'put',
      url: `/stint/${id}`,
      data: {
        actualPitEntryTime, // Changed from actualStintDuration to actualPitEntryTime
        actualPitDuration,
        isUserModified: true
      },
    });
  }
};

// ===== PITLANE API =====
export const pitlaneApi = {
  getStatus: async () => {
    return apiRequest<unknown>({ // Use unknown for better type safety than any
      method: 'get',
      url: '/pitlane/status',
    });
  },
  
  kartEntersPitlane: async (kartId: string) => { // Renamed to match backend
    return apiRequest<unknown>({ // Use unknown for better type safety than any
      method: 'post',
      url: '/pitlane/enter', // Assuming this is the endpoint from pitlaneController.ts
      data: { kartId },
    });
  },
  
  kartExitsPitlane: async (kartId: string) => { // Renamed to match backend
    return apiRequest<unknown>({ // Use unknown for better type safety than any
      method: 'post',
      url: '/pitlane/exit', // Assuming this is the endpoint from pitlaneController.ts
      data: { kartId },
    });
  },
};

// ===== ADMIN API =====
export const adminApi = {
  // Method to unassign all karts from teams (from adminController)
  unassignAllKartsFromTeams: async () => apiRequest<{ message: string; unassignedKartCount: number; updatedTeamCount: number; }>({
    method: 'post', url: '/admin/karts/unassign-all-from-teams'
  }),
  resetAssignments: async () => apiRequest<{ message: string; logsDeletedCount: number; }>({ method: 'post', url: '/admin/reset-assignments' }),
  deleteAllTeamsAndKarts: async () => apiRequest<{ message: string; deletedTeams: number; deletedKarts: number; }>({ method: 'delete', url: '/admin/teams-and-karts/all' }),
  resetKartSpeeds: async (speed?: number) => apiRequest<{ message: string; matchedCount: number; modifiedCount: number; resetValue: number; }>({ method: 'post', url: '/admin/karts/reset-speeds', data: { speed } }),
  batchCreateKarts: async (data: { startNumber: number; endNumber: number }) => apiRequest<{ message: string; createdCount: number; skippedCount: number; skippedNumbers: number[]; }>({ method: 'post', url: '/admin/karts/batch-create', data }),
  // Add other admin endpoints as needed, e.g., for specific kart status updates if they are admin-only
  moveKartToMaintenance: async (kartId: string) => apiRequest<{ message: string, kart: Kart }>({ method: 'post', url: `/admin/karts/${kartId}/set-maintenance`}),
};

// ===== APEX PARSER API =====
export const apexApi = {
  // Parse log file from server (NEW SIMPLIFIED PARSER)
  parseLogFile: async (filePath: string, sessionId?: string, raceId?: string) => {
    return apiRequest<{ success: boolean; message: string; session?: any }>({
      method: 'post',
      url: '/apex/parse-log-file-simple',
      data: { filePath, sessionId, raceId },
    });
  },

  // Parse log file from server (OLD COMPLEX PARSER - DEPRECATED)
  parseLogFileOld: async (filePath: string, sessionId?: string, raceId?: string) => {
    return apiRequest<{ success: boolean; message: string; session?: any }>({
      method: 'post',
      url: '/apex/parse-log-file',
      data: { filePath, sessionId, raceId },
    });
  },

  // Parse custom log content
  parseLog: async (logContent: string, sessionId?: string, raceId?: string) => {
    return apiRequest<{ success: boolean; message: string; session?: any }>({
      method: 'post',
      url: '/apex/parse-log',
      data: { logContent, sessionId, raceId },
    });
  },

  // Get all sessions
  getSessions: async () => {
    return apiRequest<any[]>({
      method: 'get',
      url: '/apex/sessions',
    });
  },

  // Get session by ID
  getSession: async (sessionId: string) => {
    return apiRequest<any>({
      method: 'get',
      url: `/apex/sessions/${sessionId}`,
    });
  },

  // Get session statistics
  getSessionStats: async (sessionId: string) => {
    return apiRequest<any>({
      method: 'get',
      url: `/apex/sessions/${sessionId}/stats`,
    });
  },

  // Get teams for a session
  getTeams: async (sessionId: string) => {
    return apiRequest<any[]>({
      method: 'get',
      url: `/apex/sessions/${sessionId}/teams`,
    });
  },

  // Get karts for a session
  getKarts: async (sessionId: string) => {
    return apiRequest<any[]>({
      method: 'get',
      url: `/apex/sessions/${sessionId}/karts`,
    });
  },

  // Get competitors for a session
  getCompetitors: async (sessionId: string) => {
    return apiRequest<any[]>({
      method: 'get',
      url: `/apex/sessions/${sessionId}/competitors`,
    });
  },

  // Get laps for a session
  getLaps: async (sessionId: string, competitorId?: string, limit?: number) => {
    return apiRequest<any[]>({
      method: 'get',
      url: `/apex/sessions/${sessionId}/laps`,
      params: { competitorId, limit },
    });
  },

  // Get pit stops for a session
  getPitStops: async (sessionId: string, competitorId?: string) => {
    return apiRequest<any[]>({
      method: 'get',
      url: `/apex/sessions/${sessionId}/pitstops`,
      params: { competitorId },
    });
  },

  // Get live data for a session
  getLiveData: async (sessionId: string) => {
    return apiRequest<any[]>({
      method: 'get',
      url: `/apex/sessions/${sessionId}/live`,
    });
  },

  // Delete session
  deleteSession: async (sessionId: string) => {
    return apiRequest<{ success: boolean; message: string }>({
      method: 'delete',
      url: `/apex/sessions/${sessionId}`,
    });
  },

  // Get available log files
  getLogFiles: async () => {
    return apiRequest<Array<{ name: string; size: number; modified: string }>>({
      method: 'get',
      url: '/apex/log-files',
    });
  },
};


// Export a default object with all API services
const apiService = {
  auth: authApi,
  teams: teamsApi,
  karts: kartsApi,
  rows: rowsApi,
  assignmentHistory: assignmentHistoryApi,
  strategy: strategyApi,
  stint: stintApi,
  pitlane: pitlaneApi,
  admin: adminApi,
  apex: apexApi, // Add apexApi

  // Generic methods for direct API calls
  get: async <T = any>(url: string, params?: any): Promise<T> => {
    return apiRequest<T>({ method: 'get', url, params });
  },

  post: async <T = any>(url: string, data?: any): Promise<T> => {
    return apiRequest<T>({ method: 'post', url, data });
  },

  put: async <T = any>(url: string, data?: any): Promise<T> => {
    return apiRequest<T>({ method: 'put', url, data });
  },

  delete: async <T = any>(url: string): Promise<T> => {
    return apiRequest<T>({ method: 'delete', url });
  },
};

export default apiService;




