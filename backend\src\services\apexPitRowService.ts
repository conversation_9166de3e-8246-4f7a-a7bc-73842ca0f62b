/**
 * Service for managing apex karts for pit rows
 * Creates apex karts that correspond to existing pit rows when using apex mode
 */

import { ApexKart, ApexSession } from '../models/ApexModels';
import { Row } from '../models/Row';
import { Kart } from '../models/Kart';
import mongoose from 'mongoose';

export class ApexPitRowService {
  
  /**
   * Create apex karts for all existing pit rows in a NEW session
   * Each session gets its own set of pit row karts
   */
  static async createApexKartsForPitRows(sessionId: string): Promise<void> {
    try {
      // Validate session exists with retry logic
      let session = await ApexSession.findById(sessionId);

      // If session not found, wait a bit and try again (might be a timing issue)
      if (!session) {
        console.log(`Session ${sessionId} not found, waiting and retrying...`);
        await new Promise(resolve => setTimeout(resolve, 200));
        session = await ApexSession.findById(sessionId);
      }

      if (!session) {
        throw new Error(`Apex session ${sessionId} not found after retry`);
      }

      console.log(`✅ Found session ${sessionId}: ${session.title1}`);

      // Get all existing pit rows
      const pitRows = await Row.find({}).sort({ rowNumber: 1 });

      if (pitRows.length === 0) {
        console.log('ℹ️ No pit rows found to create apex karts for (this is normal for new installations)');
        return;
      }

      console.log(`Creating apex karts for ${pitRows.length} pit rows in NEW session ${sessionId}`);

      // For new sessions, always create fresh karts for pit rows
      // No need to check existing since each session is independent
      const kartsToCreate = [];
      
      for (const row of pitRows) {
        // Generate a unique kart number for this pit row (use row number + 1000 to avoid conflicts)
        const pitRowKartNumber = 1000 + row.rowNumber;

        // Create apex kart for this pit row (always create for new sessions)
        kartsToCreate.push({
          sessionId: new mongoose.Types.ObjectId(sessionId),
          kartNumber: pitRowKartNumber,
          speed: 3, // Default speed level
          currentTeamId: null, // Not assigned to any team initially
          currentRowId: row._id, // Assign to this pit row
          status: 'in_pit_row', // Special status for pit row karts
          isActive: true
        });
      }

      if (kartsToCreate.length > 0) {
        const createdApexKarts = await ApexKart.insertMany(kartsToCreate);
        console.log(`✅ Created ${createdApexKarts.length} NEW apex karts for pit rows in session ${sessionId}`);
        console.log(`   Kart numbers: ${createdApexKarts.map(k => k.kartNumber).join(', ')}`);

        // Update pit rows to reference the apex karts directly
        const rowUpdates = [];
        for (let i = 0; i < createdApexKarts.length; i++) {
          const apexKart = createdApexKarts[i];
          if (apexKart && apexKart.kartNumber) {
            const rowNumber = apexKart.kartNumber - 1000; // Extract row number from kart number

            rowUpdates.push({
              updateOne: {
                filter: { rowNumber: rowNumber },
                update: { $set: { currentKartId: apexKart._id } }
              }
            });
          }
        }

        if (rowUpdates.length > 0) {
          const { Row } = await import('../models/Row');
          await Row.bulkWrite(rowUpdates);
          console.log(`✅ Updated ${rowUpdates.length} pit rows with apex kart references`);
        }
      } else {
        console.log('All pit rows already have corresponding apex karts');
      }

    } catch (error) {
      console.error('Error creating apex karts for pit rows:', error);
      throw error;
    }
  }

  /**
   * Get apex kart for a specific pit row
   */
  static async getApexKartForPitRow(sessionId: string, rowNumber: number): Promise<any> {
    const pitRowKartNumber = 1000 + rowNumber;
    
    return await ApexKart.findOne({
      sessionId: new mongoose.Types.ObjectId(sessionId),
      kartNumber: pitRowKartNumber
    });
  }

  /**
   * Update apex kart assignment when pit row assignment changes
   */
  static async updateApexKartAssignment(
    sessionId: string, 
    rowNumber: number, 
    teamId: string | null
  ): Promise<void> {
    const pitRowKartNumber = 1000 + rowNumber;
    
    await ApexKart.findOneAndUpdate(
      {
        sessionId: new mongoose.Types.ObjectId(sessionId),
        kartNumber: pitRowKartNumber
      },
      {
        currentTeamId: teamId,
        status: teamId ? 'racing' : 'in_pit_row',
        updatedAt: new Date()
      }
    );
  }

  /**
   * Ensure pit rows don't contain non-apex karts when in apex mode
   */
  static async validatePitRowsForApexMode(sessionId: string): Promise<{ valid: boolean; issues: string[] }> {
    const issues: string[] = [];
    
    try {
      // Get all pit rows
      const pitRows = await Row.find({}).populate('currentKartId');
      
      // Get all apex karts for this session
      const apexKarts = await ApexKart.find({ 
        sessionId: new mongoose.Types.ObjectId(sessionId) 
      });
      
      const apexKartNumbers = new Set(apexKarts.map(k => k.kartNumber));
      
      for (const row of pitRows) {
        if (row.currentKartId) {
          const kart = row.currentKartId as any;
          
          // Check if this kart number exists in apex karts
          if (!apexKartNumbers.has(kart.number)) {
            issues.push(`Pit row ${row.rowNumber} contains kart #${kart.number} which is not an apex kart`);
          }
        }
      }
      
      return {
        valid: issues.length === 0,
        issues
      };
      
    } catch (error) {
      console.error('Error validating pit rows for apex mode:', error);
      return {
        valid: false,
        issues: [`Validation error: ${error instanceof Error ? error.message : 'Unknown error'}`]
      };
    }
  }
}
