.apex-select-container {
  display: flex;
  align-items: center;
  width: 100%;
  gap: 8px;
}

.apex-select-container ion-select {
  flex: 1;
}

.apex-select-container ion-button {
  flex-shrink: 0;
  margin: 0;
}

/* Ensure proper spacing for the controls */
.apex-data-controls ion-item {
  --padding-start: 16px;
  --padding-end: 16px;
}

.apex-data-controls ion-label h3 {
  margin-bottom: 4px;
}

.apex-data-controls ion-label p {
  margin-top: 0;
  color: var(--ion-color-medium);
}

/* Style for the session/team info items */
.apex-data-controls .info-item {
  --background: var(--ion-color-light);
  border-radius: 8px;
  margin: 8px 0;
}

.apex-data-controls .info-item ion-label p {
  line-height: 1.4;
}

/* Warning items */
.apex-data-controls .warning-item {
  --background: var(--ion-color-warning-tint);
  border-radius: 8px;
  margin: 8px 0;
}

.apex-data-controls .warning-item ion-label {
  color: var(--ion-color-warning-shade);
}

/* Loading state */
.apex-data-controls .loading-item {
  opacity: 0.6;
}

/* Toggle styling */
.apex-data-controls ion-toggle {
  --handle-background: var(--ion-color-primary);
  --track-background: var(--ion-color-light);
  --track-background-checked: var(--ion-color-primary-tint);
}

/* Select styling */
.apex-data-controls ion-select {
  --placeholder-color: var(--ion-color-medium);
  --color: var(--ion-color-dark);
}

/* Note styling */
.apex-data-controls ion-note {
  font-size: 0.8em;
  margin-top: 4px;
  display: block;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .apex-select-container {
    flex-direction: column;
    align-items: stretch;
  }
  
  .apex-select-container ion-button {
    align-self: flex-end;
    margin-top: 8px;
  }
}
