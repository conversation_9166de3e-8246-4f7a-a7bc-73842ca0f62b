import { useEffect, useRef, useCallback } from "react";

// --- Read URL from environment variable ---
// Provide a fallback for safety, though it should be set via .env or Render env vars.
const WEBSOCKET_URL = import.meta.env.VITE_WEBSOCKET_URL;
// --- End Change ---

// Define the expected shape of your WebSocket message
interface WebSocketMessage {
  event: string; // e.g., 'teamsUpdated', 'kartsUpdated', 'strategyUpdated', etc.
  payload?: unknown; // Optional data associated with the event, use unknown for safety
}

// Strategy-related events
export const STRATEGY_UPDATE_EVENT = 'strategyUpdated';
export const STRATEGY_CREATED_EVENT = 'strategyCreated';
export const STRATEGY_DELETED_EVENT = 'strategyDeleted';

// Define the type for the message handler callback
type MessageHandler = (message: WebSocketMessage) => void;

// Custom hook to manage WebSocket connection
function useWebSocket(messageHandler: MessageHandler | null) {
  const ws = useRef<WebSocket | null>(null);
  const handlerRef = useRef<MessageHandler | null>(messageHandler); // Ref to store the handler

  // Update the ref if the handler function changes (prevents stale closures)
  useEffect(() => {
    handlerRef.current = messageHandler;
  }, [messageHandler]);

  const connectWebSocket = useCallback(() => {
    // Prevent multiple connections
    if (ws.current && ws.current.readyState === WebSocket.OPEN) {
        console.log("[WS Handler] WebSocket already connected.");
        return;
    }
    if (ws.current && ws.current.readyState === WebSocket.CONNECTING) {
        console.log("[WS Handler] WebSocket connection already in progress.");
        return;
    }

    // --- Use the dynamic URL ---
    console.log(`[WS Handler] Using WebSocket URL: ${WEBSOCKET_URL}`);
    console.log(`[WS Handler] Attempting to connect WebSocket...`);
    ws.current = new WebSocket(WEBSOCKET_URL);
    // --- End Change ---

    ws.current.onopen = () => {
      console.log("[WS Handler] WebSocket Connected");
    };

    ws.current.onmessage = (event) => {
      try {
        // Ensure data is a string before parsing
        const dataString = typeof event.data === 'string' ? event.data : event.data.toString();
        const message: WebSocketMessage = JSON.parse(dataString); // Add type assertion if needed: as WebSocketMessage
        // console.log(`[WS Handler] Message Received:`, message); // Can be noisy, log conditionally if needed

        // Call the message handler passed to the hook if it exists
        if (handlerRef.current) {
          handlerRef.current(message);
        } else {
          console.warn("[WS Handler] Received message but no handler is attached.");
        }

      } catch (error) {
        console.error("[WS Handler] Failed to parse WebSocket message:", event.data, error);
      }
    };

    ws.current.onclose = (event) => {
      console.log("[WS Handler] WebSocket Disconnected:", event.code, event.reason);
      ws.current = null; // Clear ref on close
      // Optional: Implement reconnection logic here if desired
      // if (event.code !== 1000) { // Don't auto-reconnect on normal closure
      //   console.log("[WS Handler] Attempting to reconnect in 5 seconds...");
      //   setTimeout(connectWebSocket, 5000);
      // }
    };

    ws.current.onerror = (event) => {
      // Log the event itself which might contain more info than just 'error'
      console.error("[WS Handler] WebSocket Error:", event);
      // The onclose event will usually fire after an error.
    };
  }, []); // Empty dependency array: connectWebSocket function itself doesn't change

  useEffect(() => {
    let connectionAttemptTimeout: number | null = null;

    // Connect on mount only if a handler is provided
    if (messageHandler) {
        connectWebSocket();
        // Optional: Add a timeout to log if connection isn't established quickly
        connectionAttemptTimeout = setTimeout(() => {
            if (ws.current?.readyState !== WebSocket.OPEN) {
                console.warn("[WS Handler] WebSocket connection not established after 10 seconds.");
            }
        }, 10000); // 10 seconds
    } else {
        // If no handler is provided, ensure any existing connection is closed
        console.log("[WS Handler] No message handler provided, closing connection if open.");
        ws.current?.close(1000, "Handler removed"); // Use code 1000 for normal closure
    }

    // Cleanup function: Close WebSocket connection when component unmounts or handler becomes null
    return () => {
        if (connectionAttemptTimeout !== null) { // Check if it's not null before clearing
            clearTimeout(connectionAttemptTimeout);
        }
      if (ws.current) {
          console.log("[WS Handler] Cleaning up WebSocket connection...");
          // Remove listeners before closing to prevent errors during cleanup
          ws.current.onopen = null;
          ws.current.onmessage = null;
          ws.current.onclose = null;
          ws.current.onerror = null;
          // Close with a normal closure code
          ws.current.close(1000, "Component unmounting or handler removed");
          ws.current = null; // Clear the ref
      }
    };
    // Re-run effect if connectWebSocket or messageHandler changes
    // connectWebSocket is stable due to useCallback([])
  }, [connectWebSocket, messageHandler]);

  // The hook doesn't need to return anything for this specific use case,
  // but you could return the connection status or a manual send function if needed.
}

// --- Default export for easy importing in components ---
export default useWebSocket;
