// d:\Desktop\ManulilacRaceplanner\frontend\src\App.tsx
import React from 'react';
import { Browser<PERSON>outer, Route, Routes, Outlet } from "react-router-dom"; // Use BrowserRouter alias if preferred
import {
  IonApp,
  IonMenu,
  IonHeader,
  IonToolbar,
  IonTitle,
  IonContent,
  IonList,
  IonItem,
  IonLabel,
  IonSplitPane,
  IonPage,
  // IonMenuButton, // Removed as it wasn't used in the main content area
  // IonButtons, // Removed as it wasn't used
  IonIcon, // Keep IonIcon
  setupIonicReact // Keep setupIonicReact
} from "@ionic/react";
import { people, list, carOutline, analyticsOutline, logOutOutline, personCircleOutline, timerOutline } from "ionicons/icons"; // Keep icons

/* Import Ionic CSS */
import "@ionic/react/css/core.css";
import "@ionic/react/css/normalize.css";
import "@ionic/react/css/structure.css";
import "@ionic/react/css/typography.css";
import "@ionic/react/css/padding.css";
import "@ionic/react/css/float-elements.css";
import "@ionic/react/css/text-alignment.css";
import "@ionic/react/css/text-transformation.css";
import "@ionic/react/css/flex-utils.css";
import "@ionic/react/css/display.css";
// Consider if you need dark mode support explicitly enabled here
import "@ionic/react/css/palettes/dark.system.css";

/* Pages */
import PitRows from "./pages/PitRows/PitRows";
import Teams from "./pages/Teams";
import KartsStatusPage from "./pages/KartStatusPage"; // Corrected import name casing
import RaceStrategy from './pages/RaceStrategy/racestrategy';

import LogReplay from './pages/LogReplay/LogReplay';
import DatabaseMonitor from './pages/DatabaseMonitor/DatabaseMonitor';
import LiveTimingPage from './pages/LiveTimingPage';
import Login from './pages/Auth/Login';
import Register from './pages/Auth/Register';
import ProtectedRoute from './components/ProtectedRoute';
import { AuthProvider, useAuth } from './context/AuthContext';
import { DataProvider } from './context/DataContext';

// Call Ionic setup
setupIonicReact();

// Main app content with menu
const MainLayout = () => {
  const { logout, user } = useAuth();
  
  return (
    <IonSplitPane contentId="main-content">
      {/* Side Menu */}
      <IonMenu contentId="main-content">
        <IonHeader>
          <IonToolbar>
            <IonTitle>Menu</IonTitle>
          </IonToolbar>
        </IonHeader>
        <IonContent>
          <IonList>
            <IonItem button routerLink="/pitrows" routerDirection="root">
              <IonIcon icon={list} slot="start" aria-hidden="true" />
              <IonLabel>Pit Rows</IonLabel>
            </IonItem>
            <IonItem button routerLink="/teams" routerDirection="root">
              <IonIcon icon={people} slot="start" aria-hidden="true" />
              <IonLabel>Teams</IonLabel>
            </IonItem>
            <IonItem button routerLink="/karts" routerDirection="root">
              <IonIcon icon={carOutline} slot="start" aria-hidden="true" />
              <IonLabel>Karts</IonLabel>
            </IonItem>
            <IonItem button routerLink="/racestrategy" routerDirection="root">
              <IonIcon icon={analyticsOutline} slot="start" aria-hidden="true" />
              <IonLabel>Strategy</IonLabel>
            </IonItem>
            <IonItem button routerLink="/live-timing" routerDirection="root">
              <IonIcon icon={timerOutline} slot="start" aria-hidden="true" />
              <IonLabel>Live Timing</IonLabel>
            </IonItem>
            <IonItem button routerLink="/log-replay" routerDirection="root">
              <IonIcon icon={analyticsOutline} slot="start" aria-hidden="true" />
              <IonLabel>Log Replay</IonLabel>
            </IonItem>
            <IonItem button routerLink="/database-monitor" routerDirection="root">
              <IonIcon icon={analyticsOutline} slot="start" aria-hidden="true" />
              <IonLabel>Database Monitor</IonLabel>
            </IonItem>
          </IonList>
        </IonContent>
        
        {/* User Footer */}
        {user && (
          <div className="user-footer">
            <IonItem lines="none" className="user-info">
              <IonIcon icon={personCircleOutline} slot="start" size="large" />
              <IonLabel>
                <h3>{user.name}</h3>
                <p>{user.email}</p>
              </IonLabel>
            </IonItem>
            <IonItem button onClick={logout} lines="none" className="logout-button">
              <IonIcon icon={logOutOutline} slot="start" aria-hidden="true" />
              <IonLabel>Logout</IonLabel>
            </IonItem>
          </div>
        )}
      </IonMenu>

      {/* Main Content Area */}
      <IonPage id="main-content">
        <IonContent className="ion-padding">
          <Outlet />
        </IonContent>
      </IonPage>
    </IonSplitPane>
  );
};

// App with routing and auth provider
function AppWithRouting() {
  return (
    <BrowserRouter>
      <AuthProvider>
        <DataProvider>
          <Routes>
            <Route path="/login" element={<Login />} />
            <Route path="/register" element={<Register />} />

            {/* Protected routes */}
            <Route element={<ProtectedRoute />}>
              <Route path="/" element={<MainLayout />}>
                <Route index element={<PitRows />} />
                <Route path="/pitrows" element={<PitRows />} />
                <Route path="/teams" element={<Teams />} />
                <Route path="/karts" element={<KartsStatusPage />} />
                <Route path="/racestrategy" element={<RaceStrategy/>} />
                <Route path="/live-timing" element={<LiveTimingPage/>} />
                <Route path="/log-replay" element={<LogReplay/>} />
                <Route path="/database-monitor" element={<DatabaseMonitor/>} />
                <Route path="*" element={<PitRows />} />
              </Route>
            </Route>
          </Routes>
        </DataProvider>
      </AuthProvider>
    </BrowserRouter>
  );
}

// Main App component
function App() {
  return (
    <IonApp>
      <AppWithRouting />
    </IonApp>
  );
}

export default App;
