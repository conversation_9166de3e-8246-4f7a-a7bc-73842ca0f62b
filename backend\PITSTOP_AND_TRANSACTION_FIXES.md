# Pitstop Detection and Transaction Handling Fixes

## Overview

This document outlines the fixes implemented to address pitstop recognition issues and database transaction handling in the Apex parser system.

## Issues Fixed

### 1. Pitstop Recognition Problems

**Problem**: Pitstops were not being properly detected and recorded in the database.

**Root Cause**: 
- The `handlePitUpdate` method only called `handleStatusUpdate` instead of creating actual pitstop records
- No proper pitstop creation logic for `pit` field type with "IN"/"OUT" values
- Missing lap number tracking for pitstop records

**Solution**:
- Implemented proper pitstop creation in `handlePitUpdate` method
- Added `handlePitEntry` method to create new pitstop records
- Added `handlePitExit` method to complete existing pitstop records
- Proper lap number calculation from latest lap data

### 2. Database Transaction Handling

**Problem**: Concurrent websocket messages could cause database conflicts during grid creation and message processing.

**Root Cause**:
- No queuing mechanism for concurrent websocket messages
- Database operations could overlap during grid creation and message processing
- No transaction safety for database operations

**Solution**:
- Added message queuing system with `messageQueue` array
- Implemented `isProcessingGrid` and `isProcessingMessage` flags
- Added `processQueuedMessages` method to handle queued messages after operations complete
- Messages are queued when parser is busy and processed sequentially

### 3. Total Laps Calculation

**Problem**: `totalLaps` field was stored in database but should be calculated by frontend.

**Root Cause**:
- Redundant data storage in multiple models
- Frontend should calculate this dynamically for better performance

**Solution**:
- Removed `totalLaps` field from `IApexTeam`, `IApexKart`, and `IApexLiveData` interfaces
- Removed `totalLaps` from corresponding schemas
- Updated API routes to calculate `totalLaps` dynamically when needed
- Frontend can now calculate total laps by counting lap records

## Implementation Details

### Pitstop Detection Flow

```typescript
// 1. Websocket message received: r12345c9|pit|IN
// 2. Parser identifies field type as 'pit'
// 3. handlePitUpdate called with pitStatus 'IN'
// 4. handlePitEntry creates new ApexPitStop record
// 5. Kart status updated to 'in_pit_row'

// Later: r12345c9|pit|OUT
// 6. handlePitUpdate called with pitStatus 'OUT'
// 7. handlePitExit finds active pitstop and adds exit time
// 8. Pit duration calculated and stored
// 9. Kart status updated to 'on_track'
```

### Transaction Handling Flow

```typescript
// 1. Message received while parser is busy
// 2. Message added to messageQueue
// 3. Current operation completes
// 4. processQueuedMessages processes all queued messages
// 5. Each message processed sequentially to avoid conflicts
```

### Database Schema Changes

**Removed Fields**:
- `IApexTeam.totalLaps`
- `IApexKart.totalLaps`
- `IApexLiveData.totalLaps`

**Added Fields**:
- `ApexParserSimple.messageQueue`
- `ApexParserSimple.isProcessingGrid`
- `ApexParserSimple.isProcessingMessage`

## API Changes

### Kart Endpoint
- `/api/apex/sessions/:sessionId/karts` now calculates `totalLaps` dynamically
- Uses `ApexLap.countDocuments()` to get lap count per kart

### Pitstop Endpoint
- `/api/apex/sessions/:sessionId/pitstops` returns proper pitstop records
- Includes `pitInTime`, `pitOutTime`, and `pitDuration` fields

## Testing

### Test Script
A comprehensive test script has been created: `backend/scripts/test-pitstop-detection.ts`

**Test Cases**:
1. Session creation with grid data
2. Pit entry message processing
3. Pitstop record creation verification
4. Pit exit message processing
5. Pitstop completion verification
6. Rapid message handling (transaction safety)

### Running Tests
```bash
cd backend
npx ts-node scripts/test-pitstop-detection.ts
```

## Performance Improvements

### Database Queries
- Removed redundant `totalLaps` storage
- Dynamic calculation only when needed
- Better indexing for pitstop queries

### Memory Usage
- Message queuing prevents memory buildup during high-traffic periods
- Sequential processing ensures controlled resource usage

### Concurrency
- Proper transaction handling prevents database conflicts
- Message queuing ensures data consistency

## Configuration

### Parser Configuration
```typescript
const parser = new ApexParserSimple({
  enableLogging: true  // Enable detailed logging for debugging
});
```

### Message Queue Monitoring
The parser now logs queue status:
- `⏳ Message queued (X in queue)` - Message added to queue
- `📤 Processing queued message (X remaining)` - Processing queued message

## Error Handling

### Pitstop Errors
- Missing competitor mapping logged as warnings
- Invalid pitstop states handled gracefully
- Database errors don't crash the parser

### Transaction Errors
- Processing flags reset on errors
- Queued messages still processed after errors
- Error logging includes original message content

## Future Improvements

### Potential Enhancements
1. **Pitstop Analytics**: Add pitstop statistics and trends
2. **Advanced Queuing**: Priority-based message queuing
3. **Performance Metrics**: Track processing times and queue lengths
4. **Database Transactions**: Use MongoDB transactions for complex operations

### Monitoring
1. **Queue Length Monitoring**: Alert when queue gets too long
2. **Processing Time Tracking**: Monitor message processing performance
3. **Error Rate Monitoring**: Track parsing error rates

## Conclusion

These fixes address the core issues with pitstop detection and database transaction handling, providing:

- ✅ Reliable pitstop detection and recording
- ✅ Safe concurrent message processing
- ✅ Optimized database schema
- ✅ Comprehensive testing
- ✅ Better error handling and logging

The system now properly handles high-frequency websocket messages while maintaining data consistency and providing accurate pitstop tracking.
