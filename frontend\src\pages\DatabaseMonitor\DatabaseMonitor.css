/* Database Monitor Styles */

.database-monitor-container {
  padding: 16px;
  max-width: 1200px;
  margin: 0 auto;
}

.status-badge {
  margin-right: 16px;
  font-size: 0.8rem;
  padding: 4px 8px;
}

/* Card spacing */
.database-monitor-container ion-card {
  margin-bottom: 16px;
}

.database-monitor-container ion-card:last-child {
  margin-bottom: 0;
}

/* Statistics styling */
.stat-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: var(--ion-color-light);
  border-radius: 8px;
  text-align: left;
}

.stat-item ion-icon {
  font-size: 24px;
  color: var(--ion-color-primary);
  min-width: 24px;
}

.stat-item h3 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--ion-color-dark);
}

.stat-item p {
  margin: 0;
  font-size: 0.9rem;
  color: var(--ion-color-medium);
}

/* Parsing status */
.parsing-status {
  margin-bottom: 16px;
  text-align: center;
}

.parsing-status ion-badge {
  font-size: 1rem;
  padding: 8px 16px;
}

.last-activity {
  margin-top: 16px;
  padding: 12px;
  background: var(--ion-color-light-tint);
  border-radius: 8px;
}

.last-activity h4 {
  margin: 0 0 8px 0;
  color: var(--ion-color-dark);
}

.last-activity p {
  margin: 0;
  color: var(--ion-color-medium);
}

.last-activity strong {
  color: var(--ion-color-primary);
  font-size: 1.1rem;
}

/* Session details */
.session-details {
  margin-top: 16px;
  padding: 16px;
  background: var(--ion-color-light-tint);
  border-radius: 8px;
}

.session-details h4 {
  margin: 0 0 8px 0;
  color: var(--ion-color-dark);
  font-size: 1.2rem;
}

.session-details p {
  margin: 4px 0;
  color: var(--ion-color-medium);
  font-size: 0.9rem;
}

.detail-stat {
  text-align: center;
  padding: 12px;
  background: var(--ion-color-light);
  border-radius: 6px;
}

.detail-stat strong {
  display: block;
  font-size: 1.4rem;
  color: var(--ion-color-primary);
  margin-bottom: 4px;
}

.detail-stat span {
  font-size: 0.8rem;
  color: var(--ion-color-medium);
}

/* Timestamps */
.timestamp {
  font-size: 0.8rem;
  color: var(--ion-color-medium);
  font-family: monospace;
}

.database-monitor-container ion-card-title .timestamp {
  font-size: 0.7rem;
  font-weight: normal;
  margin-left: auto;
}

/* List styling */
.database-monitor-container ion-list {
  padding: 0;
}

.database-monitor-container ion-item {
  --padding-start: 16px;
  --padding-end: 16px;
  --border-color: transparent;
  margin-bottom: 4px;
}

.database-monitor-container ion-item:last-child {
  margin-bottom: 0;
}

.database-monitor-container ion-item h3 {
  margin: 0 0 4px 0;
  font-weight: 600;
  color: var(--ion-color-dark);
}

.database-monitor-container ion-item p {
  margin: 2px 0;
  font-size: 0.9rem;
  color: var(--ion-color-medium);
}

/* Badge styling */
.database-monitor-container ion-badge {
  font-size: 0.8rem;
  padding: 4px 8px;
}

/* Card header improvements */
.database-monitor-container ion-card-header {
  padding-bottom: 8px;
}

.database-monitor-container ion-card-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 1.2rem;
  font-weight: 600;
}

.database-monitor-container ion-card-title ion-icon {
  font-size: 1.3rem;
  color: var(--ion-color-primary);
}

/* Grid styling */
.database-monitor-container ion-grid {
  padding: 0;
}

.database-monitor-container ion-row {
  margin-bottom: 16px;
}

.database-monitor-container ion-row:last-child {
  margin-bottom: 0;
}

/* Button styling */
.database-monitor-container ion-button {
  --border-radius: 8px;
}

/* Select styling */
.database-monitor-container ion-select {
  --padding-start: 12px;
  --padding-end: 12px;
  border: 1px solid var(--ion-color-light-shade);
  border-radius: 8px;
}

/* Item improvements */
.database-monitor-container ion-item {
  --background: transparent;
  margin-bottom: 8px;
}

.database-monitor-container ion-item:last-child {
  margin-bottom: 0;
}

/* Loading states */
.database-monitor-container .loading-placeholder {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40px;
  color: var(--ion-color-medium);
}

/* Empty state */
.database-monitor-container .empty-state {
  text-align: center;
  padding: 40px 20px;
  color: var(--ion-color-medium);
}

.database-monitor-container .empty-state ion-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
}

/* Status indicators */
.database-monitor-container .status-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 0.9rem;
  font-weight: 500;
}

.database-monitor-container .status-indicator.active {
  background-color: var(--ion-color-success-tint);
  color: var(--ion-color-success-shade);
}

.database-monitor-container .status-indicator.idle {
  background-color: var(--ion-color-medium-tint);
  color: var(--ion-color-medium-shade);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .database-monitor-container {
    padding: 12px;
  }
  
  .stat-item {
    padding: 12px;
    gap: 8px;
  }
  
  .stat-item h3 {
    font-size: 1.3rem;
  }
  
  .stat-item ion-icon {
    font-size: 20px;
  }
  
  .detail-stat strong {
    font-size: 1.2rem;
  }
  
  .status-badge {
    margin-right: 8px;
    font-size: 0.7rem;
    padding: 2px 6px;
  }
}

@media (max-width: 480px) {
  .database-monitor-container {
    padding: 8px;
  }
  
  .session-details,
  .last-activity {
    padding: 12px;
  }
  
  .database-monitor-container ion-card-title .timestamp {
    display: none; /* Hide timestamp on very small screens */
  }
}

/* Animation for live updates */
@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
  100% {
    opacity: 1;
  }
}

.database-monitor-container .live-indicator {
  animation: pulse 2s infinite;
}

/* Real-time update highlighting */
.database-monitor-container .recent-update {
  background: var(--ion-color-success-tint);
  border-left: 4px solid var(--ion-color-success);
  transition: background-color 0.3s ease;
}

.database-monitor-container .recent-update.fade-out {
  background: transparent;
}
