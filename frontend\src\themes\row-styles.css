/* src/themes/row-styles.css */

/* Define row colors as variables (optional but recommended) */
:root {
  --row-color-red: #eb445a;
  --row-color-blue: #3880ff;
  --row-color-green: #2dd36f;
  --row-color-yellow: #ffc409;
  --row-color-purple: #9200ce;
  --row-color-grey: #92949c;
  --row-color-black: #222428;
  --row-color-white: #ffffff;
}

/* --- Row Color Indicator Styles --- */

.row-color-indicator {
    /* Base style for the indicator rectangle */
    width: 16px;
    height: 16px;
    border-radius: 3px;
    border: 1px solid rgba(0, 0, 0, 0.15); /* Subtle border */
    flex-shrink: 0; /* Prevent shrinking in flex layouts */
    transition: transform 0.15s ease-out, box-shadow 0.15s ease-out; /* Keep transition if hover effect is desired */
  }
  
  /* --- Define Specific Row Colors --- */
  /* Add classes for all the possible colors you store in your Row objects. */
  /* Use descriptive names. */
  
  /* Use variables for colors */
  .row-bg-red { background-color: var(--row-color-red); }
  .row-bg-blue { background-color: var(--row-color-blue); }
  .row-bg-green { background-color: var(--row-color-green); }
  .row-bg-yellow { background-color: var(--row-color-yellow); }
  .row-bg-purple { background-color: var(--row-color-purple); }
  .row-bg-grey { background-color: var(--row-color-grey); }
  .row-bg-black { background-color: var(--row-color-black); }
  .row-bg-white { background-color: var(--row-color-white); }

  /* Add more classes as needed for your specific row colors */
  /* .row-bg-orange { background-color: orange; } */
  /* .row-bg-pink { background-color: pink; } */
  
  /* --- Optional: Hover effect (if using clickable indicators) --- */
  .location-col.clickable:hover .row-color-indicator {
      transform: scale(1.1);
      box-shadow: 0 0 4px rgba(0,0,0,0.2);
  }