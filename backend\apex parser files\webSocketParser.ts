import { useEffect } from "react";
import { useDataContext } from "../contexts/DataContext";
import { useDriverProperties } from "./useDriverProperties";
import { parseGridData } from "./parseGrid";
import { Session } from "./types";
import { useRaceManager } from "./useRaceManager";

export const useMessageParser = () => {
  const {
    sessions,
    selectedRace,
    setSessions,
    selectedSession,
    setSelectedSession,
    message,
  } = useDataContext();

  const { updateProperty } = useDriverProperties();
  const { addLap, addKart, updateKart, enterPit, exitPit } = useRaceManager();

  useEffect(() => {
    if (message) parseMessage(message);
  }, [message]);

  const sessionFields = new Set([
    "best", "css", "comments", "effects", "init", "title1", "title2", "dyn1", "light", "wth1", "wth2", "wth3", "track", "com", "grid", "msg"
  ]);

  const parseMessage = (message: Record<string, { type: string; value: string }> | null) => {
    if (!message) return;

    const { init, grid, ...rest } = message;

    if (selectedRace && init) {
      createOrUpdateSession({ ...rest, grid });
    } else {
      processMessage(rest);
    }
  };

  const createOrUpdateSession = (sessionData: Record<string, any>) => {
    if (!selectedRace) {
      console.warn("No race selected. Cannot create a new session.");
      return;
    }

    const newSession: Session = {
      id: Date.now(),
      raceId: selectedRace.id,
      ...sessionData,
      grid: parseGridData(sessionData.grid?.value),
    };

    console.log("Session created or updated:", newSession);

    if (!sessions.some((s) => s.title2?.value === newSession.title2?.value)) {
      setSessions((prev) => [...prev, newSession]);
    }

    setSelectedSession(newSession);
  };

  const processMessage = (fields: Record<string, { type: string; value: string }>) => {
    Object.entries(fields).forEach(([key, { type, value }]) => {
      if (sessionFields.has(key)) {
        handleSessionField(key, { type, value });
      } else if (key.startsWith("r")) {
        handleDriverMessage(key, { type, value });
      } else {
        console.warn(`Unknown field: ${key}`);
      }
    });
  };

  // Handle session field updates
  const handleSessionField = (key: string, { type, value }: { type: string; value: string }) => {
    if (!selectedSession) {
      console.warn(`No active session to update for field: ${key}`);
      return;
    }

    const updatedSession = {
      ...selectedSession,
      [key]: { type, value },
    };

    setSelectedSession(updatedSession);
    console.log(`Session field updated: ${key} = ${type} ${value}`);
  };

  const handleDriverMessage = (
    key: string,
    { type, value }: { type: string; value: string }
  ) => {
    const [driverId, columnId] = key.split("c");
    const headerType = selectedSession?.grid?.header_types[`c${columnId}`];
    // Switch statement to handle different types of messages based on columnId
    switch (headerType) {
      // Nationality (nat)
      case "nat":
        console.log(`Driver ${driverId}: Nationality ${value}`);
        // Handle nationality update logic, e.g., updateDriverNationality(driverId, value);
        break;
  
      // Status (sta)
      case "sta":
        console.log(`Driver ${driverId}: Status ${value}`);
        // Handle status updates (PITOUT, ON TRACK, etc.)
        // For example: updateDriverStatus(driverId, value);
        break;
  
      // Classification (rk)
      case "rk":
        console.log(`Driver ${driverId}: Classification ${value}`);
        // Handle driver's classification (position)
        // For example: updateClassification(driverId, value);
        break;
  
      // Race Number (no)
      case "no":
        console.log(`Driver ${driverId}: Race Number ${value}`);
        // Handle race number update
        // For example: updateRaceNumber(driverId, value);
        break;
  
      // Group (grp)
      case "grp":
        console.log(`Driver ${driverId}: Group ${value}`);
        // Handle group-specific logic
        // For example: updateDriverGroup(driverId, value);
        break;
  
      // Driver Info (dr)
      case "dr":
        console.log(`Driver ${driverId}: Driver Info ${value}`);
        // Handle driver/team-specific logic
        // For example: updateDriverInfo(driverId, value);
        break;
  
      // Sector 1 (s1)
      case "s1":
        console.log(`Driver ${driverId}: Sector 1 Time ${value}`);
        // Handle sector 1 time
        // For example: updateSectorTime(driverId, "s1", value);
        break;
  
      // Sector 2 (s2)
      case "s2":
        console.log(`Driver ${driverId}: Sector 2 Time ${value}`);
        // Handle sector 2 time
        // For example: updateSectorTime(driverId, "s2", value);
        break;
  
      // Last Lap (llp)
      case "llp":
        if (type === "TI" || type === "TB") {
          console.log(`Driver ${driverId}: Last Lap ${value}`);
          // Handle last lap information
          // For example: updateLastLap(driverId, value);
        }
        break;
  
      // Best Lap (blp)
      case "blp":
        if (type === "IB") {
          console.log(`Driver ${driverId}: Best Lap ${value}`);
          // Handle best lap logic
          // For example: updateBestLap(driverId, value);
        }
        break;
  
      // Interval (int)
      case "int":
        console.log(`Driver ${driverId}: Interval to driver ahead ${value}`);
        // Handle interval to driver ahead
        // For example: updateInterval(driverId, value);
        break;
  
      // Gap (gap)
      case "gap":
        console.log(`Driver ${driverId}: Gap to leader ${value}`);
        // Handle gap to leader
        // For example: updateGap(driverId, value);
        break;
  
      // Total Laps (tlp)
      case "tlp":
        console.log(`Driver ${driverId}: Total Laps ${value}`);
        
        // Handle total laps completed
        // For example: updateTotalLaps(driverId, value);
        break;
  
      // Driver Timer (otr)
      case "otr":
        if (type === "TO" || type === "IN") {
          console.log(`Driver ${driverId}: Driver Timer ${value}`);
          // Handle driver timer (Pit or Track)
          // For example: updateDriverTimer(driverId, value);
        }
        break;
  
      // Pit Status (pit)
      case "pit":
        console.log(`Driver ${driverId}: Pit Status ${value}`);
        if (value === "IN") {
          // Handle when a driver enters the pit
          // For example: enterPit(driverId);
        } else if (value === "OUT") {
          // Handle when a driver exits the pit
          // For example: exitPit(driverId);
        }
        break;
  
      default:
        console.warn(`Unhandled driver message for ${key}, ${type}, ${value}`);
        break;
    }
  };
  

  return { parseMessage };
};

